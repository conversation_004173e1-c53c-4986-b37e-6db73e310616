# -*- coding: utf-8 -*-
"""hotel_capture_raw_json.py – 流式写入 + 起始偏移 + 覆盖选项 + 错误重试 (2025‑07‑22)

更新要点
--------
* **完成一条→立即写入**：JSONL 流式落盘仍然保持。
* **起始偏移**：`has_website_start` / `no_website_start` 参数仍支持。
* **新增 `overwrite` 参数**
  - `overwrite=True`（默认）：脚本启动时**清空**输出文件后再写入。
  - `overwrite=False`：若文件已存在则**追加写入**，否则创建新文件。
* **新增 `retry_failed_requests` 方法**：从JSONL文件中读取失败记录并重试。
"""

from __future__ import annotations

import os
import json
import asyncio
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
from pathlib import Path

import aiohttp
import pandas as pd

# ---------------------------- 日志配置 -----------------------------------
LOGGER_NAME = "HotelCapture"
logger = logging.getLogger(LOGGER_NAME)
logger.setLevel(logging.INFO)
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")

os.makedirs("logs", exist_ok=True)
ch = logging.StreamHandler()
ch.setFormatter(formatter)
fh = RotatingFileHandler(
    "logs/hotel_capture.log", maxBytes=10 * 1024 * 1024,
    backupCount=7, encoding="utf-8")
fh.setFormatter(formatter)
logger.addHandler(ch)
logger.addHandler(fh)


# ------------------------------------------------------------------------
#  工具：安全写 JSONL（带异步锁）
# ------------------------------------------------------------------------
async def _append_jsonl(record: dict, outfile: str, lock: asyncio.Lock) -> None:
    """将单条记录追加到 JSONL 文件。"""
    line = json.dumps(record, ensure_ascii=False)
    async with lock:
        with open(outfile, "a", encoding="utf-8") as f:
            f.write(line + "\n")


# ------------------------------------------------------------------------
#  单酒店异步请求
# ------------------------------------------------------------------------
async def async_send_request(session: aiohttp.ClientSession, row: pd.Series, region: str,
                             api_url: str, base_dir: str | None, max_retries: int,
                             outfile: str, lock: asyncio.Lock, upload_to_db: bool = False,
                             db_table: str = "") -> None:
    hotel_name = row['英文名称']
    hotel_url = row.get('官网链接', '')
    has_orig = bool(pd.notna(hotel_url) and str(hotel_url).strip().lower() not in ("", "none", "nan"))

    # 构建基础payload
    payload: dict = {"hotel_name": hotel_name, "hotel_region": region}

    # 添加酒店完整信息
    hotel_info = {
        "id": str(row.get('母酒店id', '')),
        "english_name": str(row.get('英文名称', '')),
        "local_name": str(row.get('当地语言名称', '')),
        "country": str(row.get('国家', '')),
        "province": str(row.get('省份', '')),
        "city": str(row.get('城市', ''))
    }
    payload["hotel_info"] = hotel_info

    if base_dir:
        payload["base_dir"] = base_dir
    if has_orig:
        payload["hotel_url"] = hotel_url

    # 添加数据库上传参数
    if upload_to_db:
        payload["upload_to_db"] = upload_to_db
        payload["db_table"] = db_table

    for attempt in range(max_retries + 1):
        try:
            if attempt:
                logger.warning(f"[重试 {attempt}/{max_retries}] {hotel_name}")
            async with session.post(api_url, json=payload, ssl=False,
                                    timeout=aiohttp.ClientTimeout(total=300)) as resp:
                if resp.status != 200:
                    raise RuntimeError(f"HTTP {resp.status}")
                resp_json = await resp.json(content_type=None)
                logger.info(f"[成功] {hotel_name}")
                await _append_jsonl({
                    "hotel_name": hotel_name,
                    "success": True,
                    "response": resp_json,
                    "timestamp": datetime.utcnow().isoformat()
                }, outfile, lock)
                return
        except Exception as exc:
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt)
            else:
                logger.error(f"[失败] {hotel_name} | {exc}")
                await _append_jsonl({
                    "hotel_name": hotel_name,
                    "success": False,
                    "error": str(exc),
                    "timestamp": datetime.utcnow().isoformat()
                }, outfile, lock)
                return


# ------------------------------------------------------------------------
#  多酒店异步批量请求
# ------------------------------------------------------------------------
async def async_send_hotel_requests(df: pd.DataFrame, region: str, api_url: str,
                                    base_dir: str | None, outfile: str,
                                    max_concurrent: int = 5, max_retries: int = 3,
                                    upload_to_db: bool = False, db_table: str = ""):
    connector = aiohttp.TCPConnector(limit_per_host=max_concurrent, ssl=False)
    sem = asyncio.Semaphore(max_concurrent)
    lock = asyncio.Lock()

    async def worker(session, r):
        async with sem:
            await async_send_request(session, r, region, api_url, base_dir,
                                     max_retries, outfile, lock, upload_to_db, db_table)

    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = [worker(session, row) for _, row in df.iterrows()]
        await asyncio.gather(*tasks)


# ------------------------------------------------------------------------
#  错误重试功能
# ------------------------------------------------------------------------
def read_failed_records(jsonl_file: str) -> list[dict]:
    """从JSONL文件中读取失败的记录"""
    failed_records = []
    if not os.path.exists(jsonl_file):
        logger.error(f"文件不存在：{jsonl_file}")
        return failed_records
    
    try:
        with open(jsonl_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue
                try:
                    record = json.loads(line)
                    # 检查两种失败情况：
                    # 1. 请求层面失败：success = false
                    # 2. 业务逻辑失败：success = true 但 response.success = false
                    is_request_failed = record.get('success') is False
                    is_business_failed = (record.get('success') is True and 
                                        record.get('response', {}).get('success') is False)
                    
                    if is_request_failed or is_business_failed:
                        # 标记失败类型，便于后续处理
                        if is_request_failed:
                            record['failure_type'] = 'request_failed'
                        elif is_business_failed:
                            record['failure_type'] = 'business_failed'
                            record['business_error'] = record.get('response', {}).get('error', 'Unknown business error')
                        
                        failed_records.append(record)
                        
                except json.JSONDecodeError as e:
                    logger.warning(f"第{line_num}行JSON解析失败：{e}")
                    continue
        
        # 统计失败类型
        request_failed_count = len([r for r in failed_records if r.get('failure_type') == 'request_failed'])
        business_failed_count = len([r for r in failed_records if r.get('failure_type') == 'business_failed'])
        
        logger.info(f"从 {jsonl_file} 中读取到 {len(failed_records)} 条失败记录:")
        logger.info(f"  - 请求失败: {request_failed_count} 条")
        logger.info(f"  - 业务失败: {business_failed_count} 条")
        
        return failed_records
    
    except Exception as e:
        logger.error(f"读取文件 {jsonl_file} 失败：{e}")
        return failed_records


async def retry_failed_request(session: aiohttp.ClientSession, failed_record: dict,
                               region: str, api_url: str, base_dir: str | None,
                               max_retries: int, outfile: str, lock: asyncio.Lock,
                               upload_to_db: bool = False, db_table: str = "") -> None:
    """重试单个失败的请求"""
    hotel_name = failed_record.get('hotel_name', 'Unknown')
    failure_type = failed_record.get('failure_type', 'unknown')
    
    # 获取原始错误信息
    if failure_type == 'request_failed':
        original_error = failed_record.get('error', 'Unknown request error')
    elif failure_type == 'business_failed':
        original_error = failed_record.get('business_error', 'Unknown business error')
    else:
        original_error = 'Unknown error type'
    
    logger.info(f"[开始重试] {hotel_name} - 失败类型: {failure_type} - 原始错误: {original_error}")
    
    # 从失败记录中提取原始参数（如果有的话）
    original_response = failed_record.get('response', {})
    
    # 构建重试payload - 这里需要重新构建完整的payload
    # 由于原始记录可能不包含完整的酒店信息，我们需要基础信息
    payload: dict = {
        "hotel_name": hotel_name, 
        "hotel_region": region
    }
    
    # 尝试从失败记录中恢复更多信息
    if 'hotel_info' in original_response:
        payload["hotel_info"] = original_response['hotel_info']
    else:
        # 如果没有完整信息，提供基础结构
        payload["hotel_info"] = {
            "id": "",
            "english_name": hotel_name,
            "local_name": "",
            "country": "",
            "province": "",
            "city": ""
        }
    
    if base_dir:
        payload["base_dir"] = base_dir
        
    # 添加数据库上传参数
    if upload_to_db:
        payload["upload_to_db"] = upload_to_db
        payload["db_table"] = db_table

    for attempt in range(max_retries + 1):
        try:
            if attempt:
                logger.warning(f"[重试 {attempt}/{max_retries}] {hotel_name} - {failure_type}")
            
            async with session.post(api_url, json=payload, ssl=False,
                                    timeout=aiohttp.ClientTimeout(total=300)) as resp:
                if resp.status != 200:
                    raise RuntimeError(f"HTTP {resp.status}")
                
                resp_json = await resp.json(content_type=None)
                
                # 检查业务层面是否成功
                business_success = resp_json.get('success', True)
                if not business_success:
                    business_error = resp_json.get('error', 'Unknown business error')
                    raise RuntimeError(f"Business logic failed: {business_error}")
                
                logger.info(f"[重试成功] {hotel_name} - 原失败类型: {failure_type}")
                
                await _append_jsonl({
                    "hotel_name": hotel_name,
                    "success": True,
                    "response": resp_json,
                    "timestamp": datetime.utcnow().isoformat(),
                    "retry_attempt": True,
                    "original_failure_type": failure_type,
                    "original_error": original_error
                }, outfile, lock)
                return
                
        except Exception as exc:
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt)
            else:
                logger.error(f"[重试最终失败] {hotel_name} - {failure_type} | {exc}")
                await _append_jsonl({
                    "hotel_name": hotel_name,
                    "success": False,
                    "error": str(exc),
                    "timestamp": datetime.utcnow().isoformat(),
                    "retry_attempt": True,
                    "retry_failed": True,
                    "original_failure_type": failure_type,
                    "original_error": original_error
                }, outfile, lock)
                return


async def async_retry_failed_requests(failed_records: list[dict], region: str, 
                                      api_url: str, base_dir: str | None, outfile: str,
                                      max_concurrent: int = 5, max_retries: int = 3,
                                      upload_to_db: bool = False, db_table: str = ""):
    """异步重试所有失败的请求"""
    if not failed_records:
        logger.info("没有失败记录需要重试")
        return
        
    connector = aiohttp.TCPConnector(limit_per_host=max_concurrent, ssl=False)
    sem = asyncio.Semaphore(max_concurrent)
    lock = asyncio.Lock()

    async def worker(session, failed_record):
        async with sem:
            await retry_failed_request(session, failed_record, region, api_url, 
                                       base_dir, max_retries, outfile, lock, 
                                       upload_to_db, db_table)

    async with aiohttp.ClientSession(connector=connector) as session:
        tasks = [worker(session, record) for record in failed_records]
        await asyncio.gather(*tasks)


def retry_failed_requests(input_jsonl_file: str, output_file: str, *,
                          region: str = "Japan", base_dir: str | None = None,
                          max_concurrent: int = 5, max_retries: int = 3,
                          api_url: str = "http://localhost:8080/api/capture_hotel_complete",
                          upload_to_db: bool = False, db_table: str = ""):
    """
    重试JSONL文件中的失败记录
    
    Args:
        input_jsonl_file: 包含失败记录的JSONL文件路径
        output_file: 重试结果输出文件路径
        region: 酒店地区
        base_dir: 基础目录
        max_concurrent: 最大并发数
        max_retries: 最大重试次数
        api_url: API接口地址
        upload_to_db: 是否上传到数据库
        db_table: 数据库表名
    """
    logger.info(f"开始重试失败请求，输入文件：{input_jsonl_file}")
    
    # 读取失败记录
    failed_records = read_failed_records(input_jsonl_file)
    
    if not failed_records:
        logger.info("没有找到失败记录，无需重试")
        return
    
    # 清空输出文件
    Path(output_file).write_text("")
    logger.info(f"重试结果将写入 → {output_file}")
    
    # 执行重试
    asyncio.run(async_retry_failed_requests(
        failed_records, region, api_url, base_dir, output_file,
        max_concurrent=max_concurrent, max_retries=max_retries,
        upload_to_db=upload_to_db, db_table=db_table))
    
    logger.info(f"重试任务完成，共处理 {len(failed_records)} 条失败记录")


# ------------------------------------------------------------------------
#  主函数
# ------------------------------------------------------------------------

def main(input_file: str, output_file: str, *,
         has_website_start: int = 0, has_website_num: int = 5,
         no_website_start: int = 0, no_website_num: int = 5,
         hotel_name: str | None = None, hotel_url: str | None = None,
         base_dir: str | None = None, region: str = "Japan",
         max_concurrent: int = 5, max_retries: int = 3,
         api_url: str = "http://localhost:8080/api/capture_hotel_complete",
         overwrite: bool = True, upload_to_db: bool = False, db_table: str = ""):
    logger.info(f"读取 Excel：{input_file}")
    # 扩展读取字段，支持更完整的酒店信息
    try:
        df = pd.read_excel(input_file,
                           usecols=['母酒店id', '英文名称', '当地语言名称', '国家', '省份', '城市', '官网链接'])
    except ValueError:
        # 向后兼容：如果新字段不存在，使用原有字段
        logger.warning("Excel文件缺少部分字段，使用基础字段读取")
        df = pd.read_excel(input_file, usecols=['母酒店id', '英文名称', '官网链接'])
        # 添加缺失字段的默认值
        for col in ['当地语言名称', '国家', '省份', '城市']:
            if col not in df.columns:
                df[col] = ''

    # 筛选目标行
    if hotel_name or hotel_url:
        sub = df.copy()
        if hotel_name:
            sub = sub[sub['英文名称'] == hotel_name]
        if hotel_url:
            sub = sub[sub['官网链接'] == hotel_url]
        if sub.empty:
            logger.error("❌ 未找到匹配酒店")
            return
        test_df = sub
    else:
        has_site = df[df['官网链接'].notna() & (df['官网链接'] != '')]
        no_site = df[~df['官网链接'].notna() | (df['官网链接'] == '')]

        has_slice = has_site.iloc[has_website_start: has_website_start + has_website_num]
        no_slice = no_site.iloc[no_website_start:  no_website_start + no_website_num]
        test_df = pd.concat([has_slice, no_slice], ignore_index=True)

    # 处理输出文件
    if overwrite:
        Path(output_file).write_text("")
        logger.info(f"结果将流式写入 (覆盖) → {output_file}")
    else:
        Path(output_file).touch(exist_ok=True)
        logger.info(f"结果将追加写入 → {output_file}")

    asyncio.run(async_send_hotel_requests(
        test_df, region, api_url, base_dir, output_file,
        max_concurrent=max_concurrent, max_retries=max_retries,
        upload_to_db=upload_to_db, db_table=db_table))

    logger.info("全部任务已提交并写入完毕")


# ------------------------------------------------------------------------
#  CLI 入口
# ------------------------------------------------------------------------
if __name__ == "__main__":
    # 示例 1：覆盖写入（默认）
    local_api_url = "http://localhost:8080/api/capture_hotel_complete"
    upload_to_db = True
    db_table = "htlcrawlervacationdb.hotel_static_data"
    
    # 原始请求
    main(
        input_file="大概率有接送服务的日本酒店.xlsx",
        output_file="concat_hotel_capture_raw2.jsonl",
        hotel_name="Setsu Niseko",
        # has_website_start=0, has_website_num=1,
        # # no_website_start=0, no_website_num=0,
        # base_dir="concat_website_access",
        # max_concurrent=3,
        # max_retries=0,
        # api_url=local_api_url,
        # overwrite=False,
        # upload_to_db=upload_to_db,
        # db_table=db_table
    )
    
    # 示例 2：重试失败请求
    # retry_failed_requests(
    #     input_jsonl_file="concat_hotel_capture_raw.jsonl",
    #     output_file="concat_hotel_capture_retry.jsonl",
    #     region="Japan",
    #     base_dir="concat_website_access",
    #     max_concurrent=3,
    #     max_retries=3,
    #     api_url=local_api_url,
    #     upload_to_db=upload_to_db,
    #     db_table=db_table
    # )

    # 示例 3：追加写入
    # main(
    #     input_file="没有官网链接的样本.xlsx",
    #     output_file="hotel_capture_raw.jsonl",
    #     has_website_start=3, has_website_num=2,
    #     no_website_start=4, no_website_num=1,
    #     base_dir="no_website_access2",
    #     max_concurrent=3,
    #     max_retries=0,
    #     overwrite=False,
    # )
