<!DOCTYPE html>
<html class="no-js" dir="ltr" lang="ja" prefix="og: https://ogp.me/ns#"><head prefix="og: http://ogp.me/ns#  article: http://ogp.me/ns/article#"><style>#msta_langArea.msta-default{position:fixed;z-index:99999999;background-color:#005cad;color:#fff;padding:3px 8px;transition:left 300ms 0s ease;transition:right 300ms 0s ease}#msta_langArea.msta-default .msta-lang-list{background-color:#fff;color:#000}#msta_langArea.msta-default .msta_standby{color:#00f;cursor:pointer}#msta_langArea.msta-default .msta_selected{color:#999;cursor:auto}#msta_langArea.msta-default .msta_orgLink{display:inline-block;color:#fff;line-height:12px;font-size:12px;padding:0;margin:0;text-align:center}#msta_langArea.msta-default .msta_orgLink a:hover{color:#999!important}#msta_langArea.msta-default .msta_orgLink a:visited{color:#fff}#MSTAConfirm,#MSTATranslating{width:100%;height:100%;display:none;position:fixed;top:0;left:0;padding:0;margin:0;text-align:center;z-index:999999999}#MSTALoaderImgBox{position:fixed;left:50%;top:50%;transform:translateX(-50%) translateY(-50%)}#MSTALoaderImg{width:70px;--b:15px;aspect-ratio:1;border-radius:50%;padding:1px;background:conic-gradient(#0000 10%,#999) content-box;-webkit-mask:repeating-conic-gradient(#0000 0deg,#000 1deg 37deg,#0000 38deg 45deg),radial-gradient(farthest-side,#0000 calc(100% - var(--b) - 1px),#000 calc(100% - var(--b)));-webkit-mask-composite:destination-in;mask-composite:intersect;animation:MSTALoaderImgANI 1s infinite steps(10)}@keyframes MSTALoaderImgANI{to{transform:rotate(1turn)}}#MSTAWarningBox{width:623px;height:auto;background-color:#fff;border:solid 2px #005cad;border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;-o-border-radius:3px;-ms-border-radius:3px;text-align:center;margin:0 auto}#MSTAWarningBox .MSTAtitle{padding:3px;text-align:left;font-weight:800;color:#fff;background-color:#005cad}#MSTAWarningBox .MSTAmsg{padding:20px;text-align:left;color:#333}#MSTAWarningBox .MSTAbtn{text-align:right;background-color:#005cad}#MSTAWarningBox .MSTAbtn button{margin:3px;min-width:80px;background-color:buttonface;box-sizing:border-box;border-width:2px;border-style:outset;border-color:buttonface;border-image:initial;-webkit-appearance:button;-moz-appearance:button}@media print{#msta_langArea.msta-default{display:none}}@media screen and (max-width:750px){#MSTAWarningBox{width:90%;margin-top:12%!important}}[class*=MSTA_IF_TRANS_HIDE]{display:inline!important;padding:0!important;margin:0!important}.MSTA_TRANS_HIDE{display:none!important}#MSTAWarningBox .MSTAbtn button:focus{outline-style:auto!important;outline-width:medium!important;outline-color:currentColor!important}</style>
<script async="" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js" type="text/javascript"></script><script async="" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js" type="text/javascript"></script><script async="" src="https://d.line-scdn.net/n/line_tag/public/release/v1/lt.js"></script><script async="" src="https://www.googletagmanager.com/gtag/destination?id=AW-765343266&amp;cx=c&amp;gtm=45je5840h2v9191777719za200zb6579748&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/destination?id=AW-*********&amp;cx=c&amp;gtm=45je5840h2v9191777719za200zb6579748&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********" type="text/javascript"></script><script async="" src="https://analytics.tiktok.com/i18n/pixel/static/identify_ecbed230.js" type="text/javascript"></script><script async="" data-id="CQK8AMRC77U2CBNGTUKG" src="https://analytics.tiktok.com/i18n/pixel/static/main.MTc3NWUxZTAxMA.js" type="text/javascript"></script><script async="" src="https://www.clarity.ms/tag/uet/97141453"></script><script async="" src="https://js.fout.jp/segmentation.js" type="text/javascript"></script><script async="" src="https://cdn.microad.jp/js/track.js"></script><script async="" src="https://analytics.tiktok.com/i18n/pixel/events.js?sdkid=CQK8AMRC77U2CBNGTUKG&amp;lib=ttq" type="text/javascript"></script><script async="" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js" type="text/javascript"></script><script async="" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js" type="text/javascript"></script><script async="" src="https://connect.facebook.net/signals/config/1004757730430892?v=2.9.222&amp;r=stable&amp;domain=toho.orixhotelsandresorts.com&amp;hme=2e9ee56babe122798b967566f46100108daa710154b06378259c746cb66ac325&amp;ex_m=83%2C143%2C124%2C18%2C117%2C58%2C40%2C118%2C64%2C57%2C131%2C72%2C13%2C82%2C26%2C112%2C103%2C62%2C65%2C111%2C128%2C91%2C133%2C7%2C3%2C4%2C6%2C5%2C2%2C73%2C81%2C134%2C208%2C155%2C52%2C213%2C210%2C211%2C45%2C170%2C25%2C61%2C217%2C216%2C158%2C28%2C51%2C8%2C54%2C77%2C78%2C79%2C84%2C107%2C27%2C24%2C110%2C106%2C105%2C125%2C63%2C127%2C126%2C41%2C108%2C50%2C100%2C12%2C130%2C37%2C199%2C201%2C165%2C21%2C22%2C23%2C15%2C16%2C36%2C33%2C34%2C68%2C74%2C76%2C89%2C116%2C119%2C38%2C90%2C19%2C17%2C94%2C59%2C31%2C121%2C120%2C122%2C113%2C20%2C30%2C49%2C88%2C129%2C29%2C180%2C151%2C86%2C109%2C67%2C98%2C44%2C39%2C96%2C97%2C102%2C48%2C14%2C104%2C95%2C55%2C43%2C46%2C0%2C80%2C132%2C1%2C101%2C11%2C99%2C257%2C197%2C141%2C183%2C176%2C9%2C47%2C75%2C53%2C123%2C56%2C93%2C71%2C70%2C42%2C114%2C69%2C66%2C60%2C92%2C85%2C35%2C115%2C32%2C87%2C10%2C135%2C223%2C222%2C224%2C229%2C230%2C231%2C227%2C219%2C157%2C195%2C218%2C220%2C259%2C198%2C144%2C188%2C172%2C245%2C189%2C247%2C248%2C246%2C207%2C163%2C153%2C256%2C138%2C161%2C145%2C178%2C152%2C209%2C136%2C166%2C186"></script><script async="" src="https://connect.facebook.net/signals/config/505407292120828?v=2.9.222&amp;r=stable&amp;domain=toho.orixhotelsandresorts.com&amp;hme=2e9ee56babe122798b967566f46100108daa710154b06378259c746cb66ac325&amp;ex_m=83%2C143%2C124%2C18%2C117%2C58%2C40%2C118%2C64%2C57%2C131%2C72%2C13%2C82%2C26%2C112%2C103%2C62%2C65%2C111%2C128%2C91%2C133%2C7%2C3%2C4%2C6%2C5%2C2%2C73%2C81%2C134%2C208%2C155%2C52%2C213%2C210%2C211%2C45%2C170%2C25%2C61%2C217%2C216%2C158%2C28%2C51%2C8%2C54%2C77%2C78%2C79%2C84%2C107%2C27%2C24%2C110%2C106%2C105%2C125%2C63%2C127%2C126%2C41%2C108%2C50%2C100%2C12%2C130%2C37%2C199%2C201%2C165%2C21%2C22%2C23%2C15%2C16%2C36%2C33%2C34%2C68%2C74%2C76%2C89%2C116%2C119%2C38%2C90%2C19%2C17%2C94%2C59%2C31%2C121%2C120%2C122%2C113%2C20%2C30%2C49%2C88%2C129%2C29%2C180%2C151%2C86%2C109%2C67%2C98%2C44%2C39%2C96%2C97%2C102%2C48%2C14%2C104%2C95%2C55%2C43%2C46%2C0%2C80%2C132%2C1%2C101%2C11%2C99%2C257%2C197%2C141%2C183%2C176%2C9%2C47%2C75%2C53%2C123%2C56%2C93%2C71%2C70%2C42%2C114%2C69%2C66%2C60%2C92%2C85%2C35%2C115%2C32%2C87%2C10%2C135"></script><script async="" src="https://connect.facebook.net/en_US/fbevents.js" type="text/javascript"></script><script async="" src="https://bat.bing.com/bat.js" type="text/javascript"></script><script async="" src="https://www.google-analytics.com/analytics.js" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-RQHC82M980&amp;cx=c&amp;gtm=45He5840h2v6579748za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-7CZCCFZKBN&amp;cx=c&amp;gtm=45He5840h2v6579748za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-51RY4TK4JQ&amp;cx=c&amp;gtm=45He5840h2v6579748za200&amp;tag_exp=*********~*********~*********~*********~*********~*********~*********~*********~*********~*********" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-NFW2LB"></script><script src="//msta.j-server.com/MSTA/LUC2ORITOH/js/msta.js"></script><script src="//cdn.jsdelivr.net/npm/jquery@3.2.1/dist/jquery.min.js" type="text/javascript"></script>
<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
  j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
  'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
  })(window,document,'script','dataLayer','GTM-NFW2LB');</script>
<!-- End Google Tag Manager -->
<meta charset="utf-8"/>
<meta content="IE=Edge" http-equiv="X-UA-Compatible"/>
<meta content="telephone=no" name="format-detection"/>
<meta content="width=device-width,user-scalable=yes,initial-scale=1,viewport-fit=cover" name="viewport"/>
<meta content="IE=edge,chrome=1" http-equiv="X-UA-Compatible"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/images/_etc/favicon_toho.ico" rel="icon" type="image/x-icon"/>
<meta content="https://toho.orixhotelsandresorts.com/_assets/images/common/ogp.jpg" property="og:image"/>
<!-- wp_head START -->
<title>アクセス｜会津東山温泉 御宿東鳳［公式・最安］</title>
<!-- All in One SEO 4.5.0 - aioseo.com -->
<meta content="会津東山温泉旅館 御宿東鳳（おんやど とうほう）の「アクセス」のページです。" name="description"/>
<meta content="max-image-preview:large" name="robots"/>
<link href="https://toho.orixhotelsandresorts.com/access/" rel="canonical"/>
<meta content="All in One SEO (AIOSEO) 4.5.0" name="generator"/>
<meta content="ja_JP" property="og:locale"/>
<meta content="会津東山温泉 御宿東鳳［公式・最安］ -" property="og:site_name"/>
<meta content="article" property="og:type"/>
<meta content="アクセス｜会津東山温泉 御宿東鳳［公式・最安］" property="og:title"/>
<meta content="会津東山温泉旅館 御宿東鳳（おんやど とうほう）の「アクセス」のページです。" property="og:description"/>
<meta content="https://toho.orixhotelsandresorts.com/access/" property="og:url"/>
<meta content="https://toho.orixhotelsandresorts.com/uploads/2023/02/ogp_toho.jpg" property="og:image"/>
<meta content="https://toho.orixhotelsandresorts.com/uploads/2023/02/ogp_toho.jpg" property="og:image:secure_url"/>
<meta content="1200" property="og:image:width"/>
<meta content="624" property="og:image:height"/>
<meta content="2022-04-25T10:33:59+00:00" property="article:published_time"/>
<meta content="2025-07-16T08:21:32+00:00" property="article:modified_time"/>
<meta content="summary" name="twitter:card"/>
<meta content="アクセス｜会津東山温泉 御宿東鳳［公式・最安］" name="twitter:title"/>
<meta content="会津東山温泉旅館 御宿東鳳（おんやど とうほう）の「アクセス」のページです。" name="twitter:description"/>
<meta content="https://toho.orixhotelsandresorts.com/uploads/2023/02/ogp_toho.jpg" name="twitter:image"/>
<script class="aioseo-schema" type="application/ld+json">
			{"@context":"https:\/\/schema.org","@graph":[{"@type":"BreadcrumbList","@id":"https:\/\/toho.orixhotelsandresorts.com\/access\/#breadcrumblist","itemListElement":[{"@type":"ListItem","@id":"https:\/\/toho.orixhotelsandresorts.com\/#listItem","position":1,"name":"\u5bb6","item":"https:\/\/toho.orixhotelsandresorts.com\/","nextItem":"https:\/\/toho.orixhotelsandresorts.com\/access\/#listItem"},{"@type":"ListItem","@id":"https:\/\/toho.orixhotelsandresorts.com\/access\/#listItem","position":2,"name":"\u30a2\u30af\u30bb\u30b9","previousItem":"https:\/\/toho.orixhotelsandresorts.com\/#listItem"}]},{"@type":"Organization","@id":"https:\/\/toho.orixhotelsandresorts.com\/#organization","name":"\u6749\u4e43\u4e95\u30db\u30c6\u30eb","url":"https:\/\/toho.orixhotelsandresorts.com\/","logo":{"@type":"ImageObject","url":"https:\/\/toho.orixhotelsandresorts.com\/wp-content\/uploads\/2022\/04\/siteHeader_logo02.png","@id":"https:\/\/toho.orixhotelsandresorts.com\/access\/#organizationLogo","width":328,"height":172},"image":{"@id":"https:\/\/toho.orixhotelsandresorts.com\/#organizationLogo"}},{"@type":"WebPage","@id":"https:\/\/toho.orixhotelsandresorts.com\/access\/#webpage","url":"https:\/\/toho.orixhotelsandresorts.com\/access\/","name":"\u30a2\u30af\u30bb\u30b9\uff5c\u4f1a\u6d25\u6771\u5c71\u6e29\u6cc9 \u5fa1\u5bbf\u6771\u9cf3\uff3b\u516c\u5f0f\u30fb\u6700\u5b89\uff3d","description":"\u4f1a\u6d25\u6771\u5c71\u6e29\u6cc9\u65c5\u9928 \u5fa1\u5bbf\u6771\u9cf3\uff08\u304a\u3093\u3084\u3069 \u3068\u3046\u307b\u3046\uff09\u306e\u300c\u30a2\u30af\u30bb\u30b9\u300d\u306e\u30da\u30fc\u30b8\u3067\u3059\u3002","inLanguage":"ja","isPartOf":{"@id":"https:\/\/toho.orixhotelsandresorts.com\/#website"},"breadcrumb":{"@id":"https:\/\/toho.orixhotelsandresorts.com\/access\/#breadcrumblist"},"datePublished":"2022-04-25T10:33:59+09:00","dateModified":"2025-07-16T08:21:32+09:00"},{"@type":"WebSite","@id":"https:\/\/toho.orixhotelsandresorts.com\/#website","url":"https:\/\/toho.orixhotelsandresorts.com\/","name":"\u4f1a\u6d25\u6771\u5c71\u6e29\u6cc9 \u5fa1\u5bbf\u6771\u9cf3\uff3b\u516c\u5f0f\u30b5\u30a4\u30c8\uff3d","inLanguage":"ja","publisher":{"@id":"https:\/\/toho.orixhotelsandresorts.com\/#organization"}}]}
		</script>
<!-- All in One SEO -->
<link href="//ajax.googleapis.com" rel="dns-prefetch"/>
<link href="//cdnjs.cloudflare.com" rel="dns-prefetch"/>
<link href="https://toho.orixhotelsandresorts.com/feed/" rel="alternate" title="会津東山温泉 御宿東鳳［公式・最安］ » フィード" type="application/rss+xml"/>
<link href="https://toho.orixhotelsandresorts.com/comments/feed/" rel="alternate" title="会津東山温泉 御宿東鳳［公式・最安］ » コメントフィード" type="application/rss+xml"/>
<link href="https://toho.orixhotelsandresorts.com/wp-includes/css/dist/block-library/style.min.css?ver=6.3.1" id="wp-block-library-css" media="all" rel="stylesheet" type="text/css"/>
<style id="classic-theme-styles-inline-css" type="text/css">
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<style id="global-styles-inline-css" type="text/css">
body{--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}body .is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}body .is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}body .is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}body .is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}body .is-layout-flex{flex-wrap: wrap;align-items: center;}body .is-layout-flex > *{margin: 0;}body .is-layout-grid{display: grid;}body .is-layout-grid > *{margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
.wp-block-navigation a:where(:not(.wp-element-button)){color: inherit;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
.wp-block-pullquote{font-size: 1.5em;line-height: 1.6;}
</style>
<link href="https://toho.orixhotelsandresorts.com/plugins/wp-pagenavi/pagenavi-css.css?ver=2.70" id="wp-pagenavi-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/js/lib/fancybox/jquery.fancybox.min.css?ver=3.5.7" id="fancybox-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/js/lib/swiper/swiper-bundle.min.css?ver=8.2.6" id="swiper-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/js/lib/slick/slick.css?ver=1.8.1" id="slick-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/js/lib/wow/animate.css?ver=4.4.2" id="animate-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/js/lib/jPages/jPages.css?ver=1.0" id="jPages-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/_assets/css/style.css?ver=1.0" id="style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://toho.orixhotelsandresorts.com/?p=459" rel="shortlink"/>
<!-- wp_head END -->
<script src="https://b98.yahoo.co.jp/pagead/conversion_async.js" type="text/javascript"></script><script async="" data-ueto="ueto_cc0bbb3a9d" src="https://bat.bing.com/p/action/97141453.js" type="text/javascript"></script><script async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/*********/?random=1754392336359&amp;cv=11&amp;fst=1754392336359&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5840h2z86579748za200zb6579748zd6579748xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528501~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg&amp;rfmt=3&amp;fmt=4" type="text/javascript"></script><script async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/765343266/?random=1754392336492&amp;cv=11&amp;fst=1754392336492&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5840h2v9173965711z86579748za200zb6579748zd6579748xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528500~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg&amp;rfmt=3&amp;fmt=4" type="text/javascript"></script><link href="https://bot.talkappi.com/assets/talkappi/talkappi.css" id="talkappi-css" media="all" rel="stylesheet" type="text/css"/><script async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/765343266/?random=1754392342710&amp;cv=11&amp;fst=1754392342710&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=form_start&amp;gtm=45be5840h2v9173965711za200zb6579748zd6579748xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528500~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg&amp;data=event%3Dform_start&amp;rfmt=3&amp;fmt=4" type="text/javascript"></script><link href="https://bot.talkappi.com/assets/f/onyado-toho-hp/bot/talkappi.css" id="talkappi-css" media="all" rel="stylesheet" type="text/css"/><link href="https://bot.talkappi.com/assets/f/onyado-toho-hp/bot/talkappi.css" id="talkappi-css" media="all" rel="stylesheet" type="text/css"/></head>
<body class="is_page is_page__access templatae_accessPage is__ID459 is_page__lower">
<!-- Google Tag Manager (noscript) -->
<noscript><iframe height="0" src="https://www.googletagmanager.com/ns.html?id=GTM-NFW2LB" style="display:none;visibility:hidden" width="0"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<!-- //////////////////// HEADER START //////////////////// -->
<header class="siteHeader type-phase01">
<div class="siteHeader__wrap">
<div class="siteHeader__inner">
<div class="siteHeader__menuTop">
<ul class="siteHeader__menuLang">
<li class="siteHeader__menuLangLink msta_standby" id="msta_lang_ja">JP</li>
<li class="siteHeader__menuLangLink msta_standby" id="msta_lang_en">EN</li>
</ul>
</div>
<p class="siteHeader__menuLogo is-orix">
<a class="siteHeader__menuLogoLink" href="https://toho.orixhotelsandresorts.com/">
<!-- <a href="https://suginoi.orixhotelsandresorts.com/" class="siteHeader__menuLogoLink"> -->
<img alt="ORIX HOTELS &amp; RESORTS(オリックス ホテルズ＆リゾーツ)" class="siteHeader__topMenuLogo" src="https://toho.orixhotelsandresorts.com/_assets/images/layout/siteHeader_logo.png"/>
<img alt="旅館コレクション 東鳳" class="siteHeader__lowerMenuLogo" src="https://toho.orixhotelsandresorts.com/_assets/images/layout/siteHeader_logo02.png"/>
</a>
</p>
<nav class="siteHeader__nav">
<div class="siteHeader__menuToggle">
<div class="siteHeader__menuToggleText">MENU</div>
<div class="siteHeader__menuToggleItem js-menuToggle"><span></span><span></span><span></span></div>
</div>
</nav>
</div>
</div>
<div class="siteHeader__menu">
<div class="siteHeader__menuInner">
<div class="siteHeader__inner">
<div class="siteHeader__menuTop">
<ul class="siteHeader__menuLang">
</ul>
</div>
<div class="siteHeader__menuLogo">
<img alt="ORIX HOTELS &amp; RESORTS(オリックス ホテルズ＆リゾーツ)" src="https://toho.orixhotelsandresorts.com/_assets/images/layout/siteHeader_logo02.png"/>
</div>
<nav class="siteHeader__nav">
</nav>
</div>
<div class="siteHeader__menuLinkArea">
<!-- OPEN 施設エリア -->
<div class="siteHeader__menuTitle js-btn_sp" data-animation="slide" data-btn="header-group01">
<span class="siteHeader__menuTitleItem">温泉</span>
</div>
<div class="siteHeader__menuGroup clearfix" data-target="header-group01">
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3076/" target="_self">各温泉のご紹介</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-5691/" target="_blank">リラクゼーション</a>
</div>
<div class="siteHeader__menuTitle js-btn_sp" data-animation="slide" data-btn="header-group02">
<span class="siteHeader__menuTitleItem">お食事</span>
</div>
<div class="siteHeader__menuGroup clearfix" data-target="header-group02">
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3073/" target="_blank">夕食ビュッフェ</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3085/" target="_blank">朝食ビュッフェ</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-5905/" target="_blank">季節のメニュー</a>
</div>
<div class="siteHeader__menuTitle js-btn_sp" data-animation="slide" data-btn="header-group03">
<span class="siteHeader__menuTitleItem">お部屋</span>
</div>
<div class="siteHeader__menuGroup clearfix" data-target="header-group03">
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3056/" target="_self">彩 (いろどり)</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3070/" target="_self">和室</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3072/" target="_self">洋室</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3107/" target="_self">大部屋</a>
<a class="siteHeader__menuGroupLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3119/" target="_self">特別和洋室</a>
</div>
<div class="siteHeader__menuListSub">
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink talkappi-faq-icon" href="javascript:void(0);">よくあるご質問</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/plan/" target="_self">おすすめプラン</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/stay/" target="_self">おすすめの過ごし方</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/map/" target="_self">館内マップ</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/area_information/" target="_self">周辺情報</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/news/" target="_self">お知らせ</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/concept/" target="_self">私たちの想い</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/access/" target="_self">アクセス</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://www.toho-wedding.jp/" target="_blank">TOHO Wedding</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3101/" target="_self">東鳳のおもてなし</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://toho.orixhotelsandresorts.com/contents/contents-3290/" target="_self">団体・ご宴会</a>
</div>
<div class="siteHeader__menuItemSub">
<a class="siteHeader__menuItemLink" href="https://inquiry.talkappi.com/?f=onyado-toho-hp&amp;id=2260060001" target="_self">お問い合わせ</a>
</div>
</div>
<div class="siteHeader__menuLast u-spDb">
<p class="siteHeader__menuLastName">会津・東山温泉 御宿 東鳳</p>
<p class="siteHeader__menuLastAddress">〒965-0813</p>
<p class="siteHeader__menuLastAddress">福島県会津若松市東山町大字石山字院内706</p>
<p class="siteHeader__menuLastTel"><a href="tel:0242-26-4141">TEL：0242-26-4141</a><br/>受付時間 9:00～19:00</p>
</div>
<div class="siteHeader__menuBottom">
<div class="u-button__arrow siteHeader__searchBtn">
<a class="u-button__arrowText siteHeader__searchBtnInner" href="https://go-ohr.reservation.jp/ja/hotels/ohr-toho" rel="noopener noreferrer" target="_blank">空室検索</a>
</div>
<div class="siteHeader__menuBottomSns">
<a class="siteHeader__menuBottomSnsItem" href="https://www.instagram.com/onyado_toho/" rel="noopener" target="_blank"><img alt="Instagram" class="siteHeader__menuBottomSnsImage" src="https://toho.orixhotelsandresorts.com/_assets/images/layout/header_snsSecArea_instagram.png"/></a>
</div>
</div>
<div class="siteHeader__menuLast u-pcDf">
<p class="siteHeader__menuLastName">会津・東山温泉 御宿 東鳳</p>
<p class="siteHeader__menuLastAddress">〒965-0813 福島県会津若松市東山町大字石山字院内706</p>
<p class="siteHeader__menuLastTel"><a href="tel:0242-26-4141">TEL：0242-26-4141</a>　受付時間 9:00～19:00</p>
</div>
</div>
</div>
</div>
</header>
<!-- //////////////////// HEADER END //////////////////// -->
<!-- //////////////////// CONTENT START //////////////////// -->
<main class="siteContent" role="main"><!-- ////////////////空室検索//////////////// -->
<!-- 空室検索 -->
<div class="search__bg"></div>
<div class="topPageFvSlider__searchBtnContent">
<div class="search__ttlArea">
<p class="search__ttl">空室検索</p>
<p class="search__subTtl">当サイトが最もお得</p>
</div>
<div class="search__container">
<link href="https://cdn.jsdelivr.net/npm/pikaday@1.8.0/css/pikaday.css" rel="stylesheet" type="text/css"/>
<style>
                            .pika-lendar th:first-child,
                            .pika-lendar td:first-child .pika-button {
                                color: #f00;
                            }
                            .pika-lendar th:last-child,
                            .pika-lendar td:last-child .pika-button {
                                color: #00f;
                            }
                            .has-event .pika-button {
                                color: #f00;
                                font-weight: normal;
                                background: #f5f5f5;
                                box-shadow:none;
                            }
                            .is-selected .pika-button {
                                color: #fff;
                                font-weight: bold;
                                background: #33aaff;
                                box-shadow: inset 0 1px 3px #178fe5;
                                border-radius: 3px;
                            }
                            .pika-button:hover,
                            .pika-row.pick-whole-week:hover .pika-button {
                                color: #fff;
                                background: #ff8000;
                                box-shadow: none;
                                border-radius: 3px;
                            }
                            .is-disabled .pika-button{
                                background: #D5E9F7 !important;
                            }
                        </style>
<script src="https://cdn.jsdelivr.net/npm/pikaday@1.8.0/pikaday.js"></script>
<script src="https://manager.reservation.jp/cmn/js/externalSite/es6-promise.auto.min.js"></script>
<form action="https://go-ohr.reservation.jp/ja/hotels/ohr-toho/search" class="search__form" data-gtm-form-interact-id="0" id="ZenSearchFrom" method="get" target="_blank">
<div class="search__box" id="searchBox">
<fieldset class="checkin search__field">
<legend>宿泊日</legend>
<input autocomplete="off" data-gtm-form-interact-field-id="0" id="checkin_date" placeholder="2025/08/04" readonly="" type="text"/>
<input id="checkin_date_hidden" name="checkin_date" type="hidden"/>
</fieldset>
<fieldset class="nights search__field">
<legend>泊数</legend>
<select id="numOfNights" style="color: #a0a0a0; background: #fff">
<option value="1">1泊</option>
<option value="2">2泊</option>
<option value="3">3泊</option>
<option value="4">4泊</option>
<option value="5">5泊</option>
<option value="6">6泊</option>
<option value="7">7泊</option>
<option value="8">8泊</option>
<option value="9">9泊</option>
<option value="10">10泊</option>
<option value="11">11泊</option>
<option value="12">12泊</option>
<option value="13">13泊</option>
<option value="14">14泊</option>
<option value="15">15泊</option>
<option value="16">16泊</option>
<option value="17">17泊</option>
<option value="18">18泊</option>
<option value="19">19泊</option>
<option value="20">20泊</option>
<option value="21">21泊</option>
<option value="22">22泊</option>
<option value="23">23泊</option>
<option value="24">24泊</option>
<option value="25">25泊</option>
<option value="26">26泊</option>
<option value="27">27泊</option>
<option value="28">28泊</option>
<option value="29">29泊</option>
<option value="30">30泊</option>
</select>
</fieldset>
<fieldset class="rooms search__field">
<legend>室数</legend>
<select name="rooms" style="color: #a0a0a0; background: #fff">
<option value="1">1室</option>
<option value="2">2室</option>
<option value="3">3室</option>
<option value="4">4室</option>
<option value="5">5室</option>
</select>
</fieldset>
<fieldset class="adults search__field">
<legend>大人</legend>
<select name="adults" style="color: #a0a0a0; background: #fff">
<option value="1">1人</option>
<option value="2">2人</option>
<option value="3">3人</option>
<option value="4">4人</option>
<option value="5">5人</option>
<option value="6">6人</option>
<option value="7">7人</option>
<option value="8">8人</option>
<option value="9">9人</option>
<option value="10">10人</option>
</select>
</fieldset>
<p class="field-button search__btn">
<input class="sub-bgcolor" id="search-submit" type="submit" value="検索"/>
</p>
</div><!--/searchbox-->
</form>
<script>
                            window.addEventListener('DOMContentLoaded', function () {
                              makeElementsWithFacilityInfo();
                            })
                            function formatDate(dt) {
                            var y = dt.getFullYear();
                            var m = ('00' + (dt.getMonth()+1)).slice(-2);
                            var d = ('00' + dt.getDate()).slice(-2);
                            document.getElementById('checkin_date').value = y + '/' + m + '/' + d;
                            }
                            formatDate(new Date());
                            document.getElementById('ZenSearchFrom').addEventListener('submit', function (event) {
                                var checkInDate = document.getElementById('checkin_date').value;
                                if (checkInDate == "") {
                                  // alert("チェックイン は必須です");
                                  alert("宿泊日は必須です");
                                  event.preventDefault();
                                  return;
                                }
                                var nights = document.getElementById('numOfNights').value;
                                var checkInDate = document.getElementById('checkin_date').value;
                                var checkInDateArr = checkInDate.split('/');
                                // checkInDateArr[0] = checkInDate.substr(0, 4);
                                // checkInDateArr[1] = checkInDate.substr(4, 2);
                                // checkInDateArr[2] = checkInDate.substr(6, 2);

                                var checkOutForCalc = new Date(checkInDateArr[0], parseInt(checkInDateArr[1], 10) - 1, checkInDateArr[2]);
                                checkOutForCalc.setDate(checkOutForCalc.getDate() + parseInt(nights, 10));

                                var checkOut = checkOutForCalc.getFullYear() + ('0' + (checkOutForCalc.getMonth() + 1)).slice(-2) + ('0' + checkOutForCalc.getDate()).slice(-2);

                                var input = document.createElement('input');
                                input.setAttribute('type', 'hidden');
                                input.setAttribute('name', 'checkout_date');
                                input.setAttribute('value', checkOut);
                                document.getElementById('ZenSearchFrom').appendChild(input);

                                checkInDate = checkInDateArr.join('');
                                document.getElementById('checkin_date_hidden').value = checkInDate;

                                setAttributeParams()
                            });

                            function makeElementsWithFacilityInfo(){
                              let langCd              = 'ja'
                              let externalSiteAuthKey = '288f8065930c69ff97ea7ce8bf84b2f0fa6968dd'
                              getFacilityInfo(externalSiteAuthKey, langCd).then(function (result) {
                                checkinCalender(result.data.today, result.data.holiday)
                                childrenDisplay(result.data.facilityChildPriceInfo)
                                attributesDisplay(result.data.attributes)
                              })
                            }

                            function getFacilityInfo(externalSiteAuthKey,langCd) {
                              return new Promise(function (resolve, reject) {
                                var req = new XMLHttpRequest();
                                var method = 'GET';
                                var url = 'https://zen-api.reservation.jp/external-site-v1/facility/getFacilityInfo?externalSiteAuthKey=' + externalSiteAuthKey  + '&langCd=' + langCd;
                                req.onreadystatechange = function() {
                                  if (req.readyState == 4){
                                    if (req.status == 200) {
                                      let data = (req.response);
                                      let obj = JSON.parse(data);
                                      resolve(obj);
                                    } else {
                                       reject(req.statusText);
                                    }
                                  }
                                };
                                req.open(method, url);
                                req.send();
                              });
                            }
                            // チェックイン日のカレンダーを作成
                            function checkinCalender(today, holiday){
                                var endMin = new Date(today);
                                endMin.setDate(endMin.getDate() + 1);
                                var startMax = new Date(today);
                                startMax.setMonth(startMax.getMonth() + 13);
                                var endMax = new Date(today);
                                endMax.setMonth(endMax.getMonth() + 13);
                                endMax.setDate(endMax.getDate() + 1);
                                var arrayHoliday = Object.values(holiday);
                                var checkin = new Pikaday({
                                    field: document.getElementById('checkin_date'),
                                    format: 'YYYY/MM/DD',
                                    showDaysInNextAndPreviousMonths: true,
                                    enableSelectionDaysInNextAndPreviousMonths: true,
                                    minDate: new Date(today),
                                    maxDate: startMax,
                                    events: arrayHoliday,
                                    i18n: {
                                        previousMonth : "前の月",
                                        nextMonth     : "次の月",
                                        months        : ["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],
                                        weekdays      : ["日曜日","月曜日","火曜日","水曜日","木曜日","金曜日","土曜日"],
                                        weekdaysShort : ["日","月","火","水","木","金","土"]
                                    },
                                    toString: function(date, format) {
                                        var day =  ('0' + (date.getDate())).slice(-2);
                                        var month = ('0' + (date.getMonth() + 1)).slice(-2);
                                        var year = date.getFullYear();
                                        // return String(year) + String(month) + String(day);
                                        return `${year}/${month}/${day}`;
                                    },
                                    parse: function(dateString, format) {
                                        var parts = dateString.split('/');
                                        var day = parseInt(parts[0], 10);
                                        var month = parseInt(parts[1], 10) - 1;
                                        var year = parseInt(parts[2], 10);
                                        return new Date(year, month, day);
                                    }
                                })
                            }

                            function getFieldset (title, fieldsetClass = '') {
                              let fieldset = document.createElement('fieldset')
                              if (fieldsetClass) {
                                fieldset.setAttribute('class', fieldsetClass)
                              }
                              let legend = document.createElement('legend')
                              legend.innerText = title
                              fieldset.appendChild(legend)
                              return fieldset
                            }

                            function addElement (el) {
                              let fieldButton = document.querySelector('.field-button')
                              if (fieldButton) {
                                fieldButton.before(el)
                              }
                            }

                            function childrenDisplay (facilityChildPriceInfo) {
                              if (
                                facilityChildPriceInfo.child1DisplayFlg == '0' &&
                                facilityChildPriceInfo.child2DisplayFlg == '0' &&
                                facilityChildPriceInfo.child3DisplayFlg == '0' &&
                                facilityChildPriceInfo.child4DisplayFlg == '0' &&
                                facilityChildPriceInfo.child5DisplayFlg == '0'
                              ) {
                                return
                              }
                              let title = '子供'
                              let fieldset = getFieldset(title, 'children')
                              let div = getChildren(facilityChildPriceInfo)
                              fieldset.appendChild(div)
                              addElement(fieldset)
                            }

                            function getChildren (facilityChildPriceInfo) {

                              // let div = document.createElement('div')
                              // div.setAttribute('class', 'children-selects')

                              // if (facilityChildPriceInfo.child1DisplayFlg != '0') {
                              //   let child1Recive = '1'
                              //   let child1 = getChild('child1', child1Recive, facilityChildPriceInfo)
                              //   div.appendChild(child1)
                              // }

                              // if (facilityChildPriceInfo.child2DisplayFlg != '0') {
                              //   let child2Recive = '1'
                              //   let child2 = getChild('child2', child2Recive, facilityChildPriceInfo)
                              //   div.appendChild(child2)
                              // }

                              // if (facilityChildPriceInfo.child3DisplayFlg != '0') {
                              //   let child3Recive = '1';
                              //   let child3 = getChild('child3', child3Recive, facilityChildPriceInfo)
                              //   div.appendChild(child3)
                              // }

                              // if (facilityChildPriceInfo.child4DisplayFlg != '0') {
                              //   let child4Recive = '1';
                              //   let child4 = getChild('child4', child4Recive, facilityChildPriceInfo)
                              //   div.appendChild(child4)
                              // }

                              // if (facilityChildPriceInfo.child5DisplayFlg != '0') {
                              //   let child5Recive = '1';
                              //   let child5 = getChild('child5', child5Recive, facilityChildPriceInfo)
                              //   div.appendChild(child5)
                              // }

                              // return div
                            }

                            function getChild (targetChild, childRecive, facilityChildPriceInfo) {

                              let optionList = [{"label":"0\u4eba","value":0},{"label":"1\u4eba","value":1},{"label":"2\u4eba","value":2},{"label":"3\u4eba","value":3},{"label":"4\u4eba","value":4},{"label":"5\u4eba","value":5},{"label":"6\u4eba","value":6},{"label":"7\u4eba","value":7},{"label":"8\u4eba","value":8},{"label":"9\u4eba","value":9},{"label":"10\u4eba","value":10}]
                              let unreceived = '受け入れなし'
                              var divClass = ''
                              var childName = ''

                              if (targetChild == 'child1') {
                                divClass = 'child1'
                                childName = facilityChildPriceInfo.child1Name
                              } else if (targetChild == 'child2') {
                                divClass = 'bed-and-meal'
                                childName = facilityChildPriceInfo.child2Name
                              } else if (targetChild == 'child3') {
                                divClass = 'bed-only'
                                childName = facilityChildPriceInfo.child3Name
                              } else if (targetChild == 'child4') {
                                divClass = 'meal-only'
                                childName = facilityChildPriceInfo.child4Name
                              } else if (targetChild == 'child5') {
                                divClass = 'no-bed-and-meal'
                                childName = facilityChildPriceInfo.child5Name
                              }

                              let div = document.createElement('div')
                              div.setAttribute('class', divClass)
                              let label = document.createElement('label')
                              label.innerText = childName
                              if (childRecive == '1') {
                                let select = document.createElement('select')
                                select.setAttribute('name', targetChild)
                                optionList.forEach ((data) => {
                                  let option = document.createElement('option')
                                  option.setAttribute('value', data.value)
                                  option.innerText = data.label
                                  select.appendChild(option)
                                })
                                label.appendChild(select)
                              } else {
                                let span = document.createElement('span')
                                span.setAttribute('name','no-acceptance')
                                span.innerText = unreceived
                                label.appendChild(span)
                              }
                              div.appendChild(label)
                              return div
                            }

                            function attributesDisplay(attributes) {

                              // 属性情報非存在時は処理を中断
                              if (Object.entries(attributes.room).length === 0 && Object.entries(attributes.plan).length === 0) {
                                return
                              }

                              let fieldset = getFieldset('属性検索')

                              if (Object.entries(attributes.room).length >= 1) {
                                let title = '客室属性'
                                let roomFieldset = getFieldset(title)
                                let roomAttributes = getAttributes('1', attributes.room)
                                roomFieldset.appendChild(roomAttributes)
                                // 客室属性セット TODO:枠が不要な場合はroomAttributesを引数としてセットしてください
                                fieldset.appendChild(roomFieldset)
                              }

                              if (Object.entries(attributes.plan).length >= 1) {
                                let title = 'プラン属性'
                                let planFieldset = getFieldset(title)
                                let planAttributes = getAttributes('2', attributes.plan)
                                planFieldset.appendChild(planAttributes)
                                // プラン属性セット TODO:枠が不要な場合はplanAttributesを引数としてセットしてください
                                fieldset.appendChild(planFieldset)
                              }
                              addElement(fieldset)
                            }

                            function getAttributes (type, attributes) {

                              // 属性ラベル情報を設定
                              function getLabels (attribute) {
                                let ul = document.createElement('ul')
                                Object.entries(attribute.labels).forEach(attributeLabel => {
                                  let li = document.createElement('li')
                                  let label = document.createElement('label')
                                  let input = document.createElement('input')
                                  input.setAttribute("type","checkbox")
                                  input.setAttribute("value",attributeLabel[1].searchValue)
                                  label.appendChild(input)
                                  let span = document.createElement('span')
                                  span.innerText = attributeLabel[1].labelName
                                  label.appendChild(span)
                                  li.appendChild(label)
                                  ul.appendChild(li)
                                })
                                return ul
                              }

                              function getDd (attribute) {
                                let dd = document.createElement('dd')
                                let ul = getLabels(attribute)
                                dd.appendChild(ul)
                                return dd
                              }

                              var dlClass = ''

                              if (type == '1') {
                                dlClass = 'room-attributes'
                              } else if (type == '2') {
                                dlClass = 'plan-attributes'
                              }

                              let dl = document.createElement('dl')
                              dl.classList.add(dlClass)

                              Object.entries(attributes).forEach(attribute => {
                                let dt = document.createElement('dt')
                                let dd = getDd(attribute[1])
                                dt.innerText = attribute[1].groupName
                                dl.appendChild(dt)
                                dl.appendChild(dd)
                              })
                              return dl
                            }

                            function setAttributeParams () {
                              let ra = []
                              let pa = []
                              document.querySelectorAll('.room-attributes input[type="checkbox"]').forEach(function($el) {
                                if ($el.checked) {
                                  ra.push($el.value)
                                }
                              })
                              document.querySelectorAll('.plan-attributes input[type="checkbox"]').forEach(function($el) {
                                if ($el.checked) {
                                  pa.push($el.value)
                                }
                              })
                              if (ra.length >= 1) {
                                let input = document.createElement('input')
                                input.setAttribute('type', 'hidden')
                                input.setAttribute('name', 'ra')
                                input.setAttribute('value', ra.join('.'))
                                document.getElementById('ZenSearchFrom').appendChild(input)
                              }
                              if (pa.length >= 1) {
                                let input = document.createElement('input')
                                input.setAttribute('type', 'hidden')
                                input.setAttribute('name', 'pa')
                                input.setAttribute('value', pa.join('.'))
                                document.getElementById('ZenSearchFrom').appendChild(input)
                              }
                            }
                        </script>
<!-- <ul class="search__linkArea">
                          <li class="search__linkBtnWrap">
                            <a href="#" class="search__linkBtn" target="_blank" rel="noopener noreferrer">
                              <span class="search__linkBtnTxt">航空券付きプラン</span>
                            </a>
                          </li>
                          <li class="search__linkBtnWrap">
                            <a href="#" target="_blank" rel="noopener noreferrer" class="search__linkBtn">
                              <span class="search__linkBtnTxt">レストラン予約</span>
                            </a>
                          </li> -->
<!-- 後日削除予定のボタン -->
<!-- <li class="search__linkBtnWrap">
                            <a href="https://asp.hotel-story.ne.jp/ver3d/ASPY0300.asp?cod1=30490&cod2=001" target="_blank" rel="noopener noreferrer" class="search__linkBtn">
                              <span class="search__linkBtnTxt">5月17日12:00以前に<br class="u-spDb">ご予約いただいたお客さまの確認</span>
                            </a>
                          </li> -->
<!-- 後日削除予定のボタン ここまで -->
<!-- </ul> -->
<p class="search__txtLinkWrap">
<a class="search__txtLink" href="https://toho.orixhotelsandresorts.com/bestrate/">当サイトからのご予約が最もお得です</a>
</p>
</div>
</div>
<!-- </div> -->
<section class="accessPage__top">
<h2 class="u-lowerPageTitle__center accessPage__topTitle">アクセス<span class="u-lowerPageTitle__en">Access</span></h2>
<h3 class="accessPage__topSubTitle">会津・東山温泉 御宿 東鳳</h3>
<p class="accessPage__address">〒965-0813 <br/>
福島県会津若松市東山町大字石山字院内706</p>
<!-- googleMap -->
<div class="accessPage__map">
<iframe allowfullscreen="" height="450" loading="lazy" referrerpolicy="no-referrer-when-downgrade" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3166.091283508837!2d139.95871979999998!3d37.4821722!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x5ff554c4ca450405%3A0xda968cdbf98c10e6!2z44CSOTY1LTA4MTMg56aP5bO255yM5Lya5rSl6Iul5p2-5biC5p2x5bGx55S65aSn5a2X55-z5bGx6Zmi5YaF77yX77yQ77yWIOW-oeWuvyDmnbHps7M!5e0!3m2!1sja!2sjp!4v1675320153035!5m2!1sja!2sjp" style="border:0;" width="600"></iframe> </div>
</section>
<section class="accessPage__main">
<div class="u-contentCenter">
<ul class="accessPage__mainList accordion">
<p class="accessPage__label">交通のご案内</p>
<li class="accessPage__mainItem accordion__item">
<div class="accessPage__mainItemTitleWrap accordion__title">
<div class="accord__plus"></div>
<p class="accessPage__mainItemTitle">お車でのアクセス</p>
</div>
<div class="accessPage__mainItemBody accordion__body">
<section class="u-articleSec">
<div class="u-articleThumb part-colum1">
<figure class="u-articleThumb__item u-pcDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb06120000434k9b70B2.png"/>
</figure>
<figure class="u-articleThumb__item u-spDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb6c120000434k8t744B.png"/>
</figure>
</div>
<div class="u-articleMap">
<iframe allowfullscreen="" height="450" loading="lazy" referrerpolicy="no-referrer-when-downgrade" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3166.091283508837!2d139.95871979999998!3d37.4821722!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x5ff554c4ca450405%3A0xda968cdbf98c10e6!2z44CSOTY1LTA4MTMg56aP5bO255yM5Lya5rSl6Iul5p2-5biC5p2x5bGx55S65aSn5a2X55-z5bGx6Zmi5YaF77yX77yQ77yWIOW-oeWuvyDmnbHps7M!5e0!3m2!1sja!2sjp!4v1675320153035!5m2!1sja!2sjp" style="border:0;" width="600"></iframe> </div>
<div class="u-contentCenter">
<div class="u-articleBtn">
<p class="u-button__arrow">
<a class="u-button__arrowText" href="https://www.google.co.jp/maps/dir/37.5232576,139.9177132/37.5180231,139.9187541/37.5183446,139.9408057/37.4940712,139.9422015/%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3,+%E3%80%92965-0813+%E7%A6%8F%E5%B3%B6%E7%9C%8C%E4%BC%9A%E6%B4%A5%E8%8B%A5%E6%9D%BE%E5%B8%82%E6%9D%B1%E5%B1%B1%E7%94%BA%E5%A4%A7%E5%AD%97%E7%9F%B3%E5%B1%B1%E5%AD%97%E9%99%A2%E5%86%85706/@37.5027197,139.9207128,14z/am=t/data=!3m1!4b1!4m12!4m11!1m0!1m0!1m0!1m0!1m5!1m1!1s0x5ff554c4cbac20f9:0x7ffa629b92b24e21!2m2!1d139.9587683!2d37.4822008!3e0" target="_blank"><span>現在地からのルートを見る</span></a>
</p>
</div>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__large"></p>
</div>
<div class="u-wysiwygArea"><h3>駐車場のご案内</h3>
<p>宿泊者専用の無料駐車場が普通車100台、大型バス20台分ほどございます。玄関前までお越し頂けましたら、スタッフが駐車場をご案内させていただきます。</p>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__small"></p>
</div>
<div class="u-contentCenter">
<div class="u-articleBtn">
<p class="u-button__arrow u-pdf">
<a class="u-button__arrowText" href="https://toho.orixhotelsandresorts.com/uploads/2023/10/access_parking.pdf" rel="noopener noreferrer" target="_blank">詳しくはPDFダウンロード</a>
</p>
</div>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__large"></p>
</div>
<div class="u-wysiwygArea"><h3>ご宿泊者専用の電気自動車(EV)・プラグインハイブリッド車でお越しのお客様へ</h3>
<p>EV200V/16Aコンセント付駐車スペースが4台分ございます。専用の<a href="https://www.wecharge.com/" rel="noopener" target="_blank">「WeChargeアプリ」</a>に車検証とクレジットカード情報を登録するのみで、その場ですぐに充電できます。駐車スペース満車の場合もございますのでご了承ください。</p>
<p> </p>
<h6>※駐車スペースのご予約は承れません。</h6>
</div>
</section>
</div>
</li>
<li class="accessPage__mainItem accordion__item">
<div class="accessPage__mainItemTitleWrap accordion__title">
<div class="accord__plus"></div>
<p class="accessPage__mainItemTitle">電車でのアクセス</p>
</div>
<div class="accessPage__mainItemBody accordion__body">
<section class="u-articleSec">
<div class="u-articleThumb part-colum1">
<figure class="u-articleThumb__item u-pcDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb05120000434k8v6638.png"/>
</figure>
<figure class="u-articleThumb__item u-spDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb12120000434k9d57B0.png"/>
</figure>
</div>
<div class="u-contentCenter">
<div class="u-articleBtn">
<p class="u-button__arrow">
<a class="u-button__arrowText" href="https://www.jreast-timetable.jp/timetable/list0018.html" target="_blank"><span>会津若松駅の時刻表はこちら</span></a>
</p>
</div>
</div>
</section>
</div>
</li>
<li class="accessPage__mainItem accordion__item">
<div class="accessPage__mainItemTitleWrap accordion__title">
<div class="accord__plus"></div>
<p class="accessPage__mainItemTitle">空港からのアクセス</p>
</div>
<div class="accessPage__mainItemBody accordion__body">
<section class="u-articleSec">
<div class="u-articleThumb part-colum1">
<figure class="u-articleThumb__item u-pcDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb1y120000434k9fB647.png"/>
</figure>
<figure class="u-articleThumb__item u-spDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3o120000434k8n5596.png"/>
</figure>
</div>
</section>
</div>
</li>
<li class="accessPage__mainItem accordion__item">
<div class="accessPage__mainItemTitleWrap accordion__title">
<div class="accord__plus"></div>
<p class="accessPage__mainItemTitle">会津若松駅から御宿東鳳まで</p>
</div>
<div class="accessPage__mainItemBody accordion__body">
<section class="u-articleSec">
<div class="u-articleThumb part-colum1">
<figure class="u-articleThumb__item u-pcDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4k120000434k8p7B30.png"/>
</figure>
<figure class="u-articleThumb__item u-spDb">
<img alt="" class="u-articleThumb__itemImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb11120000434k8xB7A1.png"/>
</figure>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__small"></p>
</div>
<p class="accessPage__flexImgText__label">無料シャトルバス（完全予約制）</p>
<div class="accessPage__flexImgText">
<figure class="accessPage__flexImgText__fig">
<img alt="" class="accessPage__flexImgText__img" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2u120000434k9hB8D2.jpg"/>
</figure>
<div class="accessPage__flexImgText__textWrap">
<div class="accessPage__flexImgText__text u-wysiwygArea">
<h3 style="font-weight: 400;"><span style="font-size: 14px;">「御宿東鳳」と「会津若松駅」をつなぐシャトルバスを運行しております。お気軽にご利用くださいませ。</span></h3>
<p><span style="font-size: 10px;">※事前予約で、万一ご乗車されない場合はご連絡ください。<br/>
</span><span style="font-size: 10px;">※途中乗車・途中下車は出来ません。<br/>
※当日の駅構内の状況により乗車位置は移動の場合があります。<br/>
※交通事情により上記のお時間は前後する場合がございます。<br/>
※ご利用の際にはお時間に余裕をもってお願いいたします。<br/>
※写真のシャトルバスは乗車人数により実際の車両と異なる場合がございます。<br/>
<span style="color: #c20638;">※只今、座席の間隔をあけてご乗車いただくようお願いをしております。定員になってしまった場合は、<br/>
ピストン式での運行となるため、お待ちいただく場合がございます。</span></span></p>
</div>
<div class="u-articleBtn">
</div>
</div>
</div>
<!-- 3列ー見出し（上） -->
<div class="u-contentCenter">
<div class="u-tableArea u-tableArea__bg">
<div class="u-table__wrap">
<table class="u-table u-column3Top">
<thead class="u-table__head">
<tr>
<th></th>
<th>当日お迎え【1日2便】</th>
<th>翌朝お送り【1日2便】</th>
</tr>
</thead>
<tbody class="u-table__body">
<tr>
<th rowspan="">
                                                        経路                                                    </th>
<td colspan="1" rowspan="">会津若松駅 発 → 御宿東鳳 着</td>
<td rowspan="">御宿東鳳 発 → ※会津武家屋敷 → ※飯盛山 → 会津若松駅着 <br/>
※は希望あり時のみ停車<br/>
（9：45発便のみ鶴ヶ城下車可）</td>
</tr>
<tr>
<th rowspan="">
                                                        運行予定                                                    </th>
<td colspan="1" rowspan="">14：50 ／ 15：40 <br/>
※JR等のダイヤ改正により変更になる場合がございます。</td>
<td rowspan="">8：55 ／ 9：45</td>
</tr>
<tr>
<th rowspan="">
                                                        乗車場所                                                    </th>
<td colspan="1" rowspan="">JR会津若松駅出口前（通常は白虎隊銅像付近に配車します）</td>
<td rowspan="">御宿東鳳　正面玄関前</td>
</tr>
<tr>
<th rowspan="">
                                                        所要時間                                                    </th>
<td colspan="1" rowspan="">約15分（道路状況により前後あり）</td>
<td rowspan="">約20分（道路状況により前後あり）</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<!-- 4列ー見出し（左） -->
<!-- 3列ー見出し（左） -->
<!-- 2列ー見出し（左） -->
<div class="u-contentCenter">
<p class="u-articleBlank__middle"></p>
</div>
<div class="u-wysiwygArea"><h3>まちなか周遊バス</h3>
<p>まちなか周遊バス「ハイカラさん」「あかべぇ」は、会津若松市街の観光に便利な周遊バスです。<br/>
会津のシンボル「鶴ヶ城」や、白虎隊臨終の舞台、「飯盛山」、情緒溢れる町並みが魅力の「七日町」など、会津の歴史や魅力を体験できるスポットを巡ります。「ハイカラさん」と「あかべぇ」は、同じコースをそれぞれ左回り、右回りで周遊しています。</p>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__middle"></p>
</div>
<div class="u-contentCenter">
<div class="u-articleTextImg part-colum2">
<div class="u-articleTextImg__cont">
<figure class="u-articleTextImg__thumb">
<img alt="" class="u-articleTextImg__thumbImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb1x120000434k8zE5C4.jpg"/>
</figure>
<h4 class="u-articleTextImg__titleSmall">ハイカラさん</h4>
<div class="u-articleTextImg__text">『会津武家屋敷前』バス停、または『東山温泉駅』バス停にて下車。<br/>
会津若松駅から約35分～40分。</div>
</div>
<div class="u-articleTextImg__cont">
<figure class="u-articleTextImg__thumb">
<img alt="" class="u-articleTextImg__thumbImg" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2t120000434k91128B.jpg"/>
</figure>
<h4 class="u-articleTextImg__titleSmall">あかべぇ</h4>
<div class="u-articleTextImg__text">『会津武家屋敷前』バス停、または『東山温泉駅』バス停にて下車。<br/>
会津若松駅から約10分。</div>
</div>
</div>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__middle"></p>
</div>
<!-- 3列ー見出し（上） -->
<!-- 4列ー見出し（左） -->
<!-- 3列ー見出し（左） -->
<!-- 2列ー見出し（左） -->
<div class="u-contentCenter">
<p class="u-table__title">運賃（1回乗車につき）</p>
<div class="u-tableArea u-tableArea__bg">
<div class="u-table__wrap">
<table class="u-table u-column2">
<tbody class="u-table__body">
<tr>
<th rowspan="1">
                                                        大人                                                    </th>
<td rowspan="1">210円</td>
</tr>
<tr>
<th rowspan="1">
                                                        小学生以下                                                    </th>
<td rowspan="1">110円</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__small"></p>
</div>
<div class="u-wysiwygArea"><h6><span style="color: #c20638;">※当館でも1日フリー乗車券を発売中</span></h6>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__middle"></p>
</div>
<div class="u-contentCenter">
<div class="u-articleBtn">
<p class="u-button__arrow">
<a class="u-button__arrowText" href="https://www.aizubus.com/rosen/machinaka-shuyu" target="_blank"><span>ハイカラさん・あかべぇの<br/>詳しい情報はこちら</span></a>
</p>
</div>
</div>
<div class="u-contentCenter">
<p class="u-articleBlank__large"></p>
</div>
<p class="accessPage__flexImgText__label">無料送迎バス <br class="u-spDb"/>（最寄りのバス停から当館まで）</p>
<div class="accessPage__flexImgText">
<figure class="accessPage__flexImgText__fig">
<img alt="" class="accessPage__flexImgText__img" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3p120000434k947C5F.jpg"/>
</figure>
<div class="accessPage__flexImgText__textWrap">
<div class="accessPage__flexImgText__text u-wysiwygArea">
<p>最寄りのバス停まで、随時お迎えにあがります。</p>
<p> </p>
<p>バス停「会津武家屋敷前」、もしくは「東山温泉駅」にお着きになりましたら、お電話にてご連絡ください。通常5分程でお迎えにあがります。</p>
<p> </p>
<p>なお、翌朝上記バス停までは随時お送りしておりますので、ご希望の方はフロントまでお申し付けください。</p>
<p> </p>
<h6>※当日の状況によりお待ちいただく時間は前後する場合がございます。</h6>
<p> </p>
<h3>無料送迎バスご連絡先</h3>
<p>御宿東鳳（おんやど とうほう）<br/>
電話番号：0242-26-4141</p>
</div>
<div class="u-articleBtn">
</div>
</div>
</div>
</section>
</div>
</li>
<li class="accessPage__mainItem accordion__item">
<div class="accessPage__mainItemTitleWrap accordion__title">
<div class="accord__plus"></div>
<p class="accessPage__mainItemTitle">タクシー</p>
</div>
<div class="accessPage__mainItemBody accordion__body">
<section class="u-articleSec">
<div class="u-wysiwygArea"><p>会津若松駅前ロータリーよりお乗りください。</p>
</div>
<!-- 3列ー見出し（上） -->
<!-- 4列ー見出し（左） -->
<!-- 3列ー見出し（左） -->
<!-- 2列ー見出し（左） -->
<div class="u-contentCenter">
<div class="u-tableArea u-tableArea__bg">
<div class="u-table__wrap">
<table class="u-table u-column2">
<tbody class="u-table__body">
<tr>
<th rowspan="1">
                                                        所要時間                                                    </th>
<td rowspan="1">約15分</td>
</tr>
<tr>
<th rowspan="1">
                                                        料金                                                    </th>
<td rowspan="1">約2,000円</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
</section>
</div>
</li>
</ul>
</div>
</section>
<!-- //////////////////// SDGs START //////////////////// -->
<div class="anchorLinkSec__imgArea">
<a class="anchorLinkSec__imgAreaLink" href="https://www.orixhotelsandresorts.com/sustainability/" target="_blank">
<img alt="" class="u-pcDb" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb5h120000434k979093.jpg"/>
<img alt="" class="u-spDb" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3q120000434k9j85D2.jpg"/>
</a>
</div>
<!--  Banner START -->
<div class="bnrArea">
<div class="u-contentCenter">
<ul class="bnrArea__list">
<li class="bnrArea__listItem">
<a class="bnrArea__listItemLink" href="https://www.toho-wedding.jp/" target="_blank">
<img alt="" class="anchorLinkSec__img u-pcDb" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb5g120000434k8r7749.jpg"/>
<img alt="" class="anchorLinkSec__img u-spDb" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4m120000434k9l6479.jpg"/>
</a>
</li>
</ul>
</div>
</div>
<!-- Banner END -->
<!-- //////////////////// SNS Area START //////////////////// -->
<article class="snsSecArea">
<div class="u-contentCenter">
<ul class="snsSecArea__list">
<li class="snsSecArea__item">
<a class="snsSecArea__link" href="https://www.instagram.com/onyado_toho/" rel="noopener" target="_blank"><img alt="instagram" src="https://toho.orixhotelsandresorts.com/_assets/images/layout/header_snsSecArea_instagram.png"/></a>
</li>
</ul>
</div>
</article>
<!-- //////////////////// SNS Area END //////////////////// -->
<section class="anchorLinkSec">
<div class="u-contentCenter">
<!-- //////////////////// Link Button Area START //////////////////// -->
<ul class="anchorLinkSec__flexWrap">
<li class="anchorLinkSec__flexItem">
<div class="u-button__arrow">
<a class="u-button__arrowText talkappi-faq-icon" href="javascript:void(0);">よくあるご質問</a>
</div>
</li>
<li class="anchorLinkSec__flexItem">
<div class="u-button__arrow">
<a class="u-button__arrowText" href="https://toho.orixhotelsandresorts.com/contents/contents-3101/" target="_self">東鳳の<br class="u-spDb"/>おもてなし</a>
</div>
</li>
<li class="anchorLinkSec__flexItem">
<div class="u-button__arrow">
<a class="u-button__arrowText" href="https://toho.orixhotelsandresorts.com/contents/contents-3290/" target="_self">団体・ご宴会</a>
</div>
</li>
<li class="anchorLinkSec__flexItem">
<div class="u-button__arrow">
<a class="u-button__arrowText" href="https://inquiry.talkappi.com/?f=onyado-toho-hp&amp;id=2260060001" target="_self">お問い合わせ</a>
</div>
</li>
<li class="anchorLinkSec__flexItem">
<div class="u-button__arrow">
<a class="u-button__arrowText" href="https://rcdp.tour-list.com/rc/search/?hotelcode=FKS44" target="_blank">レンタカー<br class="u-spDb"/>セットのご予約</a>
</div>
</li>
</ul>
<!-- //////////////////// Link Button Area END //////////////////// -->
<!-- Cleanliness START -->
<!-- Cleanliness END -->
</div>
</section>
</main>
<script charset="" id="" type="text/javascript">!function(d,g,e){d.TiktokAnalyticsObject=e;var a=d[e]=d[e]||[];a.methods="page track identify instances debug on off once ready alias group enableCookie disableCookie holdConsent revokeConsent grantConsent".split(" ");a.setAndDefer=function(b,c){b[c]=function(){b.push([c].concat(Array.prototype.slice.call(arguments,0)))}};for(d=0;d<a.methods.length;d++)a.setAndDefer(a,a.methods[d]);a.instance=function(b){b=a._i[b]||[];for(var c=0;c<a.methods.length;c++)a.setAndDefer(b,a.methods[c]);return b};a.load=
function(b,c){var f="https://analytics.tiktok.com/i18n/pixel/events.js";a._i=a._i||{};a._i[b]=[];a._i[b]._u=f;a._t=a._t||{};a._t[b]=+new Date;a._o=a._o||{};a._o[b]=c||{};c=document.createElement("script");c.type="text/javascript";c.async=!0;c.src=f+"?sdkid\x3d"+b+"\x26lib\x3d"+e;b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(c,b)};a.load("CQK8AMRC77U2CBNGTUKG");a.page()}(window,document,"ttq");</script>
<script charset="" id="" type="text/javascript">(function(a,c,e){var b="microAdUniverseTracker";a[b]=a[b]||{};a[b].track=a[b].track||function(){(a[b].queue=a[b].queue||[]).push(arguments)};var d=c.createElement("script");d.async=!0;d.src=e;c=c.getElementsByTagName("script")[0];c.parentNode.insertBefore(d,c)})(window,document,"https://cdn.microad.jp/js/track.js");microAdUniverseTracker.track({service_id:11104});</script><iframe height="0" style="display: none; visibility: hidden;" width="0"></iframe><script charset="" id="" type="text/javascript">var _fout_queue=_fout_queue||{};_fout_queue.segment===void 0&&(_fout_queue.segment={});_fout_queue.segment.queue===void 0&&(_fout_queue.segment.queue=[]);_fout_queue.segment.queue.push({user_id:54311});(function(){var a=document.createElement("script");a.type="text/javascript";a.async=!0;a.src="https://js.fout.jp/segmentation.js";var b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(a,b)})();</script><iframe height="0" id="universe_cookie_sync" src="https://cache.send.microad.jp/js/universe_cookie_sync.html" style="position:absolute; top:-9999px; left: -9999px; border-style: none" width="0"></iframe><!-- siteContent -->
<!-- //////////////////// CONTENT END //////////////////// -->
<!-- //////////////////// FOOTER START //////////////////// -->
<footer class="siteFooter">
<div class="footCommonLineWrap">
<span class="footCommonLine"></span>
<span class="footCommonLine"></span>
<span class="footCommonLine"></span>
</div>
<div class="siteFooter__inner">
<div class="toTop js-toTop ankLink js-hide">
<a class="toTop__text" href="#">Page top</a>
</div>
<div class="siteFooter__linkListWrap">
<p class="siteFooter__ttl u-pcDb">ORIX HOTELS &amp; RESORTSが展開する施設ブランド</p>
<p class="siteFooter__ttl u-spDb">ORIX HOTELS &amp; RESORTSが<br/>
展開する施設ブランド</p>
<ul class="siteFooter__linkList">
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://izusan-karaku.jp" target="_blank">熱海・伊豆山 佳ら久</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.gora-karaku.jp/" target="_blank">箱根・強羅 佳ら久</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://ashinoko-hanaori.orixhotelsandresorts.com/" target="_blank"> 箱根・芦ノ湖 はなをり</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.banso.co.jp/" target="_blank"> 函館・湯の川温泉 ホテル万惣</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="/" target="_self"> 会津・東山温泉 御宿 東鳳</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.unazuki-yamanoha.jp/" target="_blank">黒部・宇奈月温泉 やまのは  </a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://suginoi.orixhotelsandresorts.com/" target="_blank">別府温泉 杉乃井ホテル </a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.crosshotel.com/sapporo/" target="_blank">クロスホテル札幌</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.crosshotel.com/kyoto/" target="_blank">クロスホテル京都</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.crosshotel.com/osaka/" target="_blank">クロスホテル大阪</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://crosslife-hakatatenjin.orixhotelsandresorts.com/" target="_blank">クロスライフ博多天神</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://crosslife-hakatayanagibashi.orixhotelsandresorts.com/" target="_blank">クロスライフ博多柳橋</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://universalport.orixhotelsandresorts.com/" target="_blank">ホテル ユニバーサル ポート / ホテル ユニバーサル ポート ヴィータ</a>
</li>
</ul>
</div>
<figure class="siteFooter__logoArea">
<!-- <a href="https://toho.orixhotelsandresorts.com/" class="siteFooter__logoAreaLink"> -->
<a class="siteFooter__logoAreaLink" href="https://toho.orixhotelsandresorts.com/">
<img alt="アクセス" src="https://toho.orixhotelsandresorts.com/uploads/2022/12/footer_logo_toho.png"/>
</a>
</figure>
<p class="siteFooter__address u-pcDb">会津・東山温泉 御宿 東鳳　〒965-0813 福島県会津若松市東山町大字石山字院内706<br/>TEL ：0242-26-4141　受付時間 9:00～19:00</p>
<p class="siteFooter__address u-spDb">会津・東山温泉 御宿 東鳳<br/>〒965-0813<br/>福島県会津若松市東山町大字石山字院内706<br/><a class="siteFooter__addressTel" href="tel:0242-26-4141">TEL ：0242-26-4141</a><br/>受付時間 9:00～19:00</p>
<ul class="siteFooter__linkList">
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://orixhotelsandresorts.com/sitepolicy/" target="_blank">サイトポリシー</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.orix-realestate.co.jp/hotelmanagement/privacypolicy.html" target="_blank">プライバシーポリシー</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://toho.orixhotelsandresorts.com/specific_commercial_transactions/" target="_self">特定商取引法に基づく表記</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.orixhotelsandresorts.com/sns_guideline/" target="_blank">SNSガイドライン</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://toho.orixhotelsandresorts.com/provisions/" target="_self">宿泊約款</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://toho.orixhotelsandresorts.com/usagerules/" target="_self">利用規則</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.orix-realestate.co.jp/hotelmanagement/company/profile.html" target="_blank">会社概要</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="http://orix-realestate.co.jp/hotelmanagement/antisocial.html" target="_blank">反社会的勢力に対する基本方針</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://toho.orixhotelsandresorts.com/allergy/" target="_self">食物アレルギーをお持ちのお客さまへ</a>
</li>
<li class="siteFooter__linkItem">
<a class="siteFooter__link" href="https://www.orix-realestate.co.jp/hotelmanagement/customer-harassment-policy.html" target="_blank">カスタマーハラスメントへの対応方針</a>
</li>
</ul>
<small class="siteFooter__copyRight">COPYRIGHT © Onyado-Toho. All Rights Reserved.</small>
</div>
</footer>
<!-- //////////////////// FOOTER END //////////////////// -->
<script id="jquery-js" src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js?ver=3.6.0" type="text/javascript"></script>
<script id="imageMap-js" src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-rwdImageMaps/1.6/jquery.rwdImageMaps.min.js?ver=1.6" type="text/javascript"></script>
<script id="imageMapResizer-js" src="https://cdnjs.cloudflare.com/ajax/libs/image-map-resizer/1.0.10/js/imageMapResizer.min.js?ver=1.0.10" type="text/javascript"></script>
<script id="wow-js" src="https://toho.orixhotelsandresorts.com/_assets/js/lib/wow/wow.min.js?ver=1.0" type="text/javascript"></script>
<iframe allow="join-ad-interest-group" data-load-time="1754392336381" data-tagging-id="AW-*********" height="0" src="https://td.doubleclick.net/td/rul/*********?random=1754392336359&amp;cv=11&amp;fst=1754392336359&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5840h2z86579748za200zb6579748zd6579748xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528501~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg" style="display: none; visibility: hidden;" width="0"></iframe><iframe allow="join-ad-interest-group" data-load-time="1754392336519" data-tagging-id="AW-765343266" height="0" src="https://td.doubleclick.net/td/rul/765343266?random=1754392336492&amp;cv=11&amp;fst=1754392336492&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;gtm=45be5840h2v9173965711z86579748za200zb6579748zd6579748xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528500~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg" style="display: none; visibility: hidden;" width="0"></iframe><iframe allow="join-ad-interest-group" data-load-time="1754392336530" data-tagging-id="AW-765343266/Bi5tCMysmoEaEKLs-OwC" height="0" src="https://td.doubleclick.net/td/rul/765343266?random=1754392336521&amp;cv=11&amp;fst=1754392336521&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=purchase&amp;gcl_ctr=1&amp;gtm=45be5840h2v9173965711z86579748za200zb6579748zd6579748xea&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528500~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;label=Bi5tCMysmoEaEKLs-OwC&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;value=0&amp;bttype=purchase&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;ec_mode=a&amp;fledge=1&amp;capi=1&amp;_tu=Cg&amp;em=tv.1&amp;ct_cookie_present=0" style="display: none; visibility: hidden;" width="0"></iframe><script id="swiper-js" src="https://toho.orixhotelsandresorts.com/_assets/js/lib/swiper/swiper-bundle.min.js?ver=8.2.6" type="text/javascript"></script>
<script id="fancybox-js" src="https://toho.orixhotelsandresorts.com/_assets/js/lib/fancybox/jquery.fancybox.min.js?ver=3.5.7" type="text/javascript"></script>
<script id="slick-js" src="https://toho.orixhotelsandresorts.com/_assets/js/lib/slick/slick.min.js?ver=1.8.1" type="text/javascript"></script>
<script id="jPages-js" src="https://toho.orixhotelsandresorts.com/_assets/js/lib/jPages/jPages.min.js?ver=1.0" type="text/javascript"></script>
<script id="script-js" src="https://toho.orixhotelsandresorts.com/_assets/js/ES5/bundle.js?ver=1.0" type="text/javascript"></script>
<!-- ↓chatbot 記述 -->
<script defer="defer" fid="onyado-toho-hp" id="talkappi-chat" src="https://bot.talkappi.com/assets/talkappi/talkappi.js"></script>
<!-- ↓talkappi FAQ 記述 -->
<script defer="defer" fid="onyado-toho-qa" id="talkappi-faq-js" src="https://bot.talkappi.com/assets/talkappi/talkappi-faq.js"></script>
<iframe src="https://js.fout.jp/beacon.html?from=dmp" style="display: none;"></iframe><div style="display: none; visibility: hidden;"><script async="null" src="//cdn.evgnet.com/beacon/e55653z555563kl3t3d3l3d3n8091554137/engage/scripts/evergage.min.js" type="text/javascript"></script></div><div style="display: none; visibility: hidden;"><script async="null" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js"></script>
<script>window.yjDataLayer=window.yjDataLayer||[];function ytag(){yjDataLayer.push(arguments)}ytag({type:"ycl_cookie"});</script></div><div style="display: none; visibility: hidden;">
<script>!function(b,e,f,g,a,c,d){b.fbq||(a=b.fbq=function(){a.callMethod?a.callMethod.apply(a,arguments):a.queue.push(arguments)},b._fbq||(b._fbq=a),a.push=a,a.loaded=!0,a.version="2.0",a.queue=[],c=e.createElement(f),c.async=!0,c.src=g,d=e.getElementsByTagName(f)[0],d.parentNode.insertBefore(c,d))}(window,document,"script","https://connect.facebook.net/en_US/fbevents.js");fbq("init","1004757730430892");fbq("track","PageView");</script>
<noscript></noscript>
</div><div style="display: none; visibility: hidden;">
<script>(function(a,b,d){a._ltq=a._ltq||[];a._lt=a._lt||function(){a._ltq.push(arguments)};var e=location.protocol==="https:"?"https://d.line-scdn.net":"http://d.line-cdn.net",c=b.createElement("script");c.async=1;c.src=d||e+"/n/line_tag/public/release/v1/lt.js";b=b.getElementsByTagName("script")[0];b.parentNode.insertBefore(c,b)})(window,document);_lt("init",{customerType:"lap",tagId:"64481e19-4b87-4899-a988-d3c8aa482bc8"});_lt("send","pv",["64481e19-4b87-4899-a988-d3c8aa482bc8"]);</script>
<noscript></noscript>
</div><script id="mstaInit">MSTA.init();</script><div id="MSTATranslating"><div id="MSTALoaderImgBox"><div id="MSTALoaderImg"></div></div></div><div id="batBeacon205453153655" style="width: 0px; height: 0px; display: none; visibility: hidden;"><img alt="" height="0" id="batBeacon542603272327" src="https://bat.bing.com/action/0?ti=97141453&amp;tm=gtm002&amp;Ver=2&amp;mid=10e98f20-b463-486b-a4a6-97bcae587b9d&amp;bo=1&amp;sid=0f02ef4071ed11f0a866d1ad016904bb&amp;vid=0f031d2071ed11f099dfeb673df3b39c&amp;vids=1&amp;msclkid=N&amp;uach=pv%3D10.0&amp;pi=918639831&amp;lg=zh-CN&amp;sw=1920&amp;sh=1080&amp;sc=24&amp;nwd=1&amp;tl=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;p=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;r=&amp;lt=14041&amp;evt=pageLoad&amp;sv=1&amp;cdb=AQAQ&amp;rn=448326" style="width: 0px; height: 0px; display: none; visibility: hidden;" width="0"/></div><div id="msta_langArea" style="visibility:hidden"><olang></olang></div><div id="msta_footerArea" style="display:none"></div><div id="talkappi-app" style="z-index:2147483647;left:0;bottom:0 !important;position:fixed;width:100%;height:0;background-color:rgba(128, 128, 128, 0.5);transition:0.5s;-webkit-overflow-scrolling:touch !important;"><iframe allowtransparency="yes" height="100%" id="talkappi-app-frame" marginheight="0" marginwidth="0" src="" style="border:0;" width="100%"></iframe></div><iframe allow="join-ad-interest-group" data-load-time="1754392342718" data-tagging-id="AW-765343266" height="0" src="https://td.doubleclick.net/td/rul/765343266?random=1754392342710&amp;cv=11&amp;fst=1754392342710&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=form_start&amp;gtm=45be5840h2v9173965711za200zb6579748zd6579748xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=*********~*********~*********~*********~104527906~104528500~*********~*********~104948813~*********~*********~*********~*********&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Ftoho.orixhotelsandresorts.com%2Faccess%2F&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E3%82%A2%E3%82%AF%E3%82%BB%E3%82%B9%EF%BD%9C%E4%BC%9A%E6%B4%A5%E6%9D%B1%E5%B1%B1%E6%B8%A9%E6%B3%89%20%E5%BE%A1%E5%AE%BF%E6%9D%B1%E9%B3%B3%EF%BC%BB%E5%85%AC%E5%BC%8F%E3%83%BB%E6%9C%80%E5%AE%89%EF%BC%BD&amp;npa=0&amp;pscdl=noapi&amp;auid=913158677.1754392332&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;_tu=Cg&amp;data=event%3Dform_start" style="display: none; visibility: hidden;" width="0"></iframe><div class="pika-single is-hidden is-bound" style="position: static; left: auto; top: auto;"></div><div id="talkappi-app" style="z-index:2147483647;left:0;bottom:0 !important;position:fixed;width:100%;height:0;background-color:rgba(128, 128, 128, 0.5);transition:0.5s;-webkit-overflow-scrolling:touch !important;"><iframe allowtransparency="yes" height="100%" id="talkappi-app-frame" marginheight="0" marginwidth="0" src="" style="border:0;" width="100%"></iframe></div><div class="talkappi-chat-component" id="talkappi-chat-ota-price-compare" style="display: block; z-index: 9999; position: fixed; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.15) 0px 4px 20px; padding: 0px; font-family: sans-serif; overflow: hidden;"></div><div class="talkappibot"><div id="talkappi-chat-greeting" style="display:none;"><div class="talkappi-chat-menu talkappi-chat" id="talkappi-chat-menu-0" skill="" style=""><div>有什么可以帮助您的吗？</div><div class="talkappi-close-box" id="talkappi-close-box-menu" style=""><span></span></div></div></div><div class="talkappi-chat" id="talkappi-chat-icon" style="display:none;"><div id="talkappi-notification" style="display:none;"><span style="font-family: メイリオ, Meiryo, Osaka;">1</span></div><div class="talkappi-chat-minimized-icon talkappi-fade" id="talkappi-chat-minimized-minus" style="background: rgb(255, 255, 255); top: -6.66667px; right: -6.66667px;"><svg fill="none" height="16" viewbox="0 0 12 12" width="16" xmlns="http://www.w3.org/2000/svg">
<path d="M3 6H9" stroke="#84090F" stroke-linecap="square" stroke-linejoin="round" stroke-width="2"></path>
</svg>
</div></div><div id="live-chat"><iframe allow="microphone *" allowtransparency="yes" height="100%" id="talkappi-frame" marginheight="0" marginwidth="0" src="" style="border:0;height:100%;" width="100%"></iframe></div><div class="talkappi-chat-minimized talkappi-fade" style="background: rgb(132, 9, 15); color: rgb(255, 255, 255); z-index: 199; bottom: 200px; right: 0px; border-radius: 4px 0px 0px 4px;"><p class="talkappi-chat-minimized-text"><svg fill="none" height="15" viewbox="0 0 15 15" width="15" xmlns="http://www.w3.org/2000/svg">
<path clip-rule="evenodd" d="M0 3.18247C0 1.58934 1.29149 0.297852 2.88462 0.297852H12.1154C13.7085 0.297852 15 1.58934 15 3.18247V10.2857C15 11.8788 13.7085 13.1703 12.1154 13.1703H7.63715L4.68271 14.6592C4.50386 14.7493 4.29107 14.7403 4.12054 14.6352C3.95002 14.5302 3.84615 14.3443 3.84615 14.144V13.1703H2.88461C1.29149 13.1703 0 11.8788 0 10.2857V3.18247ZM4.32692 7.79785C4.80486 7.79785 5.19231 7.41041 5.19231 6.93247C5.19231 6.45453 4.80486 6.06708 4.32692 6.06708C3.84898 6.06708 3.46154 6.45453 3.46154 6.93247C3.46154 7.41041 3.84898 7.79785 4.32692 7.79785ZM8.36538 6.93247C8.36538 7.41041 7.97794 7.79785 7.5 7.79785C7.02206 7.79785 6.63462 7.41041 6.63462 6.93247C6.63462 6.45453 7.02206 6.06708 7.5 6.06708C7.97794 6.06708 8.36538 6.45453 8.36538 6.93247ZM10.6731 7.79785C11.151 7.79785 11.5385 7.41041 11.5385 6.93247C11.5385 6.45453 11.151 6.06708 10.6731 6.06708C10.1951 6.06708 9.80769 6.45453 9.80769 6.93247C9.80769 7.41041 10.1951 7.79785 10.6731 7.79785Z" fill="#FFFFFF" fill-rule="evenodd"></path>
</svg><span style="color: #FFFFFF;padding-top: 4px">聊天窗口<span></span></span></p><div class="talkappi-chat-minimized-icon talkappi-fade" id="talkappi-chat-minimized-plus" style="background: rgb(255, 255, 255); top: -15px; left: -15px;"><svg fill="none" height="16" viewbox="0 0 12 12" width="16" xmlns="http://www.w3.org/2000/svg">
<path d="M3 6H9" stroke="#84090F" stroke-linecap="square" stroke-linejoin="round" stroke-width="2"></path>
<path d="M6 3V9" stroke="#84090F" stroke-linecap="square" stroke-width="2"></path>
</svg>
</div></div></div></body></html>