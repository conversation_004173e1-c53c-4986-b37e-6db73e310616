<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html class="is-no-touchscreen" lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml"><head> <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/> <title>
		CHUAN HOUSE APARTMENT OSAKA	</title> <meta content="Chuan House Osaka, Chuan House Osaka reservation, Chuan House Osaka cheap" name="keywords"/> <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, minimal-ui" name="viewport"/> <meta content="telephone=no" name="format-detection"/> <meta content="Chuan House Osaka - Located about a 10-minute drive from National Bunraku Theater, Chuan House Osaka Apartment is within 10 minutes' drive of a top family-friendly place like …" name="description"/> <meta content="max-image-preview:large" name="robots"/> <link href="https://chuan-house.hotels-in-osaka.com/zh/" hreflang="zh" rel="alternate" title="Chinese" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/de/" hreflang="de" rel="alternate" title="Deutch" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/en/" hreflang="en" rel="alternate" title="English" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/fr/" hreflang="fr" rel="alternate" title="French" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/ja/" hreflang="ja" rel="alternate" title="Japanese" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/ko/" hreflang="ko" rel="alternate" title="Korean" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/nl/" hreflang="nl" rel="alternate" title="Dutch" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/ru/" hreflang="ru" rel="alternate" title="Russian" type="text/html"/> <link href="https://chuan-house.hotels-in-osaka.com/en/" rel="canonical"/> <meta content="ie=edge" http-equiv="x-ua-compatible"/> <link href="https://www.hotels-in-osaka.com/build/packs/css/common-3939ecc247.pack.css" rel="stylesheet" type="text/css"/> <link href="https://www.hotels-in-osaka.com/build/packs/css/htheme3306-fc6400340b.pack.css" rel="stylesheet" type="text/css"/> <script type="text/javascript">
		window.travel = window.travel || {};
		travel.opts = {
			id: '7245307766',
			track: '5i1efkSoviKnod89',
			btest:  {"step":"hotel","id":"392"} ,
			device:  3 ,
			sdom: 'https://www.hotels-in-osaka.com',
			type: 'hotel', page: 'main',
			lang_id: '1', lang: 'en',
						day: '05', month: '08', year: '2025'

		};
	</script> <script type="application/ld+json">
			{
				"@context": "https://schema.org",
				"@type": "FAQPage",
				"mainEntity": [
											{
							"@type": "Question",
							"name": "&#10067; Where is Chuan House Osaka apartment placed?",
							"acceptedAnswer": {
								"@type": "Answer",
								"text":"Chuan House Osaka apartment is set in the Uehommachi, Tennoji, Southern Osaka district, a 10-minute ride from Kuromon Fresh Food Market."
							}
							},
												{
							"@type": "Question",
							"name": "&#10067; What famous attractions are close to Chuan House Osaka apartment?",
							"acceptedAnswer": {
								"@type": "Answer",
								"text":"Popular spots near Chuan House Osaka apartment include the highly photogenic Dotonbori, Kuromon Fresh Food Market, Osaka International Peace Center, among others."
							}
							},
												{
							"@type": "Question",
							"name": "&#10067; How much does it cost to stay at Chuan House Osaka apartment?",
							"acceptedAnswer": {
								"@type": "Answer",
								"text":"The price of renting Chuan House Osaka apartment is $79."
							}
							},
												{
							"@type": "Question",
							"name": "&#10067; What is the time for check-in and check-out at the apartment?",
							"acceptedAnswer": {
								"@type": "Answer",
								"text":"Guests of the apartment may check in from 16:00, and check out until 10:00."
							}
							},
												{
							"@type": "Question",
							"name": "&#10067; Does Chuan House Osaka apartment provide free Wi-Fi?",
							"acceptedAnswer": {
								"@type": "Answer",
								"text":"The internet is provided for free at Chuan House Osaka apartment."
							}
							}
						
					]
				}
		</script> <script type="application/ld+json">
		[
							{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437661/437661639/osaka-chuan-house-apartment-photo-11.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556838/osaka-chuan-house-apartment-photo-37.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945830/osaka-chuan-house-apartment-photo-7.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556805/osaka-chuan-house-apartment-photo-23.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945782/osaka-chuan-house-apartment-photo-32.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556847/osaka-chuan-house-apartment-photo-20.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4410/441080/441080928/osaka-chuan-house-apartment-photo-21.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/5375/537581/537581988/osaka-chuan-house-apartment-photo-25.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4410/441080/441080952/osaka-chuan-house-apartment-photo-13.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/5375/537582/537582063/osaka-chuan-house-apartment-photo-2.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945317/osaka-chuan-house-apartment-photo-26.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437628/437628537/osaka-chuan-house-apartment-photo-8.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437630/437630580/osaka-chuan-house-apartment-photo-10.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4410/441072/441072426/osaka-chuan-house-apartment-photo-27.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556841/osaka-chuan-house-apartment-photo-28.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556829/osaka-chuan-house-apartment-photo-18.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437628/437628570/osaka-chuan-house-apartment-photo-9.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945353/osaka-chuan-house-apartment-photo-5.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4410/441073/441073653/osaka-chuan-house-apartment-photo-12.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945779/osaka-chuan-house-apartment-photo-6.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437627/437627346/osaka-chuan-house-apartment-photo-14.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945149/osaka-chuan-house-apartment-photo-4.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484945/484945797/osaka-chuan-house-apartment-photo-31.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556808/osaka-chuan-house-apartment-photo-38.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437627/437627379/osaka-chuan-house-apartment-photo-16.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4849/484944/484944225/osaka-chuan-house-apartment-photo-30.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556796/osaka-chuan-house-apartment-photo-15.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556814/osaka-chuan-house-apartment-photo-34.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/5375/537584/537584559/osaka-chuan-house-apartment-photo-33.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556790/osaka-chuan-house-apartment-photo-24.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4215/421556/421556823/osaka-chuan-house-apartment-photo-36.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4411/441102/441102708/osaka-chuan-house-apartment-photo-29.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437627/437627040/osaka-chuan-house-apartment-photo-22.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4376/437661/437661597/osaka-chuan-house-apartment-photo-35.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/4410/441073/441073848/osaka-chuan-house-apartment-photo-17.JPEG",
					"creditText": "Chuan House"
					},
								{
					"@context": "https://schema.org",
					"@type": "ImageObject",
					"contentUrl": "/data/Photos/OriginalPhoto/5375/537582/537582042/osaka-chuan-house-apartment-photo-19.JPEG",
					"creditText": "Chuan House"
					}
				
			]
	</script> <script type="application/ld+json">
		{
			"@context": "https://schema.org",
			"@type": "BreadcrumbList",
			"itemListElement": [
									{
						"@type": "ListItem",
						"position":"1",
						"item": {
							"name": "Osaka"
							,
							"@id":"https://www.hotels-in-osaka.com/en/"						}
						},
										{
						"@type": "ListItem",
						"position":"2",
						"item": {
							"name": "Apartments"
							,
							"@id":"https://www.hotels-in-osaka.com/en/type/apartments-3/"						}
						},
										{
						"@type": "ListItem",
						"position":"3",
						"item": {
							"name": "Chuan House Osaka Apartment"
													}
						}
									]
			}
	</script> <script type="application/ld+json">
		{
		"@type": "Hotel",
					"review": [
									{
						"@type": "Review",
						"author": {
							"@type": "Person",
							"name": "Calvin"
						},
						"datePublished": "2022-05-04",
						"reviewBody": "everything was perfect, the apartment was cute! overall had such wonderful stay at the property; location and rooms were very good. it had an exceptionally comfortable bed. spacious, spacious bathroom. parking was free. relatively close to sanadayama sanko shrine, walkable.Something as tv missing. ",
						"reviewRating": {
							"@type": "Rating",
							"bestRating": "10",
							"ratingValue": "6",
							"worstRating": "1"
						}
						},
										{
						"@type": "Review",
						"author": {
							"@type": "Person",
							"name": "Joe"
						},
						"datePublished": "2021-12-11",
						"reviewBody": "Cozy, neat apartment with a beautiful garden. The ocean view here is impressive...",
						"reviewRating": {
							"@type": "Rating",
							"bestRating": "10",
							"ratingValue": "7",
							"worstRating": "1"
						}
						},
										{
						"@type": "Review",
						"author": {
							"@type": "Person",
							"name": "William"
						},
						"datePublished": "2021-01-23",
						"reviewBody": "Fantastic view, absolutely good location. Free wifi was a plus. Beds were comfy and it had a kitchen. Nothing everything was great. ",
						"reviewRating": {
							"@type": "Rating",
							"bestRating": "10",
							"ratingValue": "7",
							"worstRating": "1"
						}
						}
					
				],
							"address": {
									"addressCountry": "Japan",
													"addressRegion": "Osaka",
													"addressLocality": "1-5 Higashiobase",
													"streetAddress": "1-5 Higashiobase",
													"postalCode": "537-0024",
								"@type": "PostalAddress"
			},
							"image": "/data/Photos/OriginalPhoto/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG",
										"description": "Located about a 10-minute drive from National Bunraku Theater, Chuan House Osaka Apartment is within 10 minutes&#039; drive of a top family-friendly place like Osaka Tennoji Zoo.",
										"priceRange" : "From US$ 79",
						"@context": "https://schema.org",
			"name": "Chuan House Osaka Apartment",
						"starRating": {
				"@type": "Rating",
				"ratingValue": "0"
			},

													"hasMap": "https://www.google.com/maps/place/34.67369,135.53606/@34.67369,135.53606,17z",
										"checkinTime": "16:00-23:59",
										"checkoutTime": "09:00-10:00",
						"url": "https://chuan-house.hotels-in-osaka.com/en/"
		}
</script> <script type="application/ld+json">
		{
			"@context": "https://schema.org",
			"@type": "WebSite",
			"name": "Hotelsinosaka",
						"alternateName": [
									"HotelsInOsaka"
					,	
									"Hotels-In-Osaka"
					,	
									"Hotels In Osaka"
						
				
			],
						"url": "https://www.hotels-in-osaka.com"
		}
	</script> </head> <body> <div class="sidr-menu"> <div class="menu-logo"> <span>
											Hotels In <span>Osaka</span> </span> <div class="show-mob js-header-menu-btn mobile-menu-btn">
					Close
				</div> </div> <ul class="clearfix"> <li class="show-mob mobile-select-language"> <div class="mobile-select-title"> <i class="icn-sprite icn-h-lang-en"></i> </div> <div class="mobile-select-wrap"> <select class="js-hide-select"> <option value="L2FyLw==">
										العربية
									</option> <option value="L3poLw==">
										简体中文
									</option> <option value="L2NzLw==">
										Čeština
									</option> <option value="L2RhLw==">
										Dansk
									</option> <option value="L2RlLw==">
										Deutsch
									</option> <option selected="" value="L2VuLw==">
										English
									</option> <option value="L2VzLw==">
										Español
									</option> <option value="L2ZyLw==">
										Français
									</option> <option value="L2VsLw==">
										Ελληνικά
									</option> <option value="L2hlLw==">
										עברית
									</option> <option value="L2l0Lw==">
										Italiano
									</option> <option value="L2phLw==">
										日本語
									</option> <option value="L2tvLw==">
										한국어
									</option> <option value="L2h1Lw==">
										Magyar
									</option> <option value="L25sLw==">
										Nederlands
									</option> <option value="L25vLw==">
										Norsk
									</option> <option value="L3BsLw==">
										Polski
									</option> <option value="L3B0Lw==">
										Português
									</option> <option value="L3J1Lw==">
										Русский
									</option> <option value="L3N2Lw==">
										Svenska
									</option> <option value="L3RyLw==">
										Türkçe
									</option> <option value="L3VrLw==">
										Українська
									</option> </select> </div> </li> <li class="mobile-select-currency"> <div class="mobile-select-title">
							USD
						</div> <div class="mobile-select-wrap"> <select class="js-hide-select"> <option selected="" value="Lz9jdXJyZW5jeT0x">
										U.S. dollar (USD)
									</option> <option value="Lz9jdXJyZW5jeT0z">
										Euro (EUR)
									</option> <option value="Lz9jdXJyZW5jeT03">
										Pound sterling (GBP)
									</option> <option value="Lz9jdXJyZW5jeT0xMQ==">
										Australian dollar (AUD)
									</option> <option value="Lz9jdXJyZW5jeT0yMQ==">
										Canadian dollar (CAD)
									</option> <option value="Lz9jdXJyZW5jeT00OA==">
										Brazilian real (BRL)
									</option> <option value="Lz9jdXJyZW5jeT00OQ==">
										Bulgarian lev (BGN)
									</option> <option value="Lz9jdXJyZW5jeT01Nw==">
										Chinese yuan (CNY)
									</option> <option value="Lz9jdXJyZW5jeT02Ng==">
										Czech koruna (CZK)
									</option> <option value="Lz9jdXJyZW5jeT00">
										Danish krone (DKK)
									</option> <option value="Lz9jdXJyZW5jeT0xMw==">
										Hong Kong dollar (HKD)
									</option> <option value="Lz9jdXJyZW5jeT03Nw==">
										Israeli new sheqel (ILS)
									</option> <option value="Lz9jdXJyZW5jeT0yMw==">
										Japanese yen (JPY)
									</option> <option value="Lz9jdXJyZW5jeT0xMA==">
										Malaysian ringgit (MYR)
									</option> <option value="Lz9jdXJyZW5jeT0xMg==">
										New Zealand dollar (NZD)
									</option> <option value="Lz9jdXJyZW5jeT0yMg==">
										Norwegian krone (NOK)
									</option> <option value="Lz9jdXJyZW5jeT0xMzU=">
										Polish zloty (PLN)
									</option> <option value="Lz9jdXJyZW5jeT0xMzY=">
										Romanian leu (RON)
									</option> <option value="Lz9jdXJyZW5jeT0xMzc=">
										Russian ruble (RUB)
									</option> <option value="Lz9jdXJyZW5jeT0xNDI=">
										Saudi riyal (SAR)
									</option> <option value="Lz9jdXJyZW5jeT0yNA==">
										Singapore dollar (SGD)
									</option> <option value="Lz9jdXJyZW5jeT0xNQ==">
										South African rand (ZAR)
									</option> <option value="Lz9jdXJyZW5jeT0xNTE=">
										South Korean won (KRW)
									</option> <option value="Lz9jdXJyZW5jeT05">
										Swedish krona (SEK)
									</option> <option value="Lz9jdXJyZW5jeT04">
										Swiss franc (CHF)
									</option> <option value="Lz9jdXJyZW5jeT0xNjI=">
										Turkish lira (TRY)
									</option> <option value="Lz9jdXJyZW5jeT0xNjY=">
										Ukrainian hryvnia (UAH)
									</option> <option value="Lz9jdXJyZW5jeT0y">
										Thai baht (THB)
									</option> </select> </div> </li> <li class="menu-static-link"> <table> <thead> <tr> <td><a href="/en/#write-review">Reviews</a> </td> </tr> <tr> <td><a href="/en/#contact">Contact Us</a></td> </tr> </thead> </table> </li> </ul> </div> <div class="js-page-name page-main"> <div class="header"> <div class="header-top"> <div class="container clearfix"> <div class="show-mob js-back-btn back-btn-wrap"> <i class="icn-sprite icn-header-menu-back"></i> </div> <div class="header-logo pull-left"> <a class="header-logo__link content" href="https://www.hotels-in-osaka.com/"> <span><span>Hotel</span>s</span>-In-Osaka.com
							</a> </div> <div class="header-top-right clearfix"> <ul class="top-nav-right__menu js-menu hide-mob pull-left"> <li class="header-group-booking"> <span class="header-group-booking-btn js-menu-span"> <span class="js-trackevent-groupbooking-link js-new-window" data-new-window="I2NvbnRhY3Q="> <svg class="icon header-group-booking-icon"> <use xlink:href="/build/sprites/img/sprite.svg#icon-group-4"></use> </svg>

											Group booking
										</span> </span> </li> <li class="js-menu-item"><span class="js-menu-span">Find a hotel <i class="icn-sprite icn-white-arr-bot"></i></span> <ul class="header-menu-content"> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS81LXN0YXJzLw==">5-star hotels
																<span class="counter">(36)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS80LXN0YXJzLw==">4-star hotels
																<span class="counter">(217)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS8zLXN0YXJzLw==">3-star hotels
																<span class="counter">(495)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS8yLXN0YXJzLw==">2-star hotels
																<span class="counter">(289)</span> </span> </li> <li> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS90eXBlL3ZpbGxhcy0yNC8=">Villas in Osaka
																<span class="counter">(370)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS90eXBlL2FwYXJ0bWVudHMtMy8=">Apartments in Osaka
																<span class="counter">(3300)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS90eXBlL2hvc3RlbHMtMTEv">Hostels in Osaka
																<span class="counter">(152)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS90eXBlL2JlZC1hbmQtYnJlYWtmYXN0cy00Lw==">Bed and breakfasts in Osaka
																<span class="counter">(2)</span> </span> </li> <li> <span class="item-link js-delete-cookie js-link js-link-new" data-cookie="use_dates" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS90eXBlL2hvbGlkYXktaG9tZXMtMzMv">Holiday rentals in Osaka
																<span class="counter">(614)</span> </span> </li> </ul> </li> <li class=""> <span class="menu-help-btn js-menu-span"><span class="js-link js-link-new" data-internal="I2NvbnRhY3Q=">Help</span> </span> </li> </ul> <div class="clearfix hide-mob pull-left"> <div class="mutable-lang-block pull-right clearfix"> <div class="currency-block __lang-block"> <div class="lang-item"> <span class="lang-item-title style-uppercase">
																									USD																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																										</span> <i class="icn-white-arr-bot icn-sprite __arr-bottom"></i> </div> <div class="lang-item__hide js-trackevent-currency"> <span class="js-link js-link-new active language-item" data-internal="Lz9jdXJyZW5jeT0x">
													U.S. dollar (USD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0z">
													Euro (EUR)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT03">
													Pound sterling (GBP)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMQ==">
													Australian dollar (AUD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0yMQ==">
													Canadian dollar (CAD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT00OA==">
													Brazilian real (BRL)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT00OQ==">
													Bulgarian lev (BGN)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT01Nw==">
													Chinese yuan (CNY)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT02Ng==">
													Czech koruna (CZK)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT00">
													Danish krone (DKK)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMw==">
													Hong Kong dollar (HKD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT03Nw==">
													Israeli new sheqel (ILS)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0yMw==">
													Japanese yen (JPY)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMA==">
													Malaysian ringgit (MYR)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMg==">
													New Zealand dollar (NZD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0yMg==">
													Norwegian krone (NOK)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMzU=">
													Polish zloty (PLN)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMzY=">
													Romanian leu (RON)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xMzc=">
													Russian ruble (RUB)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xNDI=">
													Saudi riyal (SAR)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0yNA==">
													Singapore dollar (SGD)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xNQ==">
													South African rand (ZAR)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xNTE=">
													South Korean won (KRW)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT05">
													Swedish krona (SEK)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT04">
													Swiss franc (CHF)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xNjI=">
													Turkish lira (TRY)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0xNjY=">
													Ukrainian hryvnia (UAH)
												</span> <span class="js-link js-link-new language-item" data-internal="Lz9jdXJyZW5jeT0y">
													Thai baht (THB)
												</span> </div> </div> <div class="language-block __lang-block"> <div class="lang-item"> <i class="icn-sprite icn-h-lang-en"></i> <span class="lang-item-title style-uppercase">
																																																																																																																																																						 en 																																																																																																																																																																																																																																																																																																																																																																																																																																							</span> <i class="icn-white-arr-bot icn-sprite __arr-bottom"></i> </div> <div class="lang-item__hide js-trackevent-language"> <span class="js-link language-item js-link-new" data-internal="L2FyLw==" data-name="العربية"> <i class="icn-sprite icn-h-lang-ar"></i> <span>العربية</span> </span> <a class="js-link language-item" data-name="简体中文" href="/zh/"> <i class="icn-sprite icn-h-lang-zh"></i> <span>简体中文</span> </a> <span class="js-link language-item js-link-new" data-internal="L2NzLw==" data-name="Čeština"> <i class="icn-sprite icn-h-lang-cs"></i> <span>Čeština</span> </span> <span class="js-link language-item js-link-new" data-internal="L2RhLw==" data-name="Dansk"> <i class="icn-sprite icn-h-lang-da"></i> <span>Dansk</span> </span> <a class="js-link language-item" data-name="Deutsch" href="/de/"> <i class="icn-sprite icn-h-lang-de"></i> <span>Deutsch</span> </a> <a class="js-link active language-item" data-name="English" href="/en/"> <i class="icn-sprite icn-h-lang-en"></i> <span>English</span> </a> <span class="js-link language-item js-link-new" data-internal="L2VzLw==" data-name="Español"> <i class="icn-sprite icn-h-lang-es"></i> <span>Español</span> </span> <a class="js-link language-item" data-name="Français" href="/fr/"> <i class="icn-sprite icn-h-lang-fr"></i> <span>Français</span> </a> <span class="js-link language-item js-link-new" data-internal="L2VsLw==" data-name="Ελληνικά"> <i class="icn-sprite icn-h-lang-el"></i> <span>Ελληνικά</span> </span> <span class="js-link language-item js-link-new" data-internal="L2hlLw==" data-name="עברית"> <i class="icn-sprite icn-h-lang-he"></i> <span>עברית</span> </span> <span class="js-link language-item js-link-new" data-internal="L2l0Lw==" data-name="Italiano"> <i class="icn-sprite icn-h-lang-it"></i> <span>Italiano</span> </span> <a class="js-link language-item" data-name="日本語" href="/ja/"> <i class="icn-sprite icn-h-lang-ja"></i> <span>日本語</span> </a> <a class="js-link language-item" data-name="한국어" href="/ko/"> <i class="icn-sprite icn-h-lang-ko"></i> <span>한국어</span> </a> <span class="js-link language-item js-link-new" data-internal="L2h1Lw==" data-name="Magyar"> <i class="icn-sprite icn-h-lang-hu"></i> <span>Magyar</span> </span> <a class="js-link language-item" data-name="Nederlands" href="/nl/"> <i class="icn-sprite icn-h-lang-nl"></i> <span>Nederlands</span> </a> <span class="js-link language-item js-link-new" data-internal="L25vLw==" data-name="Norsk"> <i class="icn-sprite icn-h-lang-no"></i> <span>Norsk</span> </span> <span class="js-link language-item js-link-new" data-internal="L3BsLw==" data-name="Polski"> <i class="icn-sprite icn-h-lang-pl"></i> <span>Polski</span> </span> <span class="js-link language-item js-link-new" data-internal="L3B0Lw==" data-name="Português"> <i class="icn-sprite icn-h-lang-pt"></i> <span>Português</span> </span> <a class="js-link language-item" data-name="Русский" href="/ru/"> <i class="icn-sprite icn-h-lang-ru"></i> <span>Русский</span> </a> <span class="js-link language-item js-link-new" data-internal="L3N2Lw==" data-name="Svenska"> <i class="icn-sprite icn-h-lang-sv"></i> <span>Svenska</span> </span> <span class="js-link language-item js-link-new" data-internal="L3RyLw==" data-name="Türkçe"> <i class="icn-sprite icn-h-lang-tr"></i> <span>Türkçe</span> </span> <span class="js-link language-item js-link-new" data-internal="L3VrLw==" data-name="Українська"> <i class="icn-sprite icn-h-lang-uk"></i> <span>Українська</span> </span> </div> </div> </div> </div> </div> <div class="show-mob js-header-menu-btn mobile-menu-btn pull-right">
							Menu
						</div> </div> </div> <div class="header-center proxy-container"> <div class="header-center__top clearfix container"> <div class="pull-left hotel-name-wrap" id="hn"> <div class="hotel-title"> <h1>
																			Apartment Chuan House Osaka
																	</h1> <div class="starts-hotel-name show-mob"> <div class="hotel-stars m-t-10 m-hotel-star-0"> </div> </div> <div class="hotel-info"> <p class="hotel-address">1-5 Higashiobase, Osaka, Japan</p> </div> <div class="show-mob mob-btn-map"> <a href="#map">Map</a> </div> </div> <div class="starts-hotel-name hide-mob"> <div class="hotel-stars m-t-10 hotel-star-0"> </div> </div> </div> <div class="hotel-center__icon"> <svg fill="none" height="89" viewbox="0 0 128 150" width="86" xmlns="http://www.w3.org/2000/svg"> <path d="M63.0996 0.65625C63.7418 0.473482 64.4177 0.450834 65.0684 0.587891L65.3457 0.65625L124.167 18.3203L124.17 18.3213C124.971 18.5557 125.674 19.0433 126.175 19.7109C126.675 20.3787 126.945 21.191 126.945 22.0254V86.7715C126.945 94.7798 123.783 103.015 117.529 111.271L116.915 112.068C111.824 118.556 104.771 125.117 95.9219 131.513H95.9209C81.0486 142.294 66.4068 148.899 65.8125 149.153L65.8008 149.159C65.3079 149.386 64.7709 149.5 64.2285 149.494H64.2178C63.6768 149.499 63.1413 149.387 62.6475 149.166H62.6484C52.0919 144.21 42.0083 138.304 32.5234 131.519H32.5244C23.7024 125.121 16.6204 118.591 11.5303 112.075V112.074L10.9131 111.275C4.63525 103.021 1.50001 94.7863 1.5 86.7773V22.0625C1.5033 21.2236 1.77506 20.4078 2.27539 19.7344C2.77569 19.0611 3.47812 18.5646 4.28027 18.3193L63.0996 0.65625ZM119.215 24.9697L118.858 24.8633L81.9443 13.7891L64.3955 8.51562L64.3252 8.49414H64.1494L64.0791 8.51562L9.64551 24.834L9.28906 24.9404V86.8125C9.28906 87.0465 9.29328 87.2798 9.30078 87.5127L9.33398 88.21C9.77743 95.5575 13.6724 102.606 18.9346 108.893C24.2024 115.186 30.8812 120.766 36.9766 125.181C48.2307 133.32 59.6276 139.136 63.9775 141.223L64.0801 141.271H64.3369L64.4404 141.222C67.007 139.981 72.0789 137.428 78.1318 133.848L78.3809 133.702L78.3818 133.701C82.4613 131.284 86.9746 128.405 91.4688 125.151L91.4697 125.15C96.0155 121.844 100.905 117.888 105.286 113.473L106.155 112.583C113.288 105.162 118.95 96.4342 119.15 87.2773H119.215V24.9697Z" fill="#F6F6F6" stroke="#B0AFAD"></path> <path d="M119.459 24.6387V86.9473H119.43C119.43 96.2168 113.723 105.132 106.384 112.767C101.749 117.58 96.4609 121.89 91.5596 125.455C87.0156 128.745 82.4512 131.654 78.3232 134.101L78.0713 134.25C71.9394 137.854 66.8037 140.426 64.1934 141.677C59.7988 139.567 48.2676 133.685 36.8857 125.455C24.5811 116.531 9.96191 102.949 9.05371 88.3623C9.02441 87.8994 9.00977 87.4395 9.00977 86.9766V24.6035L64.2227 8.05957L82.0205 13.4092L119.459 24.6387Z" fill="#EBEBEB"></path> <path d="M119.459 24.542V86.8506H119.433C119.433 96.1201 113.726 105.035 106.384 112.67C106.033 113.039 105.669 113.405 105.306 113.769C105.121 113.959 104.931 114.146 104.738 114.334L104.512 114.557C104.245 114.814 103.985 115.069 103.715 115.321C103.446 115.573 103.129 115.887 102.836 116.165L101.893 117.026C101.582 117.308 101.272 117.585 100.961 117.858C100.651 118.132 100.342 118.395 100.035 118.646C99.0843 119.473 98.1263 120.274 97.1614 121.052C96.8685 121.301 96.5462 121.55 96.2386 121.793C95.6907 122.229 95.1429 122.654 94.6038 123.073L93.3382 123.27L92.1282 120.135C78.9786 86.0113 75.0214 49.0327 80.6526 12.8994L82.0179 13.3096L119.459 24.542Z" fill="#D9D9D9"></path> <path d="M25.0859 101.947C23.7529 105.694 20.7851 108.064 18.4589 107.238C16.1327 106.412 15.3241 102.7 16.6572 98.9499C17.7353 95.9206 22.0917 92.6481 23.7206 91.5055C23.7906 91.4596 23.8703 91.4305 23.9535 91.4207C24.0366 91.4109 24.1209 91.4206 24.1997 91.449C24.2784 91.4775 24.3495 91.5238 24.4072 91.5845C24.4649 91.6452 24.5077 91.7184 24.5322 91.7985C25.0155 93.694 26.1611 98.9059 25.0859 101.947Z" fill="#CBAB51"></path> <path d="M12.5815 97.4356C15.0835 99.3398 15.936 102.416 14.4888 104.326C13.0415 106.236 9.83934 106.228 7.33738 104.326C5.2866 102.773 4.73582 97.5879 4.59813 95.7334C4.59497 95.6545 4.60779 95.5758 4.63581 95.502C4.66384 95.4281 4.7065 95.3608 4.76123 95.3038C4.81596 95.2469 4.88162 95.2017 4.95428 95.1708C5.02695 95.1399 5.1051 95.124 5.18406 95.124C6.68699 95.3057 10.5893 95.918 12.5815 97.4356Z" fill="#CBAB51"></path> <path d="M30.4785 117.413C29.169 121.095 26.4444 123.489 24.3936 122.759C22.3428 122.03 21.7364 118.453 23.0459 114.77C24.0742 111.881 26.5615 108.377 27.5664 107.006C27.6123 106.943 27.6737 106.894 27.7447 106.862C27.8156 106.83 27.8936 106.817 27.9711 106.824C28.0485 106.832 28.1227 106.859 28.1863 106.904C28.25 106.949 28.3009 107.009 28.334 107.079C29.2188 108.978 31.5479 114.401 30.4785 117.413Z" fill="#CBAB51"></path> <path d="M42.5583 125.73C43.3083 129.539 41.8816 133.037 39.3738 133.529C36.866 134.021 34.2234 131.332 33.4734 127.517C32.8669 124.432 35.0671 119.478 35.9343 117.694C35.9767 117.62 36.0347 117.557 36.1041 117.508C36.1736 117.459 36.2529 117.425 36.3365 117.41C36.4201 117.395 36.5061 117.398 36.5884 117.419C36.6707 117.44 36.7474 117.479 36.8132 117.533C38.2195 118.878 41.9519 122.654 42.5583 125.73Z" fill="#CBAB51"></path> <path d="M14.1253 112.11C17.0989 113.429 18.7249 116.262 17.761 118.436C16.7972 120.609 13.6097 121.304 10.6302 119.985C8.20733 118.91 6.34405 113.959 5.73467 112.178C5.71402 112.102 5.70867 112.024 5.71895 111.946C5.72922 111.869 5.75491 111.794 5.7945 111.726C5.83409 111.659 5.88678 111.6 5.94946 111.553C6.01214 111.507 6.08355 111.473 6.15948 111.454C7.71221 111.322 11.7581 111.062 14.1253 112.11Z" fill="#CBAB51"></path> <path d="M24.3769 126.246C27.7578 126.539 30.3271 128.771 30.1103 131.226C29.8935 133.681 26.9843 135.436 23.6035 135.14C20.8496 134.9 17.3339 130.684 16.124 129.126C16.0757 129.06 16.0418 128.985 16.0243 128.905C16.0068 128.825 16.0062 128.743 16.0225 128.663C16.0388 128.583 16.0716 128.507 16.1188 128.44C16.1661 128.374 16.2267 128.318 16.2968 128.276C17.7822 127.614 21.6874 126.011 24.3769 126.246Z" fill="#CBAB51"></path> <path d="M40.0354 135.931C43.7971 136.26 46.6624 138.598 46.4397 141.155C46.2171 143.713 42.9886 145.515 39.2298 145.186C36.1653 144.92 32.2337 140.499 30.8802 138.882C30.8273 138.814 30.7897 138.736 30.7703 138.653C30.7508 138.57 30.7498 138.483 30.7674 138.399C30.785 138.315 30.8207 138.236 30.8721 138.168C30.9235 138.1 30.9892 138.043 31.0647 138.003C32.7112 137.306 37.0471 135.659 40.0354 135.931Z" fill="#CBAB51"></path> <path d="M61.3844 130.922C64.2262 134.616 64.3463 139.292 61.654 141.366C58.9616 143.44 54.4674 142.125 51.6256 138.436C49.3258 135.448 49.3668 128.704 49.4665 126.231C49.4793 126.121 49.5144 126.014 49.5696 125.918C49.6248 125.821 49.699 125.737 49.7877 125.67C49.8764 125.602 49.9778 125.554 50.0857 125.527C50.1936 125.5 50.3059 125.496 50.4157 125.513C52.7711 126.105 59.0905 127.939 61.3844 130.922Z" fill="#CBAB51"></path> <path d="M18.9863 87.5505C18.3183 91.8864 15.6874 95.0768 13.1093 94.6813C10.5312 94.2858 8.98139 90.4479 9.64936 86.1266C10.206 82.5026 14.3603 77.8561 15.7109 76.4264C15.7571 76.3765 15.8151 76.3388 15.8796 76.3168C15.9441 76.2949 16.013 76.2893 16.0801 76.3006C16.1473 76.312 16.2106 76.3399 16.2643 76.3818C16.3179 76.4237 16.3603 76.4783 16.3876 76.5407C17.1874 78.2985 19.5429 83.9352 18.9863 87.5505Z" fill="#CBAB51"></path> <path d="M25.0859 101.947C23.7529 105.694 20.7851 108.064 18.4589 107.238C16.1327 106.412 15.3241 102.7 16.6572 98.9499C17.7353 95.9206 22.0917 92.6481 23.7206 91.5055C23.7906 91.4596 23.8703 91.4305 23.9535 91.4207C24.0366 91.4109 24.1209 91.4206 24.1997 91.449C24.2784 91.4775 24.3495 91.5238 24.4072 91.5845C24.4649 91.6452 24.5077 91.7184 24.5322 91.7985C25.0155 93.694 26.1611 98.9059 25.0859 101.947Z" fill="#CBAB51"></path> <path d="M12.5815 97.4356C15.0835 99.3398 15.936 102.416 14.4888 104.326C13.0415 106.236 9.83934 106.228 7.33738 104.326C5.2866 102.773 4.73582 97.5879 4.59813 95.7334C4.59497 95.6545 4.60779 95.5758 4.63581 95.502C4.66384 95.4281 4.7065 95.3608 4.76123 95.3038C4.81596 95.2469 4.88162 95.2017 4.95428 95.1708C5.02695 95.1399 5.1051 95.124 5.18406 95.124C6.68699 95.3057 10.5893 95.918 12.5815 97.4356Z" fill="#CBAB51"></path> <path d="M30.4785 117.413C29.169 121.095 26.4444 123.489 24.3936 122.759C22.3428 122.03 21.7364 118.453 23.0459 114.77C24.0742 111.881 26.5615 108.377 27.5664 107.006C27.6123 106.943 27.6737 106.894 27.7447 106.862C27.8156 106.83 27.8936 106.817 27.9711 106.824C28.0485 106.832 28.1227 106.859 28.1863 106.904C28.25 106.949 28.3009 107.009 28.334 107.079C29.2188 108.978 31.5479 114.401 30.4785 117.413Z" fill="#CBAB51"></path> <path d="M42.5583 125.73C43.3083 129.539 41.8816 133.037 39.3738 133.529C36.866 134.021 34.2234 131.332 33.4734 127.517C32.8669 124.432 35.0671 119.478 35.9343 117.694C35.9767 117.62 36.0347 117.557 36.1041 117.508C36.1736 117.459 36.2529 117.425 36.3365 117.41C36.4201 117.395 36.5061 117.398 36.5884 117.419C36.6707 117.44 36.7474 117.479 36.8132 117.533C38.2195 118.878 41.9519 122.654 42.5583 125.73Z" fill="#CBAB51"></path> <path d="M14.1253 112.11C17.0989 113.429 18.7249 116.262 17.761 118.436C16.7972 120.609 13.6097 121.304 10.6302 119.985C8.20733 118.91 6.34405 113.959 5.73467 112.178C5.71402 112.102 5.70867 112.024 5.71895 111.946C5.72922 111.869 5.75491 111.794 5.7945 111.726C5.83409 111.659 5.88678 111.6 5.94946 111.553C6.01214 111.507 6.08355 111.473 6.15948 111.454C7.71221 111.322 11.7581 111.062 14.1253 112.11Z" fill="#CBAB51"></path> <path d="M24.3769 126.246C27.7578 126.539 30.3271 128.771 30.1103 131.226C29.8935 133.681 26.9843 135.436 23.6035 135.14C20.8496 134.9 17.3339 130.684 16.124 129.126C16.0757 129.06 16.0418 128.985 16.0243 128.905C16.0068 128.825 16.0062 128.743 16.0225 128.663C16.0388 128.583 16.0716 128.507 16.1188 128.44C16.1661 128.374 16.2267 128.318 16.2968 128.276C17.7822 127.614 21.6874 126.011 24.3769 126.246Z" fill="#CBAB51"></path> <path d="M40.0354 135.931C43.7971 136.26 46.6624 138.598 46.4397 141.155C46.2171 143.713 42.9886 145.515 39.2298 145.186C36.1653 144.92 32.2337 140.499 30.8802 138.882C30.8273 138.814 30.7897 138.736 30.7703 138.653C30.7508 138.57 30.7498 138.483 30.7674 138.399C30.785 138.315 30.8207 138.236 30.8721 138.168C30.9235 138.1 30.9892 138.043 31.0647 138.003C32.7112 137.306 37.0471 135.659 40.0354 135.931Z" fill="#CBAB51"></path> <path d="M61.3844 130.922C64.2262 134.616 64.3463 139.292 61.654 141.366C58.9616 143.44 54.4674 142.125 51.6256 138.436C49.3258 135.448 49.3668 128.704 49.4665 126.231C49.4793 126.121 49.5144 126.014 49.5696 125.918C49.6248 125.821 49.699 125.737 49.7877 125.67C49.8764 125.602 49.9778 125.554 50.0857 125.527C50.1936 125.5 50.3059 125.496 50.4157 125.513C52.7711 126.105 59.0905 127.939 61.3844 130.922Z" fill="#CBAB51"></path> <path d="M18.9863 87.5505C18.3183 91.8864 15.6874 95.0768 13.1093 94.6813C10.5312 94.2858 8.98139 90.4479 9.64936 86.1266C10.206 82.5026 14.3603 77.8561 15.7109 76.4264C15.7571 76.3765 15.8151 76.3388 15.8796 76.3168C15.9441 76.2949 16.013 76.2893 16.0801 76.3006C16.1473 76.312 16.2106 76.3399 16.2643 76.3818C16.3179 76.4237 16.3603 76.4783 16.3876 76.5407C17.1874 78.2985 19.5429 83.9352 18.9863 87.5505Z" fill="#CBAB51"></path> <path d="M25.0859 101.947C23.7529 105.694 20.7851 108.064 18.4589 107.238C16.1327 106.412 15.3241 102.7 16.6572 98.9499C17.7353 95.9206 22.0917 92.6481 23.7206 91.5055C23.7906 91.4596 23.8703 91.4305 23.9535 91.4207C24.0366 91.4109 24.1209 91.4206 24.1997 91.449C24.2784 91.4775 24.3495 91.5238 24.4072 91.5845C24.4649 91.6452 24.5077 91.7184 24.5322 91.7985C25.0155 93.694 26.1611 98.9059 25.0859 101.947Z" fill="#CBAB51"></path> <path d="M12.5815 97.4356C15.0835 99.3398 15.936 102.416 14.4888 104.326C13.0415 106.236 9.83934 106.228 7.33738 104.326C5.2866 102.773 4.73582 97.5879 4.59813 95.7334C4.59497 95.6545 4.60779 95.5758 4.63581 95.502C4.66384 95.4281 4.7065 95.3608 4.76123 95.3038C4.81596 95.2469 4.88162 95.2017 4.95428 95.1708C5.02695 95.1399 5.1051 95.124 5.18406 95.124C6.68699 95.3057 10.5893 95.918 12.5815 97.4356Z" fill="#CBAB51"></path> <path d="M30.4785 117.413C29.169 121.095 26.4444 123.489 24.3936 122.759C22.3428 122.03 21.7364 118.453 23.0459 114.77C24.0742 111.881 26.5615 108.377 27.5664 107.006C27.6123 106.943 27.6737 106.894 27.7447 106.862C27.8156 106.83 27.8936 106.817 27.9711 106.824C28.0485 106.832 28.1227 106.859 28.1863 106.904C28.25 106.949 28.3009 107.009 28.334 107.079C29.2188 108.978 31.5479 114.401 30.4785 117.413Z" fill="#CBAB51"></path> <path d="M42.5583 125.73C43.3083 129.539 41.8816 133.037 39.3738 133.529C36.866 134.021 34.2234 131.332 33.4734 127.517C32.8669 124.432 35.0671 119.478 35.9343 117.694C35.9767 117.62 36.0347 117.557 36.1041 117.508C36.1736 117.459 36.2529 117.425 36.3365 117.41C36.4201 117.395 36.5061 117.398 36.5884 117.419C36.6707 117.44 36.7474 117.479 36.8132 117.533C38.2195 118.878 41.9519 122.654 42.5583 125.73Z" fill="#CBAB51"></path> <path d="M14.1253 112.11C17.0989 113.429 18.7249 116.262 17.761 118.436C16.7972 120.609 13.6097 121.304 10.6302 119.985C8.20733 118.91 6.34405 113.959 5.73467 112.178C5.71402 112.102 5.70867 112.024 5.71895 111.946C5.72922 111.869 5.75491 111.794 5.7945 111.726C5.83409 111.659 5.88678 111.6 5.94946 111.553C6.01214 111.507 6.08355 111.473 6.15948 111.454C7.71221 111.322 11.7581 111.062 14.1253 112.11Z" fill="#CBAB51"></path> <path d="M24.3769 126.246C27.7578 126.539 30.3271 128.771 30.1103 131.226C29.8935 133.681 26.9843 135.436 23.6035 135.14C20.8496 134.9 17.3339 130.684 16.124 129.126C16.0757 129.06 16.0418 128.985 16.0243 128.905C16.0068 128.825 16.0062 128.743 16.0225 128.663C16.0388 128.583 16.0716 128.507 16.1188 128.44C16.1661 128.374 16.2267 128.318 16.2968 128.276C17.7822 127.614 21.6874 126.011 24.3769 126.246Z" fill="#CBAB51"></path> <path d="M40.0354 135.931C43.7971 136.26 46.6624 138.598 46.4397 141.155C46.2171 143.713 42.9886 145.515 39.2298 145.186C36.1653 144.92 32.2337 140.499 30.8802 138.882C30.8273 138.814 30.7897 138.736 30.7703 138.653C30.7508 138.57 30.7498 138.483 30.7674 138.399C30.785 138.315 30.8207 138.236 30.8721 138.168C30.9235 138.1 30.9892 138.043 31.0647 138.003C32.7112 137.306 37.0471 135.659 40.0354 135.931Z" fill="#CBAB51"></path> <path d="M61.3844 130.922C64.2262 134.616 64.3463 139.292 61.654 141.366C58.9616 143.44 54.4674 142.125 51.6256 138.436C49.3258 135.448 49.3668 128.704 49.4665 126.231C49.4793 126.121 49.5144 126.014 49.5696 125.918C49.6248 125.821 49.699 125.737 49.7877 125.67C49.8764 125.602 49.9778 125.554 50.0857 125.527C50.1936 125.5 50.3059 125.496 50.4157 125.513C52.7711 126.105 59.0905 127.939 61.3844 130.922Z" fill="#CBAB51"></path> <path d="M64.3079 143.882C63.2102 143.882 62.1223 143.856 61.0442 143.806C52.2141 143.39 44.8489 141.333 39.7454 137.855C39.3147 137.563 38.6614 137.173 37.9026 136.722C34.3225 134.598 28.3284 131.038 22.926 124.452C16.3547 116.442 12.7307 106.364 12.1887 94.502C12.1762 94.2327 12.2169 93.9635 12.3084 93.7099C12.3999 93.4563 12.5405 93.2232 12.7221 93.024C12.9037 92.8247 13.1227 92.6631 13.3667 92.5485C13.6108 92.4339 13.4083 93.3988 13.6776 93.3863C13.9469 93.3738 14.216 93.4144 14.4696 93.506C14.7232 93.5975 14.9563 93.738 15.1556 93.9196C15.3549 94.1012 15.5164 94.3203 15.631 94.5643C15.7457 94.8083 15.8111 95.0725 15.8236 95.3419C17.0101 120.848 32.7923 130.223 39.5306 134.228C40.3451 134.711 40.7744 134.999 41.5814 135.496C50.5228 141.584 68.514 142.571 84.3343 137.84C84.5949 137.754 84.8701 137.721 85.1436 137.743C85.4171 137.765 86.1501 137.779 86.3935 137.906C86.6368 138.033 86.5674 138.175 86.7421 138.386C86.9168 138.598 87.1475 138.607 87.226 138.87C87.3044 139.133 87.5137 138.709 87.4835 138.982C87.4533 139.255 87.3687 139.519 87.2347 139.758C87.1006 139.998 86.9199 140.208 86.7031 140.376C86.4864 140.545 86.2381 140.668 85.9729 140.738C78.9371 142.809 71.6422 143.868 64.3079 143.882Z" fill="#CBAB51"></path> <path d="M18.9863 89.486C18.3183 93.8219 15.6874 97.0124 13.1093 96.6169C10.5312 96.2214 8.98139 92.3835 9.64936 88.0622C10.206 84.4382 14.3603 79.7917 15.7109 78.362C15.7571 78.312 15.8151 78.2743 15.8796 78.2524C15.9441 78.2304 16.013 78.2249 16.0801 78.2362C16.1473 78.2475 16.2106 78.2754 16.2643 78.3173C16.3179 78.3592 16.3603 78.4139 16.3876 78.4762C17.1874 80.2341 19.5429 85.8708 18.9863 89.486Z" fill="#CBAB51"></path> <path d="M102.965 101.655C104.298 105.402 107.263 107.772 109.592 106.946C111.921 106.12 112.724 102.408 111.391 98.6579C110.316 95.6286 105.957 92.3561 104.328 91.2135C104.258 91.1676 104.178 91.1385 104.095 91.1287C104.012 91.1189 103.927 91.1286 103.849 91.157C103.77 91.1855 103.699 91.2318 103.641 91.2925C103.583 91.3532 103.541 91.4264 103.516 91.5065C103.03 93.402 101.884 98.6139 102.965 101.655Z" fill="#CBAB51"></path> <path d="M115.463 97.1426C112.961 99.0469 112.106 102.123 113.556 104.033C115.006 105.943 118.205 105.935 120.707 104.033C122.743 102.48 123.309 97.2949 123.446 95.4404C123.45 95.3615 123.437 95.2828 123.409 95.209C123.381 95.1352 123.338 95.0678 123.283 95.0109C123.229 94.954 123.163 94.9087 123.09 94.8778C123.018 94.8469 122.939 94.831 122.86 94.8311C121.355 95.0127 117.452 95.625 115.463 97.1426Z" fill="#CBAB51"></path> <path d="M97.5625 117.121C98.872 120.803 101.597 123.197 103.65 122.467C105.704 121.738 106.305 118.161 104.998 114.478C103.97 111.589 101.482 108.085 100.477 106.714C100.432 106.651 100.37 106.602 100.299 106.57C100.228 106.538 100.15 106.525 100.073 106.532C99.9954 106.54 99.9212 106.567 99.8576 106.612C99.794 106.657 99.7431 106.717 99.7099 106.788C98.8222 108.686 96.4931 114.109 97.5625 117.121Z" fill="#CBAB51"></path> <path d="M85.4859 125.438C84.7359 129.247 86.1627 132.745 88.6705 133.237C91.1783 133.729 93.8209 131.04 94.5709 127.225C95.1774 124.14 92.9742 119.186 92.11 117.402C92.0676 117.328 92.0096 117.265 91.9402 117.216C91.8707 117.167 91.7914 117.133 91.7078 117.118C91.6242 117.103 91.5382 117.106 91.4559 117.127C91.3736 117.148 91.2969 117.187 91.2311 117.241C89.8131 118.586 86.0924 122.362 85.4859 125.438Z" fill="#CBAB51"></path> <path d="M113.919 111.818C110.946 113.136 109.317 115.969 110.284 118.143C111.25 120.317 114.441 121.011 117.412 119.693C119.834 118.617 121.715 113.666 122.301 111.885C122.322 111.81 122.327 111.731 122.317 111.654C122.307 111.576 122.282 111.502 122.242 111.434C122.203 111.367 122.151 111.308 122.088 111.261C122.026 111.214 121.955 111.18 121.879 111.161C120.33 111.029 116.284 110.769 113.919 111.818Z" fill="#CBAB51"></path> <path d="M103.665 125.954C100.284 126.247 97.7175 128.479 97.9314 130.934C98.1453 133.389 101.06 135.147 104.429 134.851C107.183 134.611 110.699 130.395 111.906 128.837C111.954 128.771 111.988 128.696 112.006 128.616C112.023 128.536 112.024 128.454 112.007 128.374C111.991 128.294 111.958 128.218 111.911 128.151C111.864 128.085 111.803 128.029 111.733 127.987C110.262 127.322 106.354 125.719 103.665 125.954Z" fill="#CBAB51"></path> <path d="M88.0063 135.64C84.2475 135.968 81.3793 138.306 81.602 140.863C81.8246 143.421 85.0532 145.223 88.8149 144.894C91.8764 144.628 95.811 140.207 97.1645 138.59C97.2173 138.522 97.2546 138.444 97.2739 138.36C97.2932 138.277 97.2939 138.19 97.276 138.106C97.2581 138.023 97.2221 137.944 97.1704 137.875C97.1187 137.807 97.0527 137.751 96.977 137.711C95.3305 137.014 91.0004 135.367 88.0063 135.64Z" fill="#CBAB51"></path> <path d="M66.6602 130.629C63.8154 134.324 63.6953 138.999 66.3906 141.074C69.0859 143.148 73.5742 141.832 76.416 138.144C78.7158 135.156 78.6777 128.411 78.5781 125.939C78.5653 125.828 78.5302 125.721 78.4748 125.624C78.4194 125.527 78.3449 125.443 78.2559 125.376C78.1668 125.309 78.0651 125.26 77.9568 125.234C77.8485 125.207 77.736 125.203 77.626 125.221C75.2705 125.813 68.9541 127.647 66.6602 130.629Z" fill="#CBAB51"></path> <path d="M109.056 87.2583C109.724 91.5942 112.358 94.7847 114.936 94.3891C117.514 93.9936 119.078 90.1557 118.396 85.8345C117.836 82.2104 113.685 77.564 112.334 76.1343C112.288 76.0843 112.229 76.0467 112.165 76.0248C112.1 76.0029 112.031 75.9973 111.964 76.0086C111.896 76.02 111.833 76.0478 111.779 76.0897C111.725 76.1315 111.682 76.1861 111.654 76.2485C110.858 78.0063 108.499 83.643 109.056 87.2583Z" fill="#CBAB51"></path> <path d="M102.965 101.655C104.298 105.402 107.263 107.772 109.592 106.946C111.921 106.12 112.724 102.408 111.391 98.6579C110.316 95.6286 105.957 92.3561 104.328 91.2135C104.258 91.1676 104.178 91.1385 104.095 91.1287C104.012 91.1189 103.927 91.1286 103.849 91.157C103.77 91.1855 103.699 91.2318 103.641 91.2925C103.583 91.3532 103.541 91.4264 103.516 91.5065C103.03 93.402 101.884 98.6139 102.965 101.655Z" fill="#CBAB51"></path> <path d="M115.463 97.1426C112.961 99.0469 112.106 102.123 113.556 104.033C115.006 105.943 118.205 105.935 120.707 104.033C122.743 102.48 123.309 97.2949 123.446 95.4404C123.45 95.3615 123.437 95.2828 123.409 95.209C123.381 95.1352 123.338 95.0678 123.283 95.0109C123.229 94.954 123.163 94.9087 123.09 94.8778C123.018 94.8469 122.939 94.831 122.86 94.8311C121.355 95.0127 117.452 95.625 115.463 97.1426Z" fill="#CBAB51"></path> <path d="M97.5625 117.121C98.872 120.803 101.597 123.197 103.65 122.467C105.704 121.738 106.305 118.161 104.998 114.478C103.97 111.589 101.482 108.085 100.477 106.714C100.432 106.651 100.37 106.602 100.299 106.57C100.228 106.538 100.15 106.525 100.073 106.532C99.9954 106.54 99.9212 106.567 99.8576 106.612C99.794 106.657 99.7431 106.717 99.7099 106.788C98.8222 108.686 96.4931 114.109 97.5625 117.121Z" fill="#CBAB51"></path> <path d="M85.4859 125.438C84.7359 129.247 86.1627 132.745 88.6705 133.237C91.1783 133.729 93.8209 131.04 94.5709 127.225C95.1774 124.14 92.9742 119.186 92.11 117.402C92.0676 117.328 92.0096 117.265 91.9402 117.216C91.8707 117.167 91.7914 117.133 91.7078 117.118C91.6242 117.103 91.5382 117.106 91.4559 117.127C91.3736 117.148 91.2969 117.187 91.2311 117.241C89.8131 118.586 86.0924 122.362 85.4859 125.438Z" fill="#CBAB51"></path> <path d="M113.919 111.818C110.946 113.136 109.317 115.969 110.284 118.143C111.25 120.317 114.441 121.011 117.412 119.693C119.834 118.617 121.715 113.666 122.301 111.885C122.322 111.81 122.327 111.731 122.317 111.654C122.307 111.576 122.282 111.502 122.242 111.434C122.203 111.367 122.151 111.308 122.088 111.261C122.026 111.214 121.955 111.18 121.879 111.161C120.33 111.029 116.284 110.769 113.919 111.818Z" fill="#CBAB51"></path> <path d="M103.665 125.954C100.284 126.247 97.7175 128.479 97.9314 130.934C98.1453 133.389 101.06 135.147 104.429 134.851C107.183 134.611 110.699 130.395 111.906 128.837C111.954 128.771 111.988 128.696 112.006 128.616C112.023 128.536 112.024 128.454 112.007 128.374C111.991 128.294 111.958 128.218 111.911 128.151C111.864 128.085 111.803 128.029 111.733 127.987C110.262 127.322 106.354 125.719 103.665 125.954Z" fill="#CBAB51"></path> <path d="M88.0063 135.64C84.2475 135.968 81.3793 138.306 81.602 140.863C81.8246 143.421 85.0532 145.223 88.8149 144.894C91.8764 144.628 95.811 140.207 97.1645 138.59C97.2173 138.522 97.2546 138.444 97.2739 138.36C97.2932 138.277 97.2939 138.19 97.276 138.106C97.2581 138.023 97.2221 137.944 97.1704 137.875C97.1187 137.807 97.0527 137.751 96.977 137.711C95.3305 137.014 91.0004 135.367 88.0063 135.64Z" fill="#CBAB51"></path> <path d="M66.6602 130.629C63.8154 134.324 63.6953 138.999 66.3906 141.074C69.0859 143.148 73.5742 141.832 76.416 138.144C78.7158 135.156 78.6777 128.411 78.5781 125.939C78.5653 125.828 78.5302 125.721 78.4748 125.624C78.4194 125.527 78.3449 125.443 78.2559 125.376C78.1668 125.309 78.0651 125.26 77.9568 125.234C77.8485 125.207 77.736 125.203 77.626 125.221C75.2705 125.813 68.9541 127.647 66.6602 130.629Z" fill="#CBAB51"></path> <path d="M109.055 87.2583C109.723 91.5942 112.357 94.7847 114.935 94.3891C117.513 93.9936 119.077 90.1557 118.395 85.8345C117.835 82.2104 113.684 77.564 112.333 76.1343C112.287 76.0843 112.228 76.0467 112.164 76.0248C112.099 76.0029 112.03 75.9973 111.963 76.0086C111.895 76.02 111.832 76.0478 111.778 76.0897C111.724 76.1315 111.681 76.1861 111.653 76.2485C110.857 78.0063 108.498 83.643 109.055 87.2583Z" fill="#CBAB51"></path> <path d="M102.964 101.655C104.297 105.402 107.262 107.772 109.591 106.946C111.92 106.12 112.723 102.408 111.39 98.6579C110.315 95.6286 105.956 92.3561 104.327 91.2135C104.257 91.1676 104.177 91.1385 104.094 91.1287C104.011 91.1189 103.926 91.1286 103.848 91.157C103.769 91.1855 103.698 91.2318 103.64 91.2925C103.582 91.3532 103.54 91.4264 103.515 91.5065C103.029 93.402 101.883 98.6139 102.964 101.655Z" fill="#CBAB51"></path> <path d="M115.463 97.1426C112.961 99.0469 112.106 102.123 113.556 104.033C115.006 105.943 118.205 105.935 120.707 104.033C122.743 102.48 123.309 97.2949 123.446 95.4404C123.45 95.3615 123.437 95.2828 123.409 95.209C123.381 95.1352 123.338 95.0678 123.283 95.0109C123.229 94.954 123.163 94.9087 123.09 94.8778C123.018 94.8469 122.939 94.831 122.86 94.8311C121.355 95.0127 117.452 95.625 115.463 97.1426Z" fill="#CBAB51"></path> <path d="M97.5625 117.121C98.872 120.803 101.597 123.197 103.65 122.467C105.704 121.738 106.305 118.161 104.998 114.478C103.97 111.589 101.482 108.085 100.477 106.714C100.432 106.651 100.37 106.602 100.299 106.57C100.228 106.538 100.15 106.525 100.073 106.532C99.9954 106.54 99.9212 106.567 99.8576 106.612C99.794 106.657 99.7431 106.717 99.7099 106.788C98.8222 108.686 96.4931 114.109 97.5625 117.121Z" fill="#CBAB51"></path> <path d="M85.4859 125.438C84.7359 129.247 86.1627 132.745 88.6705 133.237C91.1783 133.729 93.8209 131.04 94.5709 127.225C95.1774 124.14 92.9742 119.186 92.11 117.402C92.0676 117.328 92.0096 117.265 91.9402 117.216C91.8707 117.167 91.7914 117.133 91.7078 117.118C91.6242 117.103 91.5382 117.106 91.4559 117.127C91.3736 117.148 91.2969 117.187 91.2311 117.241C89.8131 118.586 86.0924 122.362 85.4859 125.438Z" fill="#CBAB51"></path> <path d="M113.918 111.818C110.945 113.136 109.316 115.969 110.283 118.143C111.249 120.317 114.44 121.011 117.411 119.693C119.833 118.617 121.714 113.666 122.3 111.885C122.321 111.81 122.326 111.731 122.316 111.654C122.306 111.576 122.281 111.502 122.241 111.434C122.202 111.367 122.15 111.308 122.087 111.261C122.025 111.214 121.954 111.18 121.878 111.161C120.329 111.029 116.283 110.769 113.918 111.818Z" fill="#CBAB51"></path> <path d="M103.665 125.954C100.284 126.247 97.7175 128.479 97.9314 130.934C98.1453 133.389 101.06 135.147 104.429 134.851C107.183 134.611 110.699 130.395 111.906 128.837C111.954 128.771 111.988 128.696 112.006 128.616C112.023 128.536 112.024 128.454 112.007 128.374C111.991 128.294 111.958 128.218 111.911 128.151C111.864 128.085 111.803 128.029 111.733 127.987C110.262 127.322 106.354 125.719 103.665 125.954Z" fill="#CBAB51"></path> <path d="M88.0053 135.64C84.2465 135.968 81.3783 138.306 81.601 140.863C81.8237 143.421 85.0522 145.223 88.8139 144.894C91.8754 144.628 95.81 140.207 97.1635 138.59C97.2163 138.522 97.2536 138.444 97.2729 138.36C97.2922 138.277 97.2929 138.19 97.275 138.106C97.2571 138.023 97.2211 137.944 97.1694 137.875C97.1178 137.807 97.0517 137.751 96.976 137.711C95.3295 137.014 90.9994 135.367 88.0053 135.64Z" fill="#CBAB51"></path> <path d="M66.6602 130.629C63.8154 134.324 63.6953 138.999 66.3906 141.074C69.0859 143.148 73.5742 141.832 76.416 138.144C78.7158 135.156 78.6777 128.411 78.5781 125.939C78.5653 125.828 78.5302 125.721 78.4748 125.624C78.4194 125.527 78.3449 125.443 78.2559 125.376C78.1668 125.309 78.0651 125.26 77.9568 125.234C77.8485 125.207 77.736 125.203 77.626 125.221C75.2705 125.813 68.9541 127.647 66.6602 130.629Z" fill="#CBAB51"></path> <path d="M63.7328 143.272C56.3938 143.261 49.0939 141.237 42.0531 139.166C41.5467 138.999 41.2577 138.196 41.2292 138.141C41.1835 138.053 41.1053 138.415 41.2577 137.904C41.2577 137.904 41.2291 138.401 41.6945 138.141C42.1599 137.88 42.7095 138.004 43.225 138.141C59.0599 142.866 77.0365 141.879 85.972 135.797C86.5111 135.431 87.2142 135.012 88.0228 134.529C94.7845 130.518 111.215 119.398 112.386 93.8919C112.399 93.6226 112.464 93.3583 112.579 93.1143C112.694 92.8703 112.855 92.6512 113.054 92.4696C113.254 92.2881 113.487 92.1475 113.74 92.056C113.994 91.9645 114.263 91.9238 114.532 91.9363C114.802 91.9488 114.433 91.8238 114.677 91.9384C114.921 92.053 115.14 92.2146 115.322 92.4139C115.503 92.6131 115.644 92.8462 115.735 93.0998C115.827 93.3534 115.867 93.6226 115.855 93.8919C115.301 105.754 111.689 115.832 105.118 123.842C99.7152 130.428 93.7211 133.988 90.1381 136.112C89.3822 136.563 88.7289 136.949 88.2953 137.245C83.1918 140.723 75.8265 142.78 66.9994 143.196C65.9212 143.246 64.8324 143.272 63.7328 143.272Z" fill="#CBAB51"></path> <path d="M109.055 89.1938C109.723 93.5298 112.357 96.7202 114.935 96.3247C117.513 95.9292 119.077 92.0913 118.395 87.77C117.835 84.146 113.684 79.4995 112.333 78.0698C112.287 78.0199 112.228 77.9823 112.164 77.9603C112.099 77.9384 112.03 77.9329 111.963 77.9442C111.895 77.9555 111.832 77.9833 111.778 78.0252C111.724 78.0671 111.681 78.1216 111.653 78.1841C110.857 79.9419 108.498 85.5786 109.055 89.1938Z" fill="#CBAB51"></path> <path d="M59.6406 28.9805V30H55.793C55.8346 29.6146 55.9596 29.25 56.168 28.9062C56.3763 28.5599 56.7878 28.1016 57.4023 27.5312C57.8971 27.0703 58.2005 26.7578 58.3125 26.5938C58.4635 26.3672 58.5391 26.1432 58.5391 25.9219C58.5391 25.6771 58.4727 25.4896 58.3398 25.3594C58.2096 25.2266 58.0286 25.1602 57.7969 25.1602C57.5677 25.1602 57.3854 25.2292 57.25 25.3672C57.1146 25.5052 57.0365 25.7344 57.0156 26.0547L55.9219 25.9453C55.987 25.3411 56.1914 24.9076 56.5352 24.6445C56.8789 24.3815 57.3086 24.25 57.8242 24.25C58.3893 24.25 58.8333 24.4023 59.1562 24.707C59.4792 25.0117 59.6406 25.3906 59.6406 25.8438C59.6406 26.1016 59.5938 26.3477 59.5 26.582C59.4089 26.8138 59.263 27.0573 59.0625 27.3125C58.9297 27.4818 58.6901 27.7253 58.3438 28.043C57.9974 28.3607 57.7773 28.5716 57.6836 28.6758C57.5924 28.7799 57.5182 28.8815 57.4609 28.9805H59.6406ZM62.2422 24.25C62.7969 24.25 63.2305 24.4479 63.543 24.8438C63.9154 25.3125 64.1016 26.0898 64.1016 27.1758C64.1016 28.2591 63.9141 29.0378 63.5391 29.5117C63.2292 29.9023 62.7969 30.0977 62.2422 30.0977C61.6849 30.0977 61.2357 29.8841 60.8945 29.457C60.5534 29.0273 60.3828 28.263 60.3828 27.1641C60.3828 26.0859 60.5703 25.3099 60.9453 24.8359C61.2552 24.4453 61.6875 24.25 62.2422 24.25ZM62.2422 25.1602C62.1094 25.1602 61.9909 25.2031 61.8867 25.2891C61.7826 25.3724 61.7018 25.5234 61.6445 25.7422C61.569 26.026 61.5312 26.5039 61.5312 27.1758C61.5312 27.8477 61.5651 28.3099 61.6328 28.5625C61.7005 28.8125 61.7852 28.9792 61.8867 29.0625C61.9909 29.1458 62.1094 29.1875 62.2422 29.1875C62.375 29.1875 62.4935 29.1458 62.5977 29.0625C62.7018 28.9766 62.7826 28.8242 62.8398 28.6055C62.9154 28.3242 62.9531 27.8477 62.9531 27.1758C62.9531 26.5039 62.9193 26.043 62.8516 25.793C62.7839 25.5404 62.6979 25.3724 62.5938 25.2891C62.4922 25.2031 62.375 25.1602 62.2422 25.1602ZM68.5469 28.9805V30H64.6992C64.7409 29.6146 64.8659 29.25 65.0742 28.9062C65.2826 28.5599 65.694 28.1016 66.3086 27.5312C66.8034 27.0703 67.1068 26.7578 67.2188 26.5938C67.3698 26.3672 67.4453 26.1432 67.4453 25.9219C67.4453 25.6771 67.3789 25.4896 67.2461 25.3594C67.1159 25.2266 66.9349 25.1602 66.7031 25.1602C66.474 25.1602 66.2917 25.2292 66.1562 25.3672C66.0208 25.5052 65.9427 25.7344 65.9219 26.0547L64.8281 25.9453C64.8932 25.3411 65.0977 24.9076 65.4414 24.6445C65.7852 24.3815 66.2148 24.25 66.7305 24.25C67.2956 24.25 67.7396 24.4023 68.0625 24.707C68.3854 25.0117 68.5469 25.3906 68.5469 25.8438C68.5469 26.1016 68.5 26.3477 68.4062 26.582C68.3151 26.8138 68.1693 27.0573 67.9688 27.3125C67.8359 27.4818 67.5964 27.7253 67.25 28.043C66.9036 28.3607 66.6836 28.5716 66.5898 28.6758C66.4987 28.7799 66.4245 28.8815 66.3672 28.9805H68.5469ZM71.4453 30V28.8477H69.1016V27.8867L71.5859 24.25H72.5078V27.8828H73.2188V28.8477H72.5078V30H71.4453ZM71.4453 27.8828V25.9258L70.1289 27.8828H71.4453Z" fill="#1A232C"></path> <path d="M38.3721 45V37.8418H43.2793V39.0527H39.8174V40.7471H42.8057V41.958H39.8174V45H38.3721ZM44.4756 45V37.8418H49.7832V39.0527H45.9209V40.6396H49.5146V41.8457H45.9209V43.7939H49.9199V45H44.4756ZM57.6006 45H56.0283L55.4033 43.374H52.542L51.9512 45H50.418L53.2061 37.8418H54.7344L57.6006 45ZM54.9395 42.168L53.9531 39.5117L52.9863 42.168H54.9395ZM59.2412 45V39.0527H57.1172V37.8418H62.8057V39.0527H60.6865V45H59.2412ZM63.7334 37.8418H65.1787V41.7188C65.1787 42.334 65.1966 42.7327 65.2324 42.915C65.2943 43.208 65.4408 43.444 65.6719 43.623C65.9062 43.7988 66.2253 43.8867 66.6289 43.8867C67.0391 43.8867 67.3483 43.8037 67.5566 43.6377C67.765 43.4684 67.8903 43.2617 67.9326 43.0176C67.9749 42.7734 67.9961 42.3682 67.9961 41.8018V37.8418H69.4414V41.6016C69.4414 42.4609 69.4023 43.068 69.3242 43.4229C69.2461 43.7777 69.1012 44.0771 68.8896 44.3213C68.6813 44.5654 68.4014 44.7607 68.0498 44.9072C67.6982 45.0505 67.2393 45.1221 66.6729 45.1221C65.9893 45.1221 65.4701 45.0439 65.1152 44.8877C64.7637 44.7282 64.4854 44.5231 64.2803 44.2725C64.0752 44.0186 63.9401 43.7533 63.875 43.4766C63.7806 43.0664 63.7334 42.4609 63.7334 41.6602V37.8418ZM70.9746 45V37.8418H74.0166C74.7816 37.8418 75.3366 37.9069 75.6816 38.0371C76.0299 38.1641 76.3083 38.3919 76.5166 38.7207C76.7249 39.0495 76.8291 39.4255 76.8291 39.8486C76.8291 40.3857 76.6712 40.8301 76.3555 41.1816C76.0397 41.5299 75.5677 41.7497 74.9395 41.8408C75.252 42.0231 75.5091 42.2233 75.7109 42.4414C75.916 42.6595 76.1911 43.0469 76.5361 43.6035L77.4102 45H75.6816L74.6367 43.4424C74.2656 42.8857 74.0117 42.5358 73.875 42.3926C73.7383 42.2461 73.5934 42.1468 73.4404 42.0947C73.2874 42.0394 73.0449 42.0117 72.7129 42.0117H72.4199V45H70.9746ZM72.4199 40.8691H73.4893C74.1826 40.8691 74.6156 40.8398 74.7881 40.7812C74.9606 40.7227 75.0957 40.6217 75.1934 40.4785C75.291 40.3353 75.3398 40.1562 75.3398 39.9414C75.3398 39.7005 75.2747 39.5068 75.1445 39.3604C75.0176 39.2106 74.8369 39.1162 74.6025 39.0771C74.4854 39.0609 74.1338 39.0527 73.5479 39.0527H72.4199V40.8691ZM78.1963 45V37.8418H83.5039V39.0527H79.6416V40.6396H83.2354V41.8457H79.6416V43.7939H83.6406V45H78.1963ZM84.8613 37.8418H87.5029C88.0986 37.8418 88.5527 37.8874 88.8652 37.9785C89.2852 38.1022 89.6449 38.3219 89.9443 38.6377C90.2438 38.9535 90.4717 39.3408 90.6279 39.7998C90.7842 40.2555 90.8623 40.8187 90.8623 41.4893C90.8623 42.0785 90.7891 42.5863 90.6426 43.0127C90.4635 43.5335 90.208 43.9551 89.876 44.2773C89.6253 44.5215 89.2868 44.7119 88.8604 44.8486C88.5413 44.9495 88.1149 45 87.5811 45H84.8613V37.8418ZM86.3066 39.0527V43.7939H87.3857C87.7894 43.7939 88.0807 43.7712 88.2598 43.7256C88.4941 43.667 88.6878 43.5677 88.8408 43.4277C88.9971 43.2878 89.124 43.0583 89.2217 42.7393C89.3193 42.417 89.3682 41.9792 89.3682 41.4258C89.3682 40.8724 89.3193 40.4476 89.2217 40.1514C89.124 39.8551 88.9873 39.624 88.8115 39.458C88.6357 39.292 88.4128 39.1797 88.1426 39.1211C87.9408 39.0755 87.5452 39.0527 86.9561 39.0527H86.3066ZM45.5303 59V51.9004H46.9756V57.7939H50.5693V59H45.5303ZM51.5605 59V51.8418H53.0059V59H51.5605ZM54.0215 56.6709L55.4277 56.5342C55.5124 57.0062 55.6833 57.3529 55.9404 57.5742C56.2008 57.7956 56.5508 57.9062 56.9902 57.9062C57.4557 57.9062 57.8057 57.8086 58.04 57.6133C58.2777 57.4147 58.3965 57.1836 58.3965 56.9199C58.3965 56.7507 58.346 56.6074 58.2451 56.4902C58.1475 56.3698 57.9749 56.2656 57.7275 56.1777C57.5583 56.1191 57.1725 56.015 56.5703 55.8652C55.7956 55.6732 55.252 55.4372 54.9395 55.1572C54.5 54.7633 54.2803 54.2832 54.2803 53.7168C54.2803 53.3522 54.3828 53.012 54.5879 52.6963C54.7962 52.3773 55.0941 52.1348 55.4814 51.9688C55.8721 51.8027 56.3424 51.7197 56.8926 51.7197C57.791 51.7197 58.4665 51.9167 58.9189 52.3105C59.3747 52.7044 59.6139 53.2301 59.6367 53.8877L58.1914 53.9512C58.1296 53.5833 57.9961 53.3197 57.791 53.1602C57.5892 52.9974 57.2848 52.916 56.8779 52.916C56.458 52.916 56.1292 53.0023 55.8916 53.1748C55.7386 53.2855 55.6621 53.4336 55.6621 53.6191C55.6621 53.7884 55.7337 53.9333 55.877 54.0537C56.0592 54.2067 56.502 54.3662 57.2051 54.5322C57.9082 54.6982 58.4274 54.8708 58.7627 55.0498C59.1012 55.2256 59.3649 55.4681 59.5537 55.7773C59.7458 56.0833 59.8418 56.4626 59.8418 56.915C59.8418 57.3252 59.7279 57.7093 59.5 58.0674C59.2721 58.4255 58.9499 58.6924 58.5332 58.8682C58.1165 59.0407 57.5973 59.127 56.9756 59.127C56.0706 59.127 55.3757 58.9186 54.8906 58.502C54.4056 58.082 54.1159 57.4717 54.0215 56.6709ZM62.6689 59V53.0527H60.5449V51.8418H66.2334V53.0527H64.1143V59H62.6689ZM67.127 59V51.8418H68.5723V59H67.127ZM69.9688 59V51.8418H71.375L74.3047 56.6221V51.8418H75.6475V59H74.1973L71.3115 54.332V59H69.9688ZM80.5107 56.3682V55.1621H83.626V58.0137C83.3232 58.3066 82.8838 58.5654 82.3076 58.79C81.7347 59.0114 81.1536 59.1221 80.5645 59.1221C79.8158 59.1221 79.1631 58.9658 78.6064 58.6533C78.0498 58.3376 77.6315 57.8883 77.3516 57.3057C77.0716 56.7197 76.9316 56.0833 76.9316 55.3965C76.9316 54.651 77.0879 53.9886 77.4004 53.4092C77.7129 52.8298 78.1702 52.3854 78.7725 52.0762C79.2314 51.8385 79.8027 51.7197 80.4863 51.7197C81.375 51.7197 82.0684 51.9069 82.5664 52.2812C83.0677 52.6523 83.39 53.1667 83.5332 53.8242L82.0977 54.0928C81.9967 53.7412 81.8063 53.4645 81.5264 53.2627C81.2497 53.0576 80.903 52.9551 80.4863 52.9551C79.8548 52.9551 79.3519 53.1553 78.9775 53.5557C78.6064 53.9561 78.4209 54.5501 78.4209 55.3379C78.4209 56.1875 78.6097 56.8255 78.9873 57.252C79.3649 57.6751 79.8597 57.8867 80.4717 57.8867C80.7744 57.8867 81.0771 57.8281 81.3799 57.7109C81.6859 57.5905 81.9479 57.4456 82.166 57.2764V56.3682H80.5107Z" fill="#1A232C"></path> <path d="M64 116C77.2548 116 88 105.255 88 92C88 78.7452 77.2548 68 64 68C50.7452 68 40 78.7452 40 92C40 105.255 50.7452 116 64 116Z" fill="#F6F6F6"></path> <path d="M49 92C49 83.7157 55.7157 77 64 77C72.2843 77 79 83.7157 79 92C79 100.284 72.2843 107 64 107C55.7157 107 49 100.284 49 92Z" fill="#CBAB51"></path> <path d="M64 84.4062C62.4981 84.4062 61.0299 84.8516 59.7811 85.686C58.5324 86.5204 57.559 87.7064 56.9843 89.094C56.4095 90.4816 56.2592 92.0084 56.5522 93.4815C56.8452 94.9545 57.5684 96.3076 58.6304 97.3696C59.6924 98.4316 61.0455 99.1548 62.5185 99.4478C63.9916 99.7408 65.5184 99.5905 66.906 99.0157C68.2936 98.441 69.4796 97.4676 70.314 96.2189C71.1484 94.9701 71.5938 93.5019 71.5938 92C71.5915 89.9867 70.7907 88.0565 69.3671 86.6329C67.9435 85.2093 66.0133 84.4085 64 84.4062ZM64 86.0938C65.0928 86.0929 66.1642 86.3968 67.0938 86.9713V88.0963L65.6727 89.75L63.6709 90.0207L62.3561 89.1594C62.2027 89.0523 62.0296 88.977 61.8468 88.9377C61.6639 88.8984 61.4751 88.8959 61.2913 88.9305C61.1075 88.9651 60.9325 89.036 60.7764 89.139C60.6203 89.2421 60.4864 89.3752 60.3824 89.5306L58.9101 91.7314C58.7564 91.9602 58.6737 92.2292 58.6724 92.5048L58.6612 94.5221C58.2361 93.6221 58.0454 92.6292 58.1068 91.6357C58.1682 90.6422 58.4797 89.6804 59.0124 88.8395C59.545 87.9986 60.2815 87.306 61.1535 86.826C62.0255 86.3459 63.0046 86.0941 64 86.0938ZM59.8101 96.1583C59.9756 96.0273 60.1095 95.8608 60.2019 95.671C60.2943 95.4813 60.3428 95.2732 60.3438 95.0621L60.3578 92.6012L61.6284 90.7027L62.797 91.4677C63.0862 91.6704 63.4413 91.7563 63.7912 91.7082L66.0039 91.4087C66.3456 91.3624 66.6584 91.1923 66.8828 90.9305L68.4409 89.1172C68.5944 88.9376 68.7005 88.7225 68.7496 88.4914C69.2622 89.1836 69.6167 89.9798 69.7883 90.8239C69.9598 91.6679 69.9442 92.5394 69.7424 93.3767L68.9451 92.6476C68.7472 92.4675 68.5022 92.3474 68.2387 92.3012C67.9751 92.255 67.7039 92.2847 67.4566 92.3867L65.3156 93.2769C65.0917 93.3707 64.8957 93.5205 64.7466 93.712C64.5975 93.9035 64.5002 94.1302 64.4641 94.3702L64.296 95.5093C64.2456 95.8517 64.3232 96.2008 64.514 96.4896C64.7049 96.7783 64.9955 96.9866 65.3303 97.0745L66.4553 97.3684C65.3574 97.8716 64.1313 98.0252 62.9432 97.8084C61.7551 97.5916 60.6623 97.0148 59.8129 96.1562L59.8101 96.1583ZM68.0205 96.3214L67.9059 96.2068C67.7293 96.03 67.509 95.9034 67.2674 95.8398L66.0018 95.5079L66.1094 94.7759L67.9375 94.0159L69.0625 95.0417C68.7761 95.5145 68.4254 95.9452 68.0205 96.3214Z" fill="white"></path> </svg> </div> </div> </div> <div class="container"> <div class="banner__breadcrumbs breadcrumbs"> <div class="breadcrumbs__item breadcrumbs-item"> <a class="breadcrumbs-item__link" href="https://www.hotels-in-osaka.com/en/" target="_blank">Osaka</a> <span class="breadcrumbs-item__arrow">&gt;</span> </div> <div class="breadcrumbs__item breadcrumbs-item"> <a class="breadcrumbs-item__link" href="https://www.hotels-in-osaka.com/en/type/apartments-3/" target="_blank">Apartments</a> <span class="breadcrumbs-item__arrow">&gt;</span> </div> <div class="breadcrumbs__item breadcrumbs-item"> <span class="breadcrumbs-item__text">Chuan House Osaka Apartment</span> </div> </div> </div> </div> <div class="proxy-container"> <div class="main container"> <div class="main-availability-header"> <div class="hotel-availability-wrap clearfix"> <div class="hotel-availability__left hotel-gallery pull-left"> <p class="photo-hover js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG" data-lightbox="header-gallery"> <img alt="Chuan House Osaka" class="hide-mob" data-original="/data/Photos/700x500w/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG" height="437" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb71120000435oto5FBC.webp" style="display: none;" width="570"/> <span class="__hover"> <i class="icn-sprite icn-view-photo"></i> </span> </p> <p></p> </div> <div class="hotel-availability__right pull-left"> <div class="pull-left hide-mob"> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/5375/537582/537582063/osaka-chuan-house-apartment-photo-2.JPEG" data-lightbox="header-gallery"></p> <img alt="Apartment Chuan House" class="" data-original="/data/Photos/700x500w/5375/537582/537582063/osaka-chuan-house-apartment-photo-2.JPEG" height="217" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb0u120000435otqAF0B.webp" style="display: inline;" width="538"/> </div> <div class="bottom-main-img-wrap hide-mob"> <span class="pull-left bottom-main-img"> <p class="photo-hover js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG" data-lightbox="header-gallery"></p> <img alt="Chuan House Apartment" class="" data-original="/data/Photos/300x300w/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG" height="217" src="/data/Photos/300x300w/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG" style="display: block;" width="179"/> </span> <span class="pull-left bottom-main-img"> <p class="photo-hover js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945149/osaka-chuan-house-apartment-photo-4.JPEG" data-lightbox="header-gallery"></p> <img alt="Chuan House Osaka" class="" data-original="/data/Photos/300x300w/4849/484945/484945149/osaka-chuan-house-apartment-photo-4.JPEG" height="217" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb1q120000435ots1E0A.webp" style="display: block;" width="179"/> </span> <span class="pull-left bottom-main-img"> <p class="photo-hover js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945353/osaka-chuan-house-apartment-photo-5.JPEG" data-lightbox="header-gallery"></p> <img alt="Apartment Chuan House" class="" data-original="/data/Photos/300x300w/4849/484945/484945353/osaka-chuan-house-apartment-photo-5.JPEG" height="217" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2m120000435otu446B.webp" style="display: block;" width="179"/> </span> </div> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945779/osaka-chuan-house-apartment-photo-6.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945830/osaka-chuan-house-apartment-photo-7.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437628/437628537/osaka-chuan-house-apartment-photo-8.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437628/437628570/osaka-chuan-house-apartment-photo-9.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437630/437630580/osaka-chuan-house-apartment-photo-10.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437661/437661639/osaka-chuan-house-apartment-photo-11.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4410/441073/441073653/osaka-chuan-house-apartment-photo-12.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4410/441080/441080952/osaka-chuan-house-apartment-photo-13.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437627/437627346/osaka-chuan-house-apartment-photo-14.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556796/osaka-chuan-house-apartment-photo-15.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437627/437627379/osaka-chuan-house-apartment-photo-16.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4410/441073/441073848/osaka-chuan-house-apartment-photo-17.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556829/osaka-chuan-house-apartment-photo-18.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/5375/537582/537582042/osaka-chuan-house-apartment-photo-19.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556847/osaka-chuan-house-apartment-photo-20.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4410/441080/441080928/osaka-chuan-house-apartment-photo-21.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437627/437627040/osaka-chuan-house-apartment-photo-22.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556805/osaka-chuan-house-apartment-photo-23.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556790/osaka-chuan-house-apartment-photo-24.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/5375/537581/537581988/osaka-chuan-house-apartment-photo-25.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945317/osaka-chuan-house-apartment-photo-26.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4410/441072/441072426/osaka-chuan-house-apartment-photo-27.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556841/osaka-chuan-house-apartment-photo-28.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4411/441102/441102708/osaka-chuan-house-apartment-photo-29.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484944/484944225/osaka-chuan-house-apartment-photo-30.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945797/osaka-chuan-house-apartment-photo-31.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4849/484945/484945782/osaka-chuan-house-apartment-photo-32.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/5375/537584/537584559/osaka-chuan-house-apartment-photo-33.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556814/osaka-chuan-house-apartment-photo-34.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4376/437661/437661597/osaka-chuan-house-apartment-photo-35.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556823/osaka-chuan-house-apartment-photo-36.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556838/osaka-chuan-house-apartment-photo-37.JPEG" data-lightbox="header-gallery"></p> <p class="js-lightbox" data-href="/data/Photos/OriginalPhoto/4215/421556/421556808/osaka-chuan-house-apartment-photo-38.JPEG" data-lightbox="header-gallery"></p> <div class="bottom-main-img-wrap js-photos-gallery show-mob"> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/700x500w/5375/537582/537582063/osaka-chuan-house-apartment-photo-2.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/700x500w/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945149/osaka-chuan-house-apartment-photo-4.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945353/osaka-chuan-house-apartment-photo-5.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945779/osaka-chuan-house-apartment-photo-6.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945830/osaka-chuan-house-apartment-photo-7.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437628/437628537/osaka-chuan-house-apartment-photo-8.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437628/437628570/osaka-chuan-house-apartment-photo-9.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437630/437630580/osaka-chuan-house-apartment-photo-10.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437661/437661639/osaka-chuan-house-apartment-photo-11.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/700x500w/4410/441073/441073653/osaka-chuan-house-apartment-photo-12.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/700x500w/4410/441080/441080952/osaka-chuan-house-apartment-photo-13.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437627/437627346/osaka-chuan-house-apartment-photo-14.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556796/osaka-chuan-house-apartment-photo-15.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437627/437627379/osaka-chuan-house-apartment-photo-16.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4410/441073/441073848/osaka-chuan-house-apartment-photo-17.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556829/osaka-chuan-house-apartment-photo-18.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/700x500w/5375/537582/537582042/osaka-chuan-house-apartment-photo-19.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556847/osaka-chuan-house-apartment-photo-20.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4410/441080/441080928/osaka-chuan-house-apartment-photo-21.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437627/437627040/osaka-chuan-house-apartment-photo-22.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556805/osaka-chuan-house-apartment-photo-23.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556790/osaka-chuan-house-apartment-photo-24.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/5375/537581/537581988/osaka-chuan-house-apartment-photo-25.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945317/osaka-chuan-house-apartment-photo-26.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/700x500w/4410/441072/441072426/osaka-chuan-house-apartment-photo-27.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556841/osaka-chuan-house-apartment-photo-28.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4411/441102/441102708/osaka-chuan-house-apartment-photo-29.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4849/484944/484944225/osaka-chuan-house-apartment-photo-30.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945797/osaka-chuan-house-apartment-photo-31.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/700x500w/4849/484945/484945782/osaka-chuan-house-apartment-photo-32.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/5375/537584/537584559/osaka-chuan-house-apartment-photo-33.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House Apartment *" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556814/osaka-chuan-house-apartment-photo-34.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/700x500w/4376/437661/437661597/osaka-chuan-house-apartment-photo-35.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556823/osaka-chuan-house-apartment-photo-36.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556838/osaka-chuan-house-apartment-photo-37.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> <div class="photos-gallery__image-item"> <span class="pull-left js-photos"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/700x500w/4215/421556/421556808/osaka-chuan-house-apartment-photo-38.JPEG" height="100%" src="/_.gif" width="100%"/> </span> </div> </div> </div> <a class="hotel-availability__view-all-photo hide-mob" href="#photo"> <div class="hotel-availability__view-all-photo_blur"></div> <i class="icn-sprite icn-Images-3"></i> View all photos
									</a> <div class="show-mob overview-mob container"> <h2>Chuan House</h2> <div class="__text js-text-overview"> <p>Located about a 10-minute drive from National Bunraku Theater, Chuan House Osaka Apartment is within 10 minutes' drive of a top family-friendly place like Osaka Tennoji Zoo.</p> <p>Morinomiya Piloti Hall is at a distance of 15 minutes' walk and Tamatsukuri metro station is 800 metres away. This apartment is nestled about a 10-minute drive from Shinsaibashi shopping Hit Department Stores. The apartment is within short walking distance of the nearest bus stop Nakamichi.</p> <p>Featuring a balcony and a seating area along with a sofa set and conveniences like air conditioning, Chuan House Osaka Apartment promises a delightful stay. Also, this accommodation comes with a tub, a walk-in shower, and a separate toilet in the bathroom. Furthermore, hairdryers, bath sheets, and slippers is available.</p> <p>Among other facilities at the apartment a microwave, an electric kettle, and a refrigerator are provided for self-catering. The restaurant Jidoriyaki Fujitaya is approximately 5 minutes' walk from the property.</p> </div> <div class="js-btn-description"> <a class="js-more">+ More</a> <a class="js-hide" style="display: none">- Hide</a> </div> </div> <div class="upblock-form-wrap js-fixed-availform js-new-calendar-onboard-wrapper is-open-onboard"> <div class="avail-onboard-bg js-new-calendar-onboard"></div> <div class="availability-deck js-availbox-cont js-avail-scroll-point" id="availability-form"> <div class="reserv-title"> <span class="is-main-form">Online Reservation</span> <span class="is-for-popup">Enter your dates to see prices</span> </div> <form action="#rooms" autocomplete="off" class="js-avail-form" formtarget="#rooms" method="get"> <div class="new-calendar js-new-calendar-wrapper is-open-first"> <fieldset class="form-fieldset js-new-calendar-from"> <span class="form-fieldset__title"> <label class="js-check-in-text" for="d_from">Check-in date:</label> </span> <div class="form-fieldset__calendar clearfix"> <input class="js-new-calendar-from-input" id="d_from" name="from" type="hidden" value="2025-08-06"/> <div class="js-calendar-text calendar_field">Wed 6 Aug 2025</div> </div> </fieldset> <fieldset class="form-fieldset js-new-calendar-to"> <span class="form-fieldset__title"> <label class="js-check-out-text" for="d_to">Check-out date:</label> </span> <div class="form-fieldset__calendar clearfix"> <input class="js-new-calendar-to-input" id="d_to" name="to" type="hidden" value="2025-08-07"/> <div class="js-calendar-text calendar_field">Thu 7 Aug 2025</div> </div> </fieldset> <div class="hidden js-plural-night-1"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="hidden js-plural-night-2"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="hidden js-plural-night-5"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="date-picker-wrapper is-main-form-calendar no-shortcuts inline-wrapper no-gap two-months" style="user-select: none;" unselectable="on"><div class="custom-header js-custom-header"><div class="custom-header__text">Enter your dates and check our special offers</div></div><div class="drp_top-bar normal"><div class="normal-top"><span class="selection-top">Selected: </span> <b class="start-day">2025-08-06</b> <span class="separator-day"> to </span> <b class="end-day">2025-08-07</b> <i class="selected-days" style="display: inline;">(<span class="selected-days-num">2</span> Days)</i></div><div class="error-top">error</div><div class="default-top">Please select a date range between 2 and 30 days</div><input class="apply-btn hide" type="button" value="Close"/></div><div class="month-wrapper"> <div class="month1 month js-month"> <div class="month-content"> <div class="month-head"> <div class="month-caption"> <div class="month-arrow month-arrow--next"> <span class="prev"><img class="calendar-arrow calendar-arrow--left icon icon-arrow" src="/templates/hotel33/images/arrow-758f4bc1ad.svg"/></span> </div> <div class="month-name"><div class="month-element">August</div> <div class="month-element">2025</div></div> <div class="month-arrow month-arrow--next"> </div> </div> <div class="week-name"><div class="week-name__item">Mo</div><div class="week-name__item">Tu</div><div class="week-name__item">We</div><div class="week-name__item">Th</div><div class="week-name__item">Fr</div><div class="week-name__item">Sa</div><div class="week-name__item">Su</div></div> </div> <div class="days-container js-days"><div class="days-row"><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753632000000">28</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753718400000">29</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753804800000">30</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753891200000">31</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1753977600000">1</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754064000000">2</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754150400000">3</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754236800000">4</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754323200000">5</div></div><div class="day js-day-wrapper is-toMonth is-checked is-first-date-selected"><div class="day-text js-day toMonth valid real-today checked first-date-selected" data-tooltip="" time="1754409600000">6</div></div><div class="day js-day-wrapper is-toMonth is-checked is-last-date-selected"><div class="day-text js-day toMonth valid checked last-date-selected" data-tooltip="" time="1754496000000">7</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754582400000">8</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754668800000">9</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754755200000">10</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754841600000">11</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754928000000">12</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755014400000">13</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755100800000">14</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755187200000">15</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755273600000">16</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755360000000">17</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755446400000">18</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755532800000">19</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755619200000">20</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755705600000">21</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755792000000">22</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755878400000">23</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755964800000">24</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756051200000">25</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756137600000">26</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756224000000">27</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756310400000">28</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756396800000">29</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756483200000">30</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756569600000">31</div></div></div></div> </div> </div><div class="month-gap"></div><div class="month2 month js-month"> <div class="month-content"> <div class="month-head"> <div class="month-caption"> <div class="month-arrow month-arrow--next"> </div> <div class="month-name"><div class="month-element">September</div> <div class="month-element">2025</div></div> <div class="month-arrow month-arrow--next"> <span class="next"><img class="calendar-arrow calendar-arrow--right icon icon-arrow" src="/templates/hotel33/images/arrow-758f4bc1ad.svg"/></span> </div> </div> <div class="week-name"><div class="week-name__item">Mo</div><div class="week-name__item">Tu</div><div class="week-name__item">We</div><div class="week-name__item">Th</div><div class="week-name__item">Fr</div><div class="week-name__item">Sa</div><div class="week-name__item">Su</div></div> </div> <div class="days-container js-days"><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756656000000">1</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756742400000">2</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756828800000">3</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756915200000">4</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757001600000">5</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757088000000">6</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757174400000">7</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757260800000">8</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757347200000">9</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757433600000">10</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757520000000">11</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757606400000">12</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757692800000">13</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757779200000">14</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757865600000">15</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757952000000">16</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758038400000">17</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758124800000">18</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758211200000">19</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758297600000">20</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758384000000">21</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758470400000">22</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758556800000">23</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758643200000">24</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758729600000">25</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758816000000">26</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758902400000">27</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758988800000">28</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1759075200000">29</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1759161600000">30</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759248000000">1</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759334400000">2</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759420800000">3</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759507200000">4</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759593600000">5</div></div></div></div> </div> </div></div><div class="time"><div class="time1"></div><div class="time2"></div></div><div class="custom-footer js-custom-footer"><div class="calendar-hover-range">We 06 Aug<span class="calendar-hover-range-icon"> — </span>Th 07 Aug</div><div class="calendar-hover-nights"> <div class="js-plural-night-phrase">
					(<span class="js-night-count">1</span>-night stay)</div> </div></div><div class="date-range-length-tip"></div></div></div> <div class="popup-form-container clearfix"> <div class="popup-form-wrap js-form-popup"> <div class="js-adalts-child-text" style="display: none;"> <div class="js-children-form1">Child</div> <div class="js-children-form2">Children</div> <div class="js-adults-form1">Adult</div> <div class="js-adults-form2">Adults</div> </div> <div class="popup-form-wrap__title">Guests:</div> <div class="popup-form clearfix"> <div class="popup-form-adults"> <span class="js-popup-form-adults">2</span> <span class="js-popup-form-adults-text">Adults</span> </div> <div class="popup-form-child" style="display: none;"> +
						<span class="js-popup-form-childs">0</span> <span class="js-popup-form-child-text">Children</span> </div> </div> </div> <div class="adults-and-childs-wrap js-adults-childs clearfix"> <div class="adults-and-childs"> <fieldset> <label for="adults">Adults:</label> <select class="sel" id="adults" name="adults"> <option value="1">1</option> <option selected="selected" value="2">2</option> <option value="3">3</option> <option value="4">4</option> <option value="5">5</option> <option value="6">6</option> <option value="7">7</option> <option value="8">8</option> <option value="9">9</option> <option value="10">10</option> <option value="11">11</option> <option value="12">12</option> <option value="13">13</option> <option value="14">14</option> <option value="15">15</option> <option value="16">16</option> <option value="17">17</option> <option value="18">18</option> <option value="19">19</option> <option value="20">20</option> </select> </fieldset> <fieldset> <label for="childs">Children:</label> <select class="js-childs" id="childs" name="children"> <option selected="selected" value="0">0</option> <option value="1">1</option> <option value="2">2</option> <option value="3">3</option> <option value="4">4</option> <option value="5">5</option> <option value="6">6</option> <option value="7">7</option> <option value="8">8</option> <option value="9">9</option> <option value="10">10</option> </select> </fieldset> <div class="childrens-age-wrap js-age-container" style="display: none;"> <div class="js-child-age-title">Ages of children:</div> </div> </div> <div class="error-age-message hidden js-ch-error js-ch-error-one">Specify the age of a child</div> <div class="error-age-message hidden js-ch-error js-ch-error-plural">Specify the age of children</div> <div class="adults-and-childs__btn-wrap"> <div class="adults-and-childs__btn-cancel js-cancel-btn">Cancel</div> <div class="adults-and-childs__btn-ok js-ok-btn">OK</div> </div> </div> </div> <button class="avail-form__submit js-avail-info-submit-btn" type="submit">Check availability</button> </form> </div> <script type="text/javascript">
	travel.opts.av_params = {
		use:      '0',		from:     '2025-08-06',
		to:       '2025-08-07',
		adults:   '2',
		children: '0'
		
	};
</script> </div> </div> </div> <div class="seo-header" style="display: none"> <div class="links js-links-container clearfix seo-links seo-links--border"> <div class="lnk"> <ul class="lnk__list seo-links__list seo-links__list--margin"> <li class="lnk__item smile seo-links__list-item"> <a class="seo-links__list-link" href="https://itta-de-lycka.hotels-in-osaka.com/en/" target="_blank">Apartment It'ta De Lycka</a> </li> </ul> </div> <div class="rlink_link" style="display: none"> <ul class="rlink_link__list seo-links__list"> <li class="rlink_link__item seo-links__list-item"> <a class="seo-links__list-link" href="https://bela-vista-gramado-ideal-para-6-pessoas.gramado-hotels-br.com/en/" target="_blank">Bela Vista 201 - My Temporada Apartment in Gramado</a> </li> </ul> </div> </div> </div> <div class="hotel-description clearfix"> <script type="text/javascript">
	travel.opts.sid 	=     '125949';
				travel.opts.hotelName = 'Chuan House';
			travel.opts.siteDomain = 'hotels-in-osaka.com';
			travel.opts.nid		=     '29';
			travel.opts.dest_id	= '18242';
			travel.opts.obj_id 	=  '2158254';
			travel.opts.ext_id 	=  '3664773';
			travel.opts.theme 	=   '3306';
			travel.opts.city 	=    "Osaka";
			travel.opts.country = "Japan";
			travel.opts.stars 	=        '0';
			travel.opts.is_apartment = '1';
			travel.opts.htype_id =     '3';
			travel.opts.popprice =     '1';
								travel.opts.clicktrip = {
				cutype: "tab",
				dom: "hotels-in-osaka.com",
				city_id: "-240905",
                dest_type: "city",
							htype_id: 201,
			subacc: "clicktrip-bn-n29-050825-i2158254-s125949-w5i1efkSoviKnod89-cu_tab-d3306-dc3-bt392"
		};
				travel.opts.guestsInfo = {
		minAdultsCount: 1,
		maxAdultsCount: 20,
		minChildrenCount: 0,
		maxChildrenCount: 10,
		minChildAge: 0,
		maxChildAge: 16
	}
</script> <div class="hotel-page js-page" id="main" style="display: block;"> <div class="show-mob"> <div class="js-rooms-cont"></div> </div> <div class="hotel-description-main hotel-main-page pull-right"> <h2 class="hotel-page__title">
							Chuan House Apartment
			</h2> <div class="main-page__description"> <p>Located about a 10-minute drive from National Bunraku Theater, <strong>Chuan House Osaka Apartment</strong> is within 10 minutes' drive of a top family-friendly place like Osaka Tennoji Zoo.</p> <p>Featuring a balcony and a seating area along with a sofa set and conveniences like air conditioning, <strong>Chuan House Osaka Apartment</strong> promises a delightful stay. Also, this accommodation comes with a tub, a walk-in shower, and a separate toilet in the bathroom. Furthermore, hairdryers, bath sheets, and slippers is available.</p> <p>Among other facilities at the apartment a microwave, an electric kettle, and a refrigerator are provided for self-catering. The restaurant Jidoriyaki Fujitaya is approximately 5 minutes' walk from the property.</p> <p>Morinomiya Piloti Hall is at a distance of 15 minutes' walk and Tamatsukuri metro station is 800 metres away. This apartment is nestled about a 10-minute drive from Shinsaibashi shopping Hit Department Stores. The apartment is within short walking distance of the nearest bus stop Nakamichi.</p> </div> <div class="seo-main" style="display: none"> <div class="links js-links-container clearfix seo-links seo-links--border"> <div class="lnk"> <ul class="lnk__list seo-links__list seo-links__list--margin"> </ul> </div> <div class="rlink_link" style="display: none"> <ul class="rlink_link__list seo-links__list"> </ul> </div> </div> </div> <div class="categories"> <a class="categories__item" href="https://www.hotels-in-osaka.com/en/0-stars/">Unrated hotels</a> <span class="categories__item js-link js-link-new" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS9lbi90aGVtZS9hcGFydG1lbnRzLWFuZC1iLWJzLTMv">Apartments and B&amp;Bs</span> <a class="categories__item" href="https://www.hotels-in-osaka.com/en/theme/cheap-and-budget-hotels-9/">Cheap and budget hotels</a> <span class="categories__item js-link js-link-new" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS9lbi90aGVtZS9sdXh1cnktYWNjb21tb2RhdGlvbnMtMzUv">Luxury accommodations</span> <span class="categories__item js-link js-link-new" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS9lbi9uZWFyLXVlaG9tbWFjaGktdGVubm9qaS1zb3V0aGVybi1vc2FrYS1kcjkwMzc0Ny8=">Uehommachi, Tennoji, Southern Osaka</span> <span class="categories__item js-link js-link-new" data-internal="aHR0cHM6Ly93d3cuaG90ZWxzLWluLW9zYWthLmNvbS9lbi90eXBlL2FwYXJ0bWVudHMtMy8=">Apartment</span> </div> <div class="m-b-20 js-new-main-facilities hide-mob"> <h3 class="facilities-page-title m-t-20">Facilities</h3> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">General</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Free Wi-Fi in public areas</li></div> </ul> </div> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">Dining</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Electric kettle</li></div> </ul> </div> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">Room Amenities</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Sitting area</li><li>Washing machine</li></div> <div class="column"><li>Free toiletries</li><li>Parquet floor</li></div> </ul> </div> </div> <div class="mob-main-facilities show-mob clearfix"> <div class="clearfix"><h2 class="facilities-page-title m-t-20">Facilities</h2><a href="#service">Show All</a></div> <div class="facilities-block m-t-20"> <ul class="facilities js-facilities-main-mob clearfix m-t-10"> <li>Free Wi-Fi in public areas</li> </ul> </div> </div> <div class="hotel-description-policy m-b-20"> <h3 class="hotel-description-policy__title">Important information</h3> <div data="checktime"> <div> <b>Check-in:</b> <span>from 16:00 until 23:59</span> </div> <div> <b>Check-out:</b> <span>from 09:00 until 10:00</span> </div> </div> <div data="license_number"><div><b>Licence number</b><span>大阪市指令大保環第17-88號</span></div></div><div data="beds"><ul><li><b>Extra beds details</b></li><li>Cribs are not provided on site.</li></ul></div> </div> <div class="js-important-information-container"></div> <section class="hotel-faq wrp"> <h2 class="hotel-faq__title">Frequently asked questions</h2> <ul class="hotel-faq__list js-faq-accordion"> <li class="hotel-faq__item js-faq-accordion-wrapper"> <div class="hotel-faq__item-header js-faq-accordion-btn"> <div class="hotel-faq__item-title"> <div class="hotel-faq__item-emoji"> <span class="hotel-faq__item-pic">❓</span> </div> <h3 class="hotel-faq__item-question">Where is Chuan House Osaka apartment placed?</h3> </div> <div class="hotel-faq__item-toggle"> <svg class="hotel-faq__item-toggle-arrow icon icon-arrow" height="8" width="12"> <use xlink:href="/build/sprites/img/sprite.svg#icon-arrow"></use> </svg> </div> </div> <div class="hotel-faq__item-content js-faq-accordion-content"> <div class="hotel-faq__item-text">Chuan House Osaka apartment is set in the Uehommachi, Tennoji, Southern Osaka district, a 10-minute ride from Kuromon Fresh Food Market.</div> </div> </li> <li class="hotel-faq__item js-faq-accordion-wrapper"> <div class="hotel-faq__item-header js-faq-accordion-btn"> <div class="hotel-faq__item-title"> <div class="hotel-faq__item-emoji"> <span class="hotel-faq__item-pic">❓</span> </div> <h3 class="hotel-faq__item-question">What famous attractions are close to Chuan House Osaka apartment?</h3> </div> <div class="hotel-faq__item-toggle"> <svg class="hotel-faq__item-toggle-arrow icon icon-arrow" height="8" width="12"> <use xlink:href="/build/sprites/img/sprite.svg#icon-arrow"></use> </svg> </div> </div> <div class="hotel-faq__item-content js-faq-accordion-content"> <div class="hotel-faq__item-text">Popular spots near Chuan House Osaka apartment include the highly photogenic Dotonbori, Kuromon Fresh Food Market, Osaka International Peace Center, among others.</div> </div> </li> <li class="hotel-faq__item js-faq-accordion-wrapper"> <div class="hotel-faq__item-header js-faq-accordion-btn"> <div class="hotel-faq__item-title"> <div class="hotel-faq__item-emoji"> <span class="hotel-faq__item-pic">❓</span> </div> <h3 class="hotel-faq__item-question">How much does it cost to stay at Chuan House Osaka apartment?</h3> </div> <div class="hotel-faq__item-toggle"> <svg class="hotel-faq__item-toggle-arrow icon icon-arrow" height="8" width="12"> <use xlink:href="/build/sprites/img/sprite.svg#icon-arrow"></use> </svg> </div> </div> <div class="hotel-faq__item-content js-faq-accordion-content"> <div class="hotel-faq__item-text">The price of renting Chuan House Osaka apartment is $79.</div> </div> </li> <li class="hotel-faq__item js-faq-accordion-wrapper"> <div class="hotel-faq__item-header js-faq-accordion-btn"> <div class="hotel-faq__item-title"> <div class="hotel-faq__item-emoji"> <span class="hotel-faq__item-pic">❓</span> </div> <h3 class="hotel-faq__item-question">What is the time for check-in and check-out at the apartment?</h3> </div> <div class="hotel-faq__item-toggle"> <svg class="hotel-faq__item-toggle-arrow icon icon-arrow" height="8" width="12"> <use xlink:href="/build/sprites/img/sprite.svg#icon-arrow"></use> </svg> </div> </div> <div class="hotel-faq__item-content js-faq-accordion-content"> <div class="hotel-faq__item-text">Guests of the apartment may check in from 16:00, and check out until 10:00.</div> </div> </li> <li class="hotel-faq__item js-faq-accordion-wrapper"> <div class="hotel-faq__item-header js-faq-accordion-btn"> <div class="hotel-faq__item-title"> <div class="hotel-faq__item-emoji"> <span class="hotel-faq__item-pic">❓</span> </div> <h3 class="hotel-faq__item-question">Does Chuan House Osaka apartment provide free Wi-Fi?</h3> </div> <div class="hotel-faq__item-toggle"> <svg class="hotel-faq__item-toggle-arrow icon icon-arrow" height="8" width="12"> <use xlink:href="/build/sprites/img/sprite.svg#icon-arrow"></use> </svg> </div> </div> <div class="hotel-faq__item-content js-faq-accordion-content"> <div class="hotel-faq__item-text">The internet is provided for free at Chuan House Osaka apartment.</div> </div> </li> </ul> </section> </div> </div> <div class="hotel-page js-page" id="service" style="display: none;"> <div class="hotel-description-main facilities-wrap js-facilities-new-wrap pull-right"> <h2 class="hotel-page__title">Facilities</h2> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">General</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Free Wi-Fi in public areas</li></div> </ul> </div> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">Dining</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Electric kettle</li></div> </ul> </div> <div class="facilities-block m-t-20 js-mob-facilities"> <h4 class="facilities-block__title">Room Amenities</h4> <ul class="facilities js-facilities clearfix m-t-10"> <div class="column"><li>Sitting area</li><li>Washing machine</li></div> <div class="column"><li>Free toiletries</li><li>Parquet floor</li></div> </ul> </div> </div> </div> <script type="text/javascript">
	travel.opts.map_params = {
		provider:   '2',
				key:        ''
						,
				lat:        '34.67369',
				long:       '135.53606'
			};

</script> <div class="hotel-page js-page" id="map" style="display: none;"> <div class="hotel-description-main pull-right location-page"> <h2 class="hotel-page__title m-b-20">Location</h2> <div class="hide-map-block js-hotel-name">Chuan House</div> <div class="hide-map-block js-hotel-placeholder">Current location</div> <div class="main-map-inner"> <div class="main-map-wrap mapboxgl-map" id="main-map"><div class="mapboxgl-canary" style="visibility: hidden;"></div><div class="mapboxgl-canvas-container mapboxgl-interactive mapboxgl-touch-drag-pan mapboxgl-touch-zoom-rotate"><canvas aria-label="Map" class="mapboxgl-canvas" height="400" style="width: 826px; height: 400px;" tabindex="0" width="826"></canvas><div class="marker mapboxgl-marker mapboxgl-marker-anchor-bottom" style="transform: translate(-50%, -100%) translate(413px, 200px) rotateX(0deg) rotateZ(0deg);"><svg class="hotel-marker" height="70px" version="1.1" viewbox="0 0 34 50" width="50px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"> <defs></defs> <g fill="none" fill-rule="evenodd" id="Hotel-list" stroke="none" stroke-width="1"> <g fill-rule="nonzero" id="pin-(14)"> <path d="M16.6992188,0 C7.509375,0 0.0325195313,7.47685547 0.0325195313,16.6666992 C0.0325195313,19.4254883 0.722265625,22.1608398 2.03349609,24.5870117 L15.787793,49.4628906 C15.9708984,49.7945312 16.3198242,50 16.6992188,50 C17.0786133,50 17.4275391,49.7945312 17.6106445,49.4628906 L31.3700195,24.5788086 C32.6761719,22.1608398 33.365918,19.4253906 33.365918,16.6666016 C33.365918,7.47685547 25.8890625,0 16.6992188,0 Z" fill="" id="Shape"></path> <path class="hotel-marker__bg" d="M16.6992187,0 C7.509375,0 0.0325195312,7.47685547 0.0325195312,16.6666992 C0.0325195312,19.4254883 0.722265625,22.1608398 2.03349609,24.5870117 L15.787793,49.4628906 C15.9708984,49.7945312 16.3198242,50 16.6992187,50 C17.0786133,50 17.4275391,49.7945312 17.6106445,49.4628906 L31.3700195,24.5788086 C32.6761719,22.1608398 33.365918,19.4253906 33.365918,16.6666016 C33.365918,7.47685547 25.8890625,0 16.6992187,0 Z M16.6992187,25 C12.1042969,25 8.36591797,21.2616211 8.36591797,16.6666992 C8.36591797,12.0717773 12.1042969,8.33339844 16.6992187,8.33339844 C21.2941406,8.33339844 25.0325195,12.0717773 25.0325195,16.6666992 C25.0325195,21.2616211 21.2941406,25 16.6992187,25 Z" fill="#005394" id="Shape"></path> </g> </g> </svg></div></div><div class="mapboxgl-control-container"><div class="mapboxgl-ctrl-top-left"></div><div class="mapboxgl-ctrl-top-right"></div><div class="mapboxgl-ctrl-bottom-left"><div class="mapboxgl-ctrl" style="display: none;"></div></div><div class="mapboxgl-ctrl-bottom-right"><div class="mapboxgl-ctrl mapboxgl-ctrl-group"><button aria-label="Zoom in" class="mapboxgl-ctrl-zoom-in" title="Zoom in" type="button"><span aria-hidden="true" class="mapboxgl-ctrl-icon"></span></button><button aria-label="Zoom out" class="mapboxgl-ctrl-zoom-out" title="Zoom out" type="button"><span aria-hidden="true" class="mapboxgl-ctrl-icon"></span></button><button aria-label="Reset bearing to north" class="mapboxgl-ctrl-compass" title="Reset bearing to north" type="button"><span aria-hidden="true" class="mapboxgl-ctrl-icon" style="transform: rotate(0deg);"></span></button></div><div class="mapboxgl-ctrl mapboxgl-ctrl-attrib"><div class="mapboxgl-ctrl-attrib-inner"><a href="https://daylightmap.org/" target="_blank">DaylightMap</a> | <a href="https://www.openstreetmap.org/copyright" target="_blank">© OpenStreetMap contributors</a> | Building data -                 <a href="https://github.com/microsoft/AustraliaBuildingFootprints/" target="_blank">Australia</a>,                 <a href="https://github.com/microsoft/CanadianBuildingFootprints" target="_blank">Canada</a>,                 <a href="https://github.com/microsoft/Uganda-Tanzania-Building-Footprints" target="_blank">Uganda/Tanzania</a>,                 <a href="https://github.com/microsoft/USBuildingFootprints" target="_blank">US</a></div></div></div></div></div> </div> <div class="local-attractions-wrap hide-mob"> <div class="locations-nearby_list"> <div class="locations-nearby clearfix"> <div><ul><li><b>Local attractions</b></li><li><div>Tamatsukuri Inari Shrine</div><div>750 m</div></li><li><div>Yasaka Shrine</div><div>300 m</div></li><li><div>Life Imazato</div><div>700 m</div></li><li><div>Kasasagiri Morinomiya Shrine</div><div>850 m</div></li><li><div>Yasaka-jinja Shrine</div><div>300 m</div></li><li><div>Hachioji Shrine</div><div>550 m</div></li><li><div>Sanadayama Army Cemetery</div><div>750 m</div></li><li><div>Old Town Name Succesion Monument Kitakokubuncho</div><div>750 m</div></li></ul></div><div><ul><li><b>Airports</b></li><li><div>Itami</div><div>22 km</div></li></ul></div><div><ul><li><b>Train stations</b></li><li><div>Tsuruhashi Station</div><div>1.1 km</div></li></ul></div> </div> </div> </div> </div> </div> <div class="hotel-page js-page js-hotel-description" id="rooms" style="display: none;"> <div class="hotel-description-main pull-right"> <h2 class="hotel-page__title">Rooms &amp; Availability</h2> <div class="rooms-descriptions"> <p>Featuring a balcony and a seating area along with a sofa set and conveniences like air conditioning, Chuan House Osaka Apartment promises a delightful stay. Also, this accommodation comes with a tub, a walk-in shower, and a separate toilet in the bathroom. Furthermore, hairdryers, bath sheets, and slippers is available.</p> </div> <form action="/rooms/" id="form_result" method="post" name="form_result"> <input id="page" name="page" type="hidden" value="search_list"/> <input id="jid" name="jid" type="hidden" value=""/> <input id="lang" name="lang" type="hidden" value="1"/> <input id="curr" name="currency" type="hidden" value="1"/> <input id="h" name="h" type="hidden" value="2158254"/> <input id="u" name="u" type="hidden" value="0"/> </form> <div class="results js-rooms-cont"> <p class="str-warning">Please, select dates to see available rooms.</p> </div> <div class="hidden" id="splash"> <div id="splashdiv"> <div class="avail"> <div class="js-rooms"> <h2>Checking available rooms</h2> <p class="searching">Searching rooms…</p> </div> <div class="js-hotels" style="display: none"> <h2>Checking available hotels</h2> <p class="searching">Searching hotels…</p> </div> <div id="progress-bar"> <div id="ballsWaveG"> <div class="ballsWaveG" id="ballsWaveG_1"></div> <div class="ballsWaveG" id="ballsWaveG_2"></div> <div class="ballsWaveG" id="ballsWaveG_3"></div> <div class="ballsWaveG" id="ballsWaveG_4"></div> <div class="ballsWaveG" id="ballsWaveG_5"></div> <div class="ballsWaveG" id="ballsWaveG_6"></div> <div class="ballsWaveG" id="ballsWaveG_7"></div> <div class="ballsWaveG" id="ballsWaveG_8"></div> </div> </div> </div> </div> </div> </div> </div> <div class="hotel-page js-page" id="photo" style="display: none;"> <div class="gallery-wrap pull-right"> <h2 class="hotel-page__title">Gallery</h2> <div class="photos-list js-gallery"> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556799/osaka-chuan-house-apartment-photo-1.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/300x300w/5375/537582/537582063/osaka-chuan-house-apartment-photo-2.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/300x300w/4849/484944/484944252/osaka-chuan-house-apartment-photo-3.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945149/osaka-chuan-house-apartment-photo-4.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945353/osaka-chuan-house-apartment-photo-5.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945779/osaka-chuan-house-apartment-photo-6.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945830/osaka-chuan-house-apartment-photo-7.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437628/437628537/osaka-chuan-house-apartment-photo-8.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437628/437628570/osaka-chuan-house-apartment-photo-9.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437630/437630580/osaka-chuan-house-apartment-photo-10.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437661/437661639/osaka-chuan-house-apartment-photo-11.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/300x300w/4410/441073/441073653/osaka-chuan-house-apartment-photo-12.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/300x300w/4410/441080/441080952/osaka-chuan-house-apartment-photo-13.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437627/437627346/osaka-chuan-house-apartment-photo-14.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556796/osaka-chuan-house-apartment-photo-15.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437627/437627379/osaka-chuan-house-apartment-photo-16.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4410/441073/441073848/osaka-chuan-house-apartment-photo-17.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556829/osaka-chuan-house-apartment-photo-18.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/300x300w/5375/537582/537582042/osaka-chuan-house-apartment-photo-19.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556847/osaka-chuan-house-apartment-photo-20.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4410/441080/441080928/osaka-chuan-house-apartment-photo-21.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437627/437627040/osaka-chuan-house-apartment-photo-22.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556805/osaka-chuan-house-apartment-photo-23.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House * Osaka" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556790/osaka-chuan-house-apartment-photo-24.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/5375/537581/537581988/osaka-chuan-house-apartment-photo-25.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945317/osaka-chuan-house-apartment-photo-26.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment Osaka" class="lazy" data-original="/data/Photos/300x300w/4410/441072/441072426/osaka-chuan-house-apartment-photo-27.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556841/osaka-chuan-house-apartment-photo-28.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4411/441102/441102708/osaka-chuan-house-apartment-photo-29.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4849/484944/484944225/osaka-chuan-house-apartment-photo-30.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945797/osaka-chuan-house-apartment-photo-31.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment" class="lazy" data-original="/data/Photos/300x300w/4849/484945/484945782/osaka-chuan-house-apartment-photo-32.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/5375/537584/537584559/osaka-chuan-house-apartment-photo-33.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House Apartment *" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556814/osaka-chuan-house-apartment-photo-34.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House Osaka" class="lazy" data-original="/data/Photos/300x300w/4376/437661/437661597/osaka-chuan-house-apartment-photo-35.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Apartment Chuan House" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556823/osaka-chuan-house-apartment-photo-36.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556838/osaka-chuan-house-apartment-photo-37.JPEG" height="170" src="/_.gif" width="270"/> </p> <p class="photos-item photo-hover"> <img alt="Chuan House *" class="lazy" data-original="/data/Photos/300x300w/4215/421556/421556808/osaka-chuan-house-apartment-photo-38.JPEG" height="170" src="/_.gif" width="270"/> </p> </div> </div> </div> <div class="hotel-page js-page" id="write-review" style="display: none;"> <div class="contact-wrap add-review"> <div class="top100ad-reviews-our js-reviews-our clearfix"> <h3 class="contact-title hotel-page__title">Reviews</h3> <div class="top100ad-reviews-our__inner"> <div class="top100ad-reviews-our__item top100ad-review-our js-review-our"> <div class="top100ad-review-our__head"> <div class="top100ad-review-our__logo"> <span class="top100ad-review-our__initials">
																						C
													</span> <div class="top100ad-review-our__logo-bg"></div> <img alt="us" class="top100ad-review-our__flag" src="/images/flags/us.svg"/> </div> <div class="top100ad-review-our__info"> <div class="top100ad-review-our__info-head"> <p class="top100ad-review-our__name">Calvin</p> <div class="top100ad-review-our__rating"> <b>6</b>/10
							</div> </div> <p class="top100ad-review-our__date">2022-05-04 </p> </div> </div> <div class="top100ad-review-our__text top100ad-review-our__text--positive"> <div class="top100ad-review-our__icon"> <svg class="icon icon-reviews-plus"><use xlink:href="/build/sprites/img/sprite.svg#icon-reviews-plus"></use></svg> </div> <p>everything was perfect, the apartment was cute! overall had such wonderful stay at the property; location and rooms were very good. it had an exceptionally comfortable bed. spacious, spacious bathroom. parking was free. relatively close to sanadayama sanko shrine, walkable.</p> </div> <div class="top100ad-review-our__text top100ad-review-our__text--negative"> <div class="top100ad-review-our__icon"> <svg class="icon icon-reviews-minus"><use xlink:href="/build/sprites/img/sprite.svg#icon-reviews-minus"></use></svg> </div> <p>Something as tv missing. </p> </div> </div> <div class="top100ad-reviews-our__item top100ad-review-our js-review-our"> <div class="top100ad-review-our__head"> <div class="top100ad-review-our__logo"> <span class="top100ad-review-our__initials">
																						J
													</span> <div class="top100ad-review-our__logo-bg"></div> <img alt="us" class="top100ad-review-our__flag" src="/images/flags/us.svg"/> </div> <div class="top100ad-review-our__info"> <div class="top100ad-review-our__info-head"> <p class="top100ad-review-our__name">Joe</p> <div class="top100ad-review-our__rating"> <b>7</b>/10
							</div> </div> <p class="top100ad-review-our__date">2021-12-11 </p> </div> </div> <div class="top100ad-review-our__text top100ad-review-our__text--positive"> <div class="top100ad-review-our__icon"> <svg class="icon icon-reviews-plus"><use xlink:href="/build/sprites/img/sprite.svg#icon-reviews-plus"></use></svg> </div> <p>Cozy, neat apartment with a beautiful garden. The ocean view here is impressive...</p> </div> </div> <div class="top100ad-reviews-our__item top100ad-review-our js-review-our"> <div class="top100ad-review-our__head"> <div class="top100ad-review-our__logo"> <span class="top100ad-review-our__initials">
																						W
													</span> <div class="top100ad-review-our__logo-bg"></div> <img alt="us" class="top100ad-review-our__flag" src="/images/flags/us.svg"/> </div> <div class="top100ad-review-our__info"> <div class="top100ad-review-our__info-head"> <p class="top100ad-review-our__name">William</p> <div class="top100ad-review-our__rating"> <b>7</b>/10
							</div> </div> <p class="top100ad-review-our__date">2021-01-23 </p> </div> </div> <div class="top100ad-review-our__text top100ad-review-our__text--positive"> <div class="top100ad-review-our__icon"> <svg class="icon icon-reviews-plus"><use xlink:href="/build/sprites/img/sprite.svg#icon-reviews-plus"></use></svg> </div> <p>Fantastic view, absolutely good location. Free wifi was a plus. Beds were comfy and it had a kitchen. </p> </div> <div class="top100ad-review-our__text top100ad-review-our__text--negative"> <div class="top100ad-review-our__icon"> <svg class="icon icon-reviews-minus"><use xlink:href="/build/sprites/img/sprite.svg#icon-reviews-minus"></use></svg> </div> <p>Nothing everything was great. </p> </div> </div> </div> <div class="top100ad-reviews-our__pagination js-reviews-our-pagination"></div> </div> <div class="contact clearfix"> <h3 class="contact-title hotel-page__title m-b-15">Write a review</h3> <form action="/" class="js-write-review-form" id="form-review" method="post" novalidate="novalidate"> <input name="page" type="hidden" value="add_review"/> <input name="bid" type="hidden" value="0"/> <div class="m-b-15"> <strong class="contact-strong-text">Your details</strong> </div> <div class="form-field-wrap"> <label class="contact-lable" for="f_name">Name:</label> <div class="f_entire-wrap"> <input class="input-text" id="f_name" name="f_name"/> </div> </div> <div class="form-field-wrap"> <label class="contact-lable f_email-label" for="f_email">Email:</label> <div class="f_entire-wrap"> <input class="input-text" id="f_email" name="f_email"/> </div> </div> <div class="form-field-wrap full-width"> <label class="contact-lable" for="f_month">When did you travel?</label> <div class="f_entire-wrap f_select-wrap"> <select class="contact-select" id="f_month" name="f_month"> <option value="">Month</option> <option value="1">January</option> <option value="2">February</option> <option value="3">March</option> <option value="4">April</option> <option value="5">May</option> <option value="6">June</option> <option value="7">July</option> <option value="8">August</option> <option value="9">September</option> <option value="10">October</option> <option value="11">November</option> <option value="12">December</option> </select> </div> <div class="f_entire-wrap f_select-wrap"> <select class="contact-select" id="f_year" name="f_year"> <option value="">Year</option> <option value="2025">2025</option> <option value="2024">2024</option> <option value="2023">2023</option> </select> </div> </div> <div class="form-field-wrap full-width"> <strong class="contact-strong-text">Your impression of the property</strong> </div> <div class="form-field-wrap radio-btn-outwrap"> <label class="contact-lable" for="f_review">Rate this property</label> <div class="f_entire-wrap"> <div class="radio-buttons-wrap"> <span class="top-text">Very bad</span> <span class="top-text right">Excellent</span> <div class="numberstop"> <label class="" for="radio1">1</label> <label class="" for="radio2">2</label> <label class="" for="radio3">3</label> <label class="" for="radio4">4</label> <label class="" for="radio5">5</label> <label class="" for="radio6">6</label> <label class="" for="radio7">7</label> <label class="" for="radio8">8</label> <label class="" for="radio9">9</label> <label class="" for="radio10">10</label> </div> <div class="radio-btn-list"> <div class="radio-btn-item"> <input class="css-checkbox" id="radio1" name="f_rating" type="radio" value="1"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio2" name="f_rating" type="radio" value="2"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio3" name="f_rating" type="radio" value="3"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio4" name="f_rating" type="radio" value="4"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio5" name="f_rating" type="radio" value="5"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio6" name="f_rating" type="radio" value="6"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio7" name="f_rating" type="radio" value="7"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio8" name="f_rating" type="radio" value="8"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio9" name="f_rating" type="radio" value="9"/> </div> <div class="radio-btn-item"> <input class="css-checkbox" id="radio10" name="f_rating" type="radio" value="10"/> </div> </div> </div> </div> </div> <div class="form-field-wrap"> <label class="contact-lable contact-lable-textarea" for="f_review"><i class="icn-sprite icn-pros-ico"></i>Pros</label> <div class="f_entire-wrap"> <textarea class="contact-textarea" cols="50" id="f_review" name="f_review" rows="6"></textarea> </div> </div> <div class="form-field-wrap"> <label class="contact-lable contact-lable-textarea" for="f_review_cons"><i class="icn-sprite icn-cons-ico"></i>Cons</label> <div class="f_entire-wrap"> <textarea class="contact-textarea" cols="50" id="f_review_cons" name="f_review_cons" rows="6"></textarea> </div> <input name="t_user_mess" type="text" value=""/> </div> <div class="button-row full-width"> <input class="button send-mail-btn" type="submit" value="Send"/> </div> <input name="mxgf7pqs1g9" type="hidden" value="23dcddb03a2298035ee579f009be17c9"/></form> </div> </div> </div> <div class="hotel-page js-page" id="contact" style="display: none;"> <div class="contact-wrap contact-us pull-right"> <h2 class="hotel-page__title contact-wrap__title">Contact Us</h2> <div class="groupbook"> <div class="groupbook__wrap"> <div class="groupbook__text">If you need more than 2 rooms, please <a class="js-link-groupbooking" href="#">request group booking.</a></div> </div> </div> <div class="contact m-t-15"> <form action="#contact" class="js-have-redirect-to-gb" id="form-feedback" method="post" novalidate="novalidate"> <div class="form-field-wrap"> <label class="contact-lable" for="f_name">Name:</label> <input class="input-text" id="f_name" name="f_name"/> </div> <div class="form-field-wrap"> <label class="contact-lable" for="f_email">Email:</label> <input class="input-text" id="f_email" name="f_email" type="email"/> </div> <div class="form-field-wrap"> <label class="contact-lable" for="f_subject">Subject:</label> <select class="contact-select" id="f_subject" name="f_subject"> <option selected="selected" value="">Choose subject…</option> <option value="15">Are you a property owner who needs help?</option> <option value="1">Change booking</option> <option value="2">Cancel booking</option> <option value="7">I did not stay at the hotel</option> <option value="4">Hotel info</option> <option value="3">Partnership</option> <option value="6">Other</option> <option value="9">Check prices and availability</option> <option value="10">Group booking (for business clients)</option> <option value="11">Group booking (for travel agencies)</option> <option value="12">Request my personal data</option> <option value="13">Remove my personal data</option> <option value="14">Legal and law-related matters</option> </select> </div> <div class="form-field-wrap"> <label class="contact-lable" for="f_message">Message:</label> <textarea class="contact-textarea" cols="50" id="f_message" name="f_message" rows="10"></textarea> <input name="t_user_mess" type="text" value=""/> </div> <div class="button-row"> <input class="button send-mail-btn" type="submit" value="Send"/> </div> <div class="js-redirect-to-gb redirect-to-gb"> </div> <input name="mxgf7pqs1g9" type="hidden" value="23dcddb03a2298035ee579f009be17c9"/><input name="page" type="hidden" value=""/></form> </div> </div> </div> <div class="hotel-left pull-left"> <div class="menu-wrap hide-mob"><script type="text/javascript">
	travel.opts.messages = {
				f_name:    'Please enter your name',
				f_email:   'Please enter a valid email address',
				f_subject: 'Please choose subject',
				f_message: 'Please enter message',
				f_review:  'Please enter text',
				f_review_cons:  'Please enter text',
				f_month:   'Please choose month',
				f_year:    'Please choose year',
				f_rating:  'Please rate this property',
        		f_calendar:'Enter your dates and check our special offers',
    };
				travel.opts.k = 'mxgf7pqs1g9';
			travel.opts.v = '23dcddb03a2298035ee579f009be17c9';
	</script> <div class="desktop-ver"> <div class="show-mob clearfix"> <h3 class="menu-title show-mob js-menu-but"> <span class="js-text-menu"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Overview
					</span> <b class="menu-open"></b> <b class="menu-close"></b> </h3> <i class="show-mob icn-sprite icn-menu-icon-2 menu-title-img js-menu-but"> </i> </div> <div class="menu-col"> <ul class="l-menu clearfix js-hotel-menu"> <li> <a class="active" href="#main"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Overview
					</a> </li> <li> <a href="#rooms"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Rooms
					</a> </li> <li> <a href="#map"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Location
					</a> </li> <li> <a href="#service"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Facilities
					</a> </li> <li> <a href="#photo"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
						Gallery
					</a> </li> <li> <a href="#write-review"> <i class="icn-sprite icn-menu-arr"></i> <i class="icn-sprite icn-menu-arr-active"></i>
                        Reviews					</a> </li> </ul> </div> </div> </div> <div class="right-col-border"></div> <div class="map-deck-block"> <div class="map-deck-wrap"> <h4 class="map__title m-b-10">How to reach hotel</h4> <div class="map-deck clearfix"> <a class="location-map pull-left" href="#map"> <div class="hotel-map-small clearfix mapboxgl-map" id="small-map"><div class="mapboxgl-canary" style="visibility: hidden;"></div><div class="mapboxgl-canvas-container mapboxgl-interactive mapboxgl-touch-drag-pan mapboxgl-touch-zoom-rotate"><canvas aria-label="Map" class="mapboxgl-canvas" height="124" style="width: 254px; height: 124px;" tabindex="0" width="254"></canvas><div class="marker mapboxgl-marker mapboxgl-marker-anchor-bottom" style="transform: translate(-50%, -100%) translate(127px, 62px) rotateX(0deg) rotateZ(0deg);"><svg class="hotel-marker" height="40px" version="1.1" viewbox="0 0 34 50" width="20px" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"> <defs></defs> <g fill="none" fill-rule="evenodd" id="Hotel-list" stroke="none" stroke-width="1"> <g fill-rule="nonzero" id="pin-(14)"> <path d="M16.6992188,0 C7.509375,0 0.0325195313,7.47685547 0.0325195313,16.6666992 C0.0325195313,19.4254883 0.722265625,22.1608398 2.03349609,24.5870117 L15.787793,49.4628906 C15.9708984,49.7945312 16.3198242,50 16.6992188,50 C17.0786133,50 17.4275391,49.7945312 17.6106445,49.4628906 L31.3700195,24.5788086 C32.6761719,22.1608398 33.365918,19.4253906 33.365918,16.6666016 C33.365918,7.47685547 25.8890625,0 16.6992188,0 Z" fill="" id="Shape"></path> <path class="hotel-marker__bg" d="M16.6992187,0 C7.509375,0 0.0325195312,7.47685547 0.0325195312,16.6666992 C0.0325195312,19.4254883 0.722265625,22.1608398 2.03349609,24.5870117 L15.787793,49.4628906 C15.9708984,49.7945312 16.3198242,50 16.6992187,50 C17.0786133,50 17.4275391,49.7945312 17.6106445,49.4628906 L31.3700195,24.5788086 C32.6761719,22.1608398 33.365918,19.4253906 33.365918,16.6666016 C33.365918,7.47685547 25.8890625,0 16.6992187,0 Z M16.6992187,25 C12.1042969,25 8.36591797,21.2616211 8.36591797,16.6666992 C8.36591797,12.0717773 12.1042969,8.33339844 16.6992187,8.33339844 C21.2941406,8.33339844 25.0325195,12.0717773 25.0325195,16.6666992 C25.0325195,21.2616211 21.2941406,25 16.6992187,25 Z" fill="#005394" id="Shape"></path> </g> </g> </svg></div></div><div class="mapboxgl-control-container"><div class="mapboxgl-ctrl-top-left"></div><div class="mapboxgl-ctrl-top-right"></div><div class="mapboxgl-ctrl-bottom-left"><div class="mapboxgl-ctrl" style="display: none;"></div></div><div class="mapboxgl-ctrl-bottom-right"><div class="mapboxgl-ctrl mapboxgl-ctrl-attrib mapboxgl-compact"><div class="mapboxgl-ctrl-attrib-inner"><a href="https://daylightmap.org/" target="_blank">DaylightMap</a> | <a href="https://www.openstreetmap.org/copyright" target="_blank">© OpenStreetMap contributors</a> | Building data -                 <a href="https://github.com/microsoft/AustraliaBuildingFootprints/" target="_blank">Australia</a>,                 <a href="https://github.com/microsoft/CanadianBuildingFootprints" target="_blank">Canada</a>,                 <a href="https://github.com/microsoft/Uganda-Tanzania-Building-Footprints" target="_blank">Uganda/Tanzania</a>,                 <a href="https://github.com/microsoft/USBuildingFootprints" target="_blank">US</a></div></div></div></div></div> <div class="js-hotel-name hidden">Chuan House</div> </a> </div> </div> <div class="right-hotel-adress m-t-15"> <div class="right-hotel-adress__text"> <span class="">Hotel address:</span> <p class="right-address">1-5 Higashiobase, Osaka, Japan</p> </div> </div> <div class="right-col-border"></div> </div> </div> </div> <div class="price-popup js-price-popup"> <div class="price-popup-close-btn js-price-popup-close-btn"> <svg class="icon icon-close"> <use xlink:href="/build/sprites/img/sprite.svg#icon-close"></use> </svg> </div> <div class="price-popup-content js-price-popup-content"> <div class="price-popup-title"> <div class="price-popup-hotel-name">Chuan House Osaka Apartment</div> </div> <div class="price-popup-form"> <div class="availability-deck js-availbox-cont js-avail-scroll-point" id="availability-form"> <div class="reserv-title"> <span class="is-main-form">Online Reservation</span> <span class="is-for-popup">Enter your dates to see prices</span> </div> <form action="#rooms" autocomplete="off" class="js-avail-form" formtarget="#rooms" method="get"> <div class="new-calendar js-new-calendar-wrapper"> <fieldset class="form-fieldset js-new-calendar-from"> <span class="form-fieldset__title"> <label class="js-check-in-text" for="d_from">Check-in date:</label> </span> <div class="form-fieldset__calendar clearfix"> <input class="js-new-calendar-from-input" id="d_from" name="from" type="hidden" value="2025-08-06"/> <div class="js-calendar-text calendar_field">Wed 6 Aug 2025</div> </div> </fieldset> <fieldset class="form-fieldset js-new-calendar-to"> <span class="form-fieldset__title"> <label class="js-check-out-text" for="d_to">Check-out date:</label> </span> <div class="form-fieldset__calendar clearfix"> <input class="js-new-calendar-to-input" id="d_to" name="to" type="hidden" value="2025-08-07"/> <div class="js-calendar-text calendar_field">Thu 7 Aug 2025</div> </div> </fieldset> <div class="hidden js-plural-night-1"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="hidden js-plural-night-2"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="hidden js-plural-night-5"> <div class="js-plural-night-phrase">
					(<span class="js-night-count"></span>-night stay)</div> </div> <div class="date-picker-wrapper is-price-popup-calendar no-shortcuts inline-wrapper no-gap two-months" style="display: none; user-select: none;" unselectable="on"><div class="drp_top-bar normal"><div class="normal-top"><span class="selection-top">Selected: </span> <b class="start-day">2025-08-06</b> <span class="separator-day"> to </span> <b class="end-day">2025-08-07</b> <i class="selected-days" style="display: inline;">(<span class="selected-days-num">2</span> Days)</i></div><div class="error-top">error</div><div class="default-top">Please select a date range between 2 and 30 days</div><input class="apply-btn hide" type="button" value="Close"/></div><div class="month-wrapper"> <div class="month1 month js-month"> <div class="month-content"> <div class="month-head"> <div class="month-caption"> <div class="month-arrow month-arrow--next"> <span class="prev"><img class="calendar-arrow calendar-arrow--left icon icon-arrow" src="/templates/hotel33/images/arrow-758f4bc1ad.svg"/></span> </div> <div class="month-name"><div class="month-element">August</div> <div class="month-element">2025</div></div> <div class="month-arrow month-arrow--next"> </div> </div> <div class="week-name"><div class="week-name__item">Mo</div><div class="week-name__item">Tu</div><div class="week-name__item">We</div><div class="week-name__item">Th</div><div class="week-name__item">Fr</div><div class="week-name__item">Sa</div><div class="week-name__item">Su</div></div> </div> <div class="days-container js-days"><div class="days-row"><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753632000000">28</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753718400000">29</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753804800000">30</div></div><div class="day js-day-wrapper is-lastMonth"><div class="day-text js-day lastMonth invalid" data-tooltip="" time="1753891200000">31</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1753977600000">1</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754064000000">2</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754150400000">3</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754236800000">4</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth invalid" data-tooltip="" time="1754323200000">5</div></div><div class="day js-day-wrapper is-toMonth is-checked is-first-date-selected"><div class="day-text js-day toMonth valid real-today checked first-date-selected" data-tooltip="" time="1754409600000">6</div></div><div class="day js-day-wrapper is-toMonth is-checked is-last-date-selected"><div class="day-text js-day toMonth valid checked last-date-selected" data-tooltip="" time="1754496000000">7</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754582400000">8</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754668800000">9</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754755200000">10</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754841600000">11</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1754928000000">12</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755014400000">13</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755100800000">14</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755187200000">15</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755273600000">16</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755360000000">17</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755446400000">18</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755532800000">19</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755619200000">20</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755705600000">21</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755792000000">22</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755878400000">23</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1755964800000">24</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756051200000">25</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756137600000">26</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756224000000">27</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756310400000">28</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756396800000">29</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756483200000">30</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756569600000">31</div></div></div></div> </div> </div><div class="month-gap"></div><div class="month2 month js-month"> <div class="month-content"> <div class="month-head"> <div class="month-caption"> <div class="month-arrow month-arrow--next"> </div> <div class="month-name"><div class="month-element">September</div> <div class="month-element">2025</div></div> <div class="month-arrow month-arrow--next"> <span class="next"><img class="calendar-arrow calendar-arrow--right icon icon-arrow" src="/templates/hotel33/images/arrow-758f4bc1ad.svg"/></span> </div> </div> <div class="week-name"><div class="week-name__item">Mo</div><div class="week-name__item">Tu</div><div class="week-name__item">We</div><div class="week-name__item">Th</div><div class="week-name__item">Fr</div><div class="week-name__item">Sa</div><div class="week-name__item">Su</div></div> </div> <div class="days-container js-days"><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756656000000">1</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756742400000">2</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756828800000">3</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1756915200000">4</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757001600000">5</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757088000000">6</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757174400000">7</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757260800000">8</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757347200000">9</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757433600000">10</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757520000000">11</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757606400000">12</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757692800000">13</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757779200000">14</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757865600000">15</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1757952000000">16</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758038400000">17</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758124800000">18</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758211200000">19</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758297600000">20</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758384000000">21</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758470400000">22</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758556800000">23</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758643200000">24</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758729600000">25</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758816000000">26</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758902400000">27</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1758988800000">28</div></div></div><div class="days-row"><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1759075200000">29</div></div><div class="day js-day-wrapper is-toMonth"><div class="day-text js-day toMonth valid" data-tooltip="" time="1759161600000">30</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759248000000">1</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759334400000">2</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759420800000">3</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759507200000">4</div></div><div class="day js-day-wrapper is-nextMonth"><div class="day-text js-day nextMonth valid" data-tooltip="" time="1759593600000">5</div></div></div></div> </div> </div></div><div class="time"><div class="time1"></div><div class="time2"></div></div><div class="custom-footer js-custom-footer"><div class="custom-footer__content js-custom-footer"></div></div><div class="date-range-length-tip"></div></div></div> <div class="popup-form-container clearfix"> <div class="popup-form-wrap js-form-popup"> <div class="js-adalts-child-text" style="display: none;"> <div class="js-children-form1">Child</div> <div class="js-children-form2">Children</div> <div class="js-adults-form1">Adult</div> <div class="js-adults-form2">Adults</div> </div> <div class="popup-form-wrap__title">Guests:</div> <div class="popup-form clearfix"> <div class="popup-form-adults"> <span class="js-popup-form-adults">2</span> <span class="js-popup-form-adults-text">Adults</span> </div> <div class="popup-form-child" style="display: none;"> +
						<span class="js-popup-form-childs">0</span> <span class="js-popup-form-child-text">Children</span> </div> </div> </div> <div class="adults-and-childs-wrap js-adults-childs clearfix"> <div class="adults-and-childs"> <fieldset> <label for="adults">Adults:</label> <select class="sel" id="adults" name="adults"> <option value="1">1</option> <option selected="selected" value="2">2</option> <option value="3">3</option> <option value="4">4</option> <option value="5">5</option> <option value="6">6</option> <option value="7">7</option> <option value="8">8</option> <option value="9">9</option> <option value="10">10</option> <option value="11">11</option> <option value="12">12</option> <option value="13">13</option> <option value="14">14</option> <option value="15">15</option> <option value="16">16</option> <option value="17">17</option> <option value="18">18</option> <option value="19">19</option> <option value="20">20</option> </select> </fieldset> <fieldset> <label for="childs">Children:</label> <select class="js-childs" id="childs" name="children"> <option selected="selected" value="0">0</option> <option value="1">1</option> <option value="2">2</option> <option value="3">3</option> <option value="4">4</option> <option value="5">5</option> <option value="6">6</option> <option value="7">7</option> <option value="8">8</option> <option value="9">9</option> <option value="10">10</option> </select> </fieldset> <div class="childrens-age-wrap js-age-container" style="display: none;"> <div class="js-child-age-title">Ages of children:</div> </div> </div> <div class="error-age-message hidden js-ch-error js-ch-error-one">Specify the age of a child</div> <div class="error-age-message hidden js-ch-error js-ch-error-plural">Specify the age of children</div> <div class="adults-and-childs__btn-wrap"> <div class="adults-and-childs__btn-cancel js-cancel-btn">Cancel</div> <div class="adults-and-childs__btn-ok js-ok-btn">OK</div> </div> </div> </div> <button class="avail-form__submit js-avail-info-submit-btn" type="submit">Check availability</button> </form> </div> <script type="text/javascript">
	travel.opts.av_params = {
		use:      '0',		from:     '2025-08-06',
		to:       '2025-08-07',
		adults:   '2',
		children: '0'
		
	};
</script> </div> </div> </div> <div class="exit-popup js-exit-popup"> <div class="exit-popup-content js-exit-popup-content"> <div class="exit-popup-close-btn js-exit-popup-close-btn js-trackevent-close"> <svg class="icon icon-popup-close"> <use xlink:href="/build/sprites/img/sprite.svg#icon-popup-close"></use> </svg> <div class="exit-popup-close-btn-text">Close, I need more options</div> </div> <div class="exit-popup-decor"> <img alt="Compare prices and choose" class="exit-popup-img" height="180" src="/templates/hotel33/images/exit-popup-c4f6a226b1.svg" width="160"/> </div> <div class="exit-popup-form"> <div class="exit-popup-title">
                Travelling to Osaka?
			</div> <div class="exit-popup-text"> <p><b>Find your perfect place to stay!</b></p> <p>Great locations and deals for every budget.</p> </div> <div class="exit-popup-wrap clearfix"> <div class="exit-popup-column"> <div class="exit-popup-link js-exit-popup-clickunder js-trackevent-check-hotels">View deals</div> </div> <div class="exit-popup-column"> <div class="exit-popup-link is-invert js-exit-popup-no-thanks js-trackevent-no-thanks">No, thanks</div> <label class="exit-popup-check">
						Find on <span class="js-exit-popup-text"></span> <input checked="checked" class="exit-popup-checkbox js-exit-popup-checkbox" id="exit-clickunder" name="exit-clickunder" type="checkbox"/> <span class="exit-popup-checkbox-ico"></span> </label> </div> </div> </div> </div> </div> <div class="cookie-policy js-cookie-policy"> <div class="cookie-policy__wrapper"> <div class="cookie-policy__text">We use cookies to ensure that we give you the best experience on our website. If you continue to use this site, we will assume that you are happy with it.</div> <div class="cookie-policy__buttons"> <div class="cookie-policy__button cookie-policy__button--ok js-cookie-close-btn">OK</div> <div class="cookie-policy__button cookie-policy__button--no js-cookie-close-btn">NO</div> </div> </div> </div> </div> </div> </div> <div class="footer"> <div class="footer-top-wrap"> <div class="footer-top container clearfix"> <div class="hotel-title pull-left"> <h2>Chuan House Osaka Apartment, Japan</h2> <p class="m-t-5">1-5 Higashiobase, Osaka, Japan</p> <div class="links"> <p class="m-t-10">Hotel Reservation System | 2025 © hotels-in-osaka.com. All rights reserved | <a href="https://chuan-house.hotels-in-osaka.com/en/#contact">Contacts</a></p> </div> </div> </div> </div> <div class="footer-bottom container"> <div class="links js-links-container clearfix"> <div class="lnk"> <div class="lnk__title">
																									Popular hotels in Osaka
																							</div> <ul class="lnk__list"> </ul> </div> <div class="rlink_link" style="display: none"> <ul class="rlink_link__list"> </ul> </div> </div> </div> </div> <script src="https://www.hotels-in-osaka.com/build/packs/js/vendors-5c3e92ad7a.pack.js"></script><div class="lightboxOverlay" id="lightboxOverlay" style="display: none;"></div><div class="lightbox" id="lightbox" style="display: none;"><div class="lb-outerContainer"><div class="lb-container"><img class="lb-image" src="data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="/><div class="lb-nav"><a class="lb-prev" href=""></a><a class="lb-next" href=""></a></div><div class="lb-loader"><a class="lb-cancel"></a></div></div></div><div class="lb-dataContainer"><div class="lb-data"><div class="lb-details"><span class="lb-caption"></span><span class="lb-number"></span></div><div class="lb-closeContainer"><a class="lb-close"></a></div></div></div></div> <script src="https://www.hotels-in-osaka.com/build/packs/js/calendar-f64cd72405.pack.js"></script> <script src="https://www.hotels-in-osaka.com/build/packs/js/app-06b3dc9e1f.pack.js"></script> <div class="datepicker-text" style="display: none"></div></body></html>