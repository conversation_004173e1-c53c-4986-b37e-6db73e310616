<!DOCTYPE html>
<html class="pc" lang="ja"><head prefix="og: https://ogp.me/ns# fb: https://ogp.me/ns/fb#"><meta content="A7vZI3v+Gz7JfuRolKNM4Aff6zaGuT7X0mf3wtoZTnKv6497cVMnhy03KDqX7kBz/q/iidW7srW31oQbBt4VhgoAAACUeyJvcmlnaW4iOiJodHRwczovL3d3dy5nb29nbGUuY29tOjQ0MyIsImZlYXR1cmUiOiJEaXNhYmxlVGhpcmRQYXJ0eVN0b3JhZ2VQYXJ0aXRpb25pbmczIiwiZXhwaXJ5IjoxNzU3OTgwODAwLCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==" http-equiv="origin-trial"/>
<meta charset="utf-8"/>
<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge"><![endif]-->
<meta content="width=device-width" name="viewport"/>
<title>昼神温泉　ホテルひるがみの森</title>
<meta content="日本一の星空の里：阿智村。その昼神温泉の高台に佇む旅館「ホテルひるがみの森」。南信州の大自然、満天の星空、広々とした開放的な露天風呂、温泉街を一望できる大型ブランコ、室内プール、足湯、グランピング、BBQエリア、ドクターフィッシュなども楽しめる「星空の温泉リゾート」宿泊施設です。" name="description"/>
<style type="text/css">:host,:root{--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";--fa-font-regular:normal 400 1em/1 "Font Awesome 6 Free";--fa-font-light:normal 300 1em/1 "Font Awesome 6 Pro";--fa-font-thin:normal 100 1em/1 "Font Awesome 6 Pro";--fa-font-duotone:normal 900 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-regular:normal 400 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-light:normal 300 1em/1 "Font Awesome 6 Duotone";--fa-font-duotone-thin:normal 100 1em/1 "Font Awesome 6 Duotone";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands";--fa-font-sharp-solid:normal 900 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-regular:normal 400 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-light:normal 300 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-thin:normal 100 1em/1 "Font Awesome 6 Sharp";--fa-font-sharp-duotone-solid:normal 900 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-regular:normal 400 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-light:normal 300 1em/1 "Font Awesome 6 Sharp Duotone";--fa-font-sharp-duotone-thin:normal 100 1em/1 "Font Awesome 6 Sharp Duotone"}svg:not(:host).svg-inline--fa,svg:not(:root).svg-inline--fa{overflow:visible;box-sizing:content-box}.svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}.svg-inline--fa.fa-2xs{vertical-align:.1em}.svg-inline--fa.fa-xs{vertical-align:0}.svg-inline--fa.fa-sm{vertical-align:-.0714285705em}.svg-inline--fa.fa-lg{vertical-align:-.2em}.svg-inline--fa.fa-xl{vertical-align:-.25em}.svg-inline--fa.fa-2xl{vertical-align:-.3125em}.svg-inline--fa.fa-pull-left{margin-right:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-pull-right{margin-left:var(--fa-pull-margin,.3em);width:auto}.svg-inline--fa.fa-li{width:var(--fa-li-width,2em);top:.25em}.svg-inline--fa.fa-fw{width:var(--fa-fw-width,1.25em)}.fa-layers svg.svg-inline--fa{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0}.fa-layers-counter,.fa-layers-text{display:inline-block;position:absolute;text-align:center}.fa-layers{display:inline-block;height:1em;position:relative;text-align:center;vertical-align:-.125em;width:1em}.fa-layers svg.svg-inline--fa{transform-origin:center center}.fa-layers-text{left:50%;top:50%;transform:translate(-50%,-50%);transform-origin:center center}.fa-layers-counter{background-color:var(--fa-counter-background-color,#ff253a);border-radius:var(--fa-counter-border-radius,1em);box-sizing:border-box;color:var(--fa-inverse,#fff);line-height:var(--fa-counter-line-height,1);max-width:var(--fa-counter-max-width,5em);min-width:var(--fa-counter-min-width,1.5em);overflow:hidden;padding:var(--fa-counter-padding,.25em .5em);right:var(--fa-right,0);text-overflow:ellipsis;top:var(--fa-top,0);transform:scale(var(--fa-counter-scale,.25));transform-origin:top right}.fa-layers-bottom-right{bottom:var(--fa-bottom,0);right:var(--fa-right,0);top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom right}.fa-layers-bottom-left{bottom:var(--fa-bottom,0);left:var(--fa-left,0);right:auto;top:auto;transform:scale(var(--fa-layers-scale,.25));transform-origin:bottom left}.fa-layers-top-right{top:var(--fa-top,0);right:var(--fa-right,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top right}.fa-layers-top-left{left:var(--fa-left,0);right:auto;top:var(--fa-top,0);transform:scale(var(--fa-layers-scale,.25));transform-origin:top left}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-2xs{font-size:.625em;line-height:.1em;vertical-align:.225em}.fa-xs{font-size:.75em;line-height:.0833333337em;vertical-align:.125em}.fa-sm{font-size:.875em;line-height:.0714285718em;vertical-align:.0535714295em}.fa-lg{font-size:1.25em;line-height:.05em;vertical-align:-.075em}.fa-xl{font-size:1.5em;line-height:.0416666682em;vertical-align:-.125em}.fa-2xl{font-size:2em;line-height:.03125em;vertical-align:-.1875em}.fa-fw{text-align:center;width:1.25em}.fa-ul{list-style-type:none;margin-left:var(--fa-li-margin,2.5em);padding-left:0}.fa-ul>li{position:relative}.fa-li{left:calc(-1 * var(--fa-li-width,2em));position:absolute;text-align:center;width:var(--fa-li-width,2em);line-height:inherit}.fa-border{border-color:var(--fa-border-color,#eee);border-radius:var(--fa-border-radius,.1em);border-style:var(--fa-border-style,solid);border-width:var(--fa-border-width,.08em);padding:var(--fa-border-padding,.2em .25em .15em)}.fa-pull-left{float:left;margin-right:var(--fa-pull-margin,.3em)}.fa-pull-right{float:right;margin-left:var(--fa-pull-margin,.3em)}.fa-beat{animation-name:fa-beat;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-bounce{animation-name:fa-bounce;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.28,.84,.42,1))}.fa-fade{animation-name:fa-fade;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-beat-fade{animation-name:fa-beat-fade;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,cubic-bezier(.4,0,.6,1))}.fa-flip{animation-name:fa-flip;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,ease-in-out)}.fa-shake{animation-name:fa-shake;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin{animation-name:fa-spin;animation-delay:var(--fa-animation-delay,0s);animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,2s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,linear)}.fa-spin-reverse{--fa-animation-direction:reverse}.fa-pulse,.fa-spin-pulse{animation-name:fa-spin;animation-direction:var(--fa-animation-direction,normal);animation-duration:var(--fa-animation-duration,1s);animation-iteration-count:var(--fa-animation-iteration-count,infinite);animation-timing-function:var(--fa-animation-timing,steps(8))}@media (prefers-reduced-motion:reduce){.fa-beat,.fa-beat-fade,.fa-bounce,.fa-fade,.fa-flip,.fa-pulse,.fa-shake,.fa-spin,.fa-spin-pulse{animation-delay:-1ms;animation-duration:1ms;animation-iteration-count:1;transition-delay:0s;transition-duration:0s}}@keyframes fa-beat{0%,90%{transform:scale(1)}45%{transform:scale(var(--fa-beat-scale,1.25))}}@keyframes fa-bounce{0%{transform:scale(1,1) translateY(0)}10%{transform:scale(var(--fa-bounce-start-scale-x,1.1),var(--fa-bounce-start-scale-y,.9)) translateY(0)}30%{transform:scale(var(--fa-bounce-jump-scale-x,.9),var(--fa-bounce-jump-scale-y,1.1)) translateY(var(--fa-bounce-height,-.5em))}50%{transform:scale(var(--fa-bounce-land-scale-x,1.05),var(--fa-bounce-land-scale-y,.95)) translateY(0)}57%{transform:scale(1,1) translateY(var(--fa-bounce-rebound,-.125em))}64%{transform:scale(1,1) translateY(0)}100%{transform:scale(1,1) translateY(0)}}@keyframes fa-fade{50%{opacity:var(--fa-fade-opacity,.4)}}@keyframes fa-beat-fade{0%,100%{opacity:var(--fa-beat-fade-opacity,.4);transform:scale(1)}50%{opacity:1;transform:scale(var(--fa-beat-fade-scale,1.125))}}@keyframes fa-flip{50%{transform:rotate3d(var(--fa-flip-x,0),var(--fa-flip-y,1),var(--fa-flip-z,0),var(--fa-flip-angle,-180deg))}}@keyframes fa-shake{0%{transform:rotate(-15deg)}4%{transform:rotate(15deg)}24%,8%{transform:rotate(-18deg)}12%,28%{transform:rotate(18deg)}16%{transform:rotate(-22deg)}20%{transform:rotate(22deg)}32%{transform:rotate(-12deg)}36%{transform:rotate(12deg)}100%,40%{transform:rotate(0)}}@keyframes fa-spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}.fa-rotate-90{transform:rotate(90deg)}.fa-rotate-180{transform:rotate(180deg)}.fa-rotate-270{transform:rotate(270deg)}.fa-flip-horizontal{transform:scale(-1,1)}.fa-flip-vertical{transform:scale(1,-1)}.fa-flip-both,.fa-flip-horizontal.fa-flip-vertical{transform:scale(-1,-1)}.fa-rotate-by{transform:rotate(var(--fa-rotate-angle,0))}.fa-stack{display:inline-block;vertical-align:middle;height:2em;position:relative;width:2.5em}.fa-stack-1x,.fa-stack-2x{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;z-index:var(--fa-stack-z-index,auto)}.svg-inline--fa.fa-stack-1x{height:1em;width:1.25em}.svg-inline--fa.fa-stack-2x{height:2em;width:2.5em}.fa-inverse{color:var(--fa-inverse,#fff)}.fa-sr-only,.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.fa-sr-only-focusable:not(:focus),.sr-only-focusable:not(:focus){position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0}.svg-inline--fa .fa-primary{fill:var(--fa-primary-color,currentColor);opacity:var(--fa-primary-opacity,1)}.svg-inline--fa .fa-secondary{fill:var(--fa-secondary-color,currentColor);opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-primary{opacity:var(--fa-secondary-opacity,.4)}.svg-inline--fa.fa-swap-opacity .fa-secondary{opacity:var(--fa-primary-opacity,1)}.svg-inline--fa mask .fa-primary,.svg-inline--fa mask .fa-secondary{fill:#000}</style><link href="https://hirumori.co.jp/xmlrpc.php" rel="pingback"/>
<meta content="max-image-preview:large" name="robots"/>
<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
<meta content="website" property="og:type"/>
<meta content="https://hirumori.co.jp/" property="og:url"/>
<meta content="昼神温泉　ホテルひるがみの森" property="og:title"/>
<meta content="日本一の星空の里：阿智村。その昼神温泉の高台に佇む旅館「ホテルひるがみの森」。南信州の大自然、満天の星空、広々とした開放的な露天風呂、温泉街を一望できる大型ブランコ、室内プール、足湯、グランピング、BBQエリア、ドクターフィッシュなども楽しめる「星空の温泉リゾート」宿泊施設です。" property="og:description"/>
<meta content="昼神温泉　ホテルひるがみの森" property="og:site_name"/>
<meta content="https://hirumori.co.jp/wp-content/uploads/2025/05/eyecatch-100.jpg" property="og:image"/>
<meta content="https://hirumori.co.jp/wp-content/uploads/2025/05/eyecatch-100.jpg" property="og:image:secure_url"/>
<meta content="516" property="og:image:width"/>
<meta content="294" property="og:image:height"/>
<link href="//www.google.com" rel="dns-prefetch"/>
<link href="//www.googletagmanager.com" rel="dns-prefetch"/>
<link href="https://hirumori.co.jp/feed/" rel="alternate" title="昼神温泉　ホテルひるがみの森 » フィード" type="application/rss+xml"/>
<link href="https://hirumori.co.jp/comments/feed/" rel="alternate" title="昼神温泉　ホテルひるがみの森 » コメントフィード" type="application/rss+xml"/>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/style.css?ver=2.11.3" id="style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-includes/css/dist/block-library/style.min.css?ver=6.8.2" id="wp-block-library-css" media="all" rel="stylesheet" type="text/css"/>
<style id="classic-theme-styles-inline-css" type="text/css">
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/accordion/style-index.css?ver=21.0.6" id="snow-monkey-blocks-accordion-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-grid-style-inline-css" type="text/css">
.smb-grid{--smb-grid--gap:0px;--smb-grid--column-auto-repeat:auto-fit;--smb-grid--columns:1;--smb-grid--column-min-width:250px;--smb-grid--grid-template-columns:none;--smb-grid--rows:1;--smb-grid--grid-template-rows:none;display:grid;gap:var(--smb-grid--gap)}.smb-grid>*{--smb--justify-self:stretch;--smb--align-self:stretch;--smb--grid-column:auto;--smb--grid-row:auto;align-self:var(--smb--align-self);grid-column:var(--smb--grid-column);grid-row:var(--smb--grid-row);justify-self:var(--smb--justify-self);margin-bottom:0;margin-top:0;min-width:0}.smb-grid--columns\:columns{grid-template-columns:repeat(var(--smb-grid--columns),1fr)}.smb-grid--columns\:min{grid-template-columns:repeat(var(--smb-grid--column-auto-repeat),minmax(min(var(--smb-grid--column-min-width),100%),1fr))}.smb-grid--columns\:free{grid-template-columns:var(--smb-grid--grid-template-columns)}.smb-grid--rows\:rows{grid-template-rows:repeat(var(--smb-grid--rows),1fr)}.smb-grid--rows\:free{grid-template-rows:var(--smb-grid--grid-template-rows)}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/alert/style-index.css?ver=21.0.6" id="snow-monkey-blocks-alert-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-flex-style-inline-css" type="text/css">
.smb-flex{--smb-flex--box-shadow:none;box-shadow:var(--smb-flex--box-shadow);flex-direction:column}.smb-flex.is-horizontal{flex-direction:row}.smb-flex.is-vertical{flex-direction:column}.smb-flex>*{--smb--flex-grow:0;--smb--flex-shrink:1;--smb--flex-basis:auto;flex-basis:var(--smb--flex-basis);flex-grow:var(--smb--flex-grow);flex-shrink:var(--smb--flex-shrink);min-width:0}

</style>
<style id="snow-monkey-blocks-box-style-inline-css" type="text/css">
.smb-box{--smb-box--background-color:#0000;--smb-box--background-image:initial;--smb-box--background-opacity:1;--smb-box--border-color:var(--_lighter-color-gray);--smb-box--border-style:solid;--smb-box--border-width:0px;--smb-box--border-radius:var(--_global--border-radius);--smb-box--box-shadow:initial;--smb-box--color:inherit;--smb-box--padding:var(--_padding1);border-radius:var(--smb-box--border-radius);border-width:0;box-shadow:var(--smb-box--box-shadow);color:var(--smb-box--color);overflow:visible;padding:var(--smb-box--padding);position:relative}.smb-box--p-s{--smb-box--padding:var(--_padding-1)}.smb-box--p-l{--smb-box--padding:var(--_padding2)}.smb-box__background{background-color:var(--smb-box--background-color);background-image:var(--smb-box--background-image);border:var(--smb-box--border-width) var(--smb-box--border-style) var(--smb-box--border-color);border-radius:var(--smb-box--border-radius);bottom:0;display:block;left:0;opacity:var(--smb-box--background-opacity);position:absolute;right:0;top:0}.smb-box__body{position:relative}.smb-box--has-link{cursor:pointer}.smb-box--has-link:has(:focus-visible){outline:auto;outline:auto -webkit-focus-ring-color}.smb-box__link{display:block!important;height:0!important;position:static!important;text-indent:-99999px!important;width:0!important}:where(.smb-box__body.is-layout-constrained>*){--wp--style--global--content-size:100%;--wp--style--global--wide-size:100%}

</style>
<style id="snow-monkey-blocks-contents-outline-style-inline-css" type="text/css">
.smb-contents-outline{--smb-contents-outline--background-color:var(--wp--preset--color--sm-lightest-gray);--smb-contents-outline--color:inherit;background-color:var(--smb-contents-outline--background-color);color:var(--smb-contents-outline--color);padding:0!important}.smb-contents-outline .wpco{background-color:inherit;color:inherit}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/step/style-index.css?ver=21.0.6" id="snow-monkey-blocks-step-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/tabs/style-index.css?ver=22.2.3" id="snow-monkey-blocks-tabs-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-taxonomy-terms-style-inline-css" type="text/css">
.smb-taxonomy-terms__item>a{align-items:center;display:inline-flex!important}.smb-taxonomy-terms__item__count{display:inline-block;margin-left:.4em;text-decoration:none}.smb-taxonomy-terms__item__count span{align-items:center;display:inline-flex}.smb-taxonomy-terms__item__count span:before{content:"(";font-size:.8em}.smb-taxonomy-terms__item__count span:after{content:")";font-size:.8em}.smb-taxonomy-terms.is-style-tag .smb-taxonomy-terms__list{list-style:none;margin-left:0;padding-left:0}.smb-taxonomy-terms.is-style-tag .smb-taxonomy-terms__item{display:inline-block;margin:4px 4px 4px 0}.smb-taxonomy-terms.is-style-slash .smb-taxonomy-terms__list{display:flex;flex-wrap:wrap;list-style:none;margin-left:0;padding-left:0}.smb-taxonomy-terms.is-style-slash .smb-taxonomy-terms__item{display:inline-block}.smb-taxonomy-terms.is-style-slash .smb-taxonomy-terms__item:not(:last-child):after{content:"/";display:inline-block;margin:0 .5em}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/spider-contents-slider/style-index.css?ver=21.0.6" id="snow-monkey-blocks-spider-contents-slider-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/spider-slider/style-index.css?ver=21.0.6" id="snow-monkey-blocks-spider-slider-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-list-style-inline-css" type="text/css">
.smb-list{--smb-list--gap:var(--_margin-2)}.smb-list ul{list-style:none!important}.smb-list ul *>li:first-child,.smb-list ul li+li{margin-top:var(--smb-list--gap)}.smb-list ul>li{position:relative}.smb-list ul>li .smb-list__icon{left:-1.5em;position:absolute}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/section-with-bgimage/style-index.css?ver=21.0.6" id="snow-monkey-blocks-section-with-bgimage-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/section/style-index.css?ver=21.0.6" id="snow-monkey-blocks-section-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-section-with-bgvideo-style-inline-css" type="text/css">
.smb-section-with-bgvideo>.smb-section-with-bgimage__bgimage>*{display:none!important}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/hero-header/style-index.css?ver=21.0.6" id="snow-monkey-blocks-hero-header-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/panels/style-index.css?ver=23.0.2" id="snow-monkey-blocks-panels-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-thumbnail-gallery-style-inline-css" type="text/css">
.smb-thumbnail-gallery>.smb-thumbnail-gallery__canvas.slick-initialized{margin-bottom:0}.smb-thumbnail-gallery{--smb-thumbnail-gallery--dots-gap:var(--_margin-1)}.smb-thumbnail-gallery__canvas{visibility:hidden}.smb-thumbnail-gallery__canvas.slick-initialized{visibility:visible}.smb-thumbnail-gallery__canvas .slick-dots{display:flex;flex-wrap:wrap;margin:var(--_margin-1) calc(var(--smb-thumbnail-gallery--dots-gap)*-1*.5) calc(var(--smb-thumbnail-gallery--dots-gap)*-1);position:static;width:auto}.smb-thumbnail-gallery__canvas .slick-dots>li{flex:0 0 25%;height:auto;margin:0;margin-bottom:var(---smb-thumbnail-gallery--dots-gap);max-width:25%;padding-left:calc(var(--smb-thumbnail-gallery--dots-gap)*.5);padding-right:calc(var(--smb-thumbnail-gallery--dots-gap)*.5);width:auto}.smb-thumbnail-gallery__item__figure>img{width:100%}.smb-thumbnail-gallery__item__caption{display:flex;flex-direction:row;justify-content:center;margin-top:var(--_margin-1);--_font-size-level:-1;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-thumbnail-gallery__nav{display:none!important}.smb-thumbnail-gallery .slick-next{right:10px;z-index:1}.smb-thumbnail-gallery .slick-prev{left:10px;z-index:1}.smb-thumbnail-gallery .slick-dots{bottom:0;line-height:1;position:static}

</style>
<style id="snow-monkey-blocks-testimonial-style-inline-css" type="text/css">
.smb-testimonial__item{display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:center}.smb-testimonial__item__figure{border-radius:100%;flex:0 0 auto;height:48px;margin-right:var(--_margin-1);margin-top:calc(var(--_half-leading)*1rem);overflow:hidden;width:48px}.smb-testimonial__item__figure img{height:100%;object-fit:cover;object-position:50% 50%;width:100%}.smb-testimonial__item__body{flex:1 1 auto;max-width:100%}.smb-testimonial__item__name{--_font-size-level:-2;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-testimonial__item__name a{color:inherit}.smb-testimonial__item__lede{--_font-size-level:-2;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-testimonial__item__lede a{color:inherit}.smb-testimonial__item__content{margin-bottom:var(--_margin-1);--_font-size-level:-1;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-testimonial__item__content:after{background-color:currentColor;content:"";display:block;height:1px;margin-top:var(--_margin-1);width:2rem}

</style>
<style id="snow-monkey-blocks-evaluation-star-style-inline-css" type="text/css">
.smb-evaluation-star{--smb-evaluation-star--gap:var(--_margin-2);--smb-evaluation-star--icon-color:#f9bb2d;--smb-evaluation-star--numeric-color:currentColor;align-items:center;display:flex;gap:var(--smb-evaluation-star--gap)}.smb-evaluation-star--title-right .smb-evaluation-star__title{order:1}.smb-evaluation-star__body{display:inline-flex;gap:var(--smb-evaluation-star--gap)}.smb-evaluation-star__numeric{color:var(--smb-evaluation-star--numeric-color);font-weight:700}.smb-evaluation-star__numeric--right{order:1}.smb-evaluation-star__icon{color:var(--smb-evaluation-star--icon-color);display:inline-block}.smb-evaluation-star .svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/read-more-box/style-index.css?ver=21.0.6" id="snow-monkey-blocks-read-more-box-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-section-side-heading-style-inline-css" type="text/css">
.smb-section-side-heading__header,.smb-section-side-heading__subtitle,.smb-section-side-heading__title{text-align:left}.smb-section-side-heading__header>*{text-align:inherit}.smb-section-side-heading__lede-wrapper{justify-content:flex-start}.smb-section-side-heading>.smb-section__inner>.c-container>.smb-section__contents-wrapper>.c-row{justify-content:space-between}@media not all and (min-width:640px){.smb-section-side-heading :where(.smb-section__contents-wrapper>.c-row>*+*){margin-top:var(--_margin2)}}

</style>
<style id="snow-monkey-blocks-countdown-style-inline-css" type="text/css">
.smb-countdown{--smb-countdown--gap:var(--_margin-2);--smb-countdown--numeric-color:currentColor;--smb-countdown--clock-color:currentColor}.smb-countdown__list{align-items:center;align-self:center;display:flex;flex-direction:row;flex-wrap:wrap;list-style-type:none;margin-left:0;padding-left:0}.smb-countdown__list-item{margin:0 var(--smb-countdown--gap);text-align:center}.smb-countdown__list-item:first-child{margin-left:0}.smb-countdown__list-item:last-child{margin-right:0}.smb-countdown__list-item__numeric{color:var(--smb-countdown--numeric-color);display:block;--_font-size-level:3;font-size:var(--_fluid-font-size);font-weight:700;line-height:var(--_line-height)}.smb-countdown__list-item__clock{color:var(--smb-countdown--clock-color);display:block;--_font-size-level:-1;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-countdown .align-center{justify-content:center}.smb-countdown .align-left{justify-content:flex-start}.smb-countdown .align-right{justify-content:flex-end}.is-style-inline .smb-countdown__list-item__clock,.is-style-inline .smb-countdown__list-item__numeric{display:inline}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/balloon/style-index.css?ver=21.0.6" id="snow-monkey-blocks-balloon-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-price-menu-style-inline-css" type="text/css">
.smb-price-menu{--smb-price-menu--border-color:var(--_lighter-color-gray);--smb-price-menu--item-padding:var(--_padding-1);border-top:1px solid var(--smb-price-menu--border-color)}.smb-price-menu>.smb-price-menu__item{margin-bottom:0;margin-top:0}.smb-price-menu__item{border-bottom:1px solid var(--smb-price-menu--border-color);padding:var(--smb-price-menu--item-padding) 0}@media(min-width:640px){.smb-price-menu__item{align-items:center;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}.smb-price-menu__item>*{flex:0 0 auto}}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/information/style-index.css?ver=21.0.6" id="snow-monkey-blocks-information-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/items/style-index.css?ver=23.0.1" id="snow-monkey-blocks-items-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/btn/style-index.css?ver=23.0.0" id="snow-monkey-blocks-btn-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/spider-pickup-slider/style-index.css?ver=21.0.6" id="snow-monkey-blocks-spider-pickup-slider-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-slider-style-inline-css" type="text/css">
.smb-slider>.smb-slider__canvas.slick-initialized{margin-bottom:0;padding-bottom:0}.smb-slider--16to9 .smb-slider__item__figure,.smb-slider--4to3 .smb-slider__item__figure{position:relative}.smb-slider--16to9 .smb-slider__item__figure:before,.smb-slider--4to3 .smb-slider__item__figure:before{content:"";display:block}.smb-slider--16to9 .smb-slider__item__figure>img,.smb-slider--4to3 .smb-slider__item__figure>img{bottom:0;height:100%;left:0;object-fit:cover;object-position:50% 50%;position:absolute;right:0;top:0}.smb-slider--16to9 .smb-slider__item__figure:before{padding-top:56.25%}.smb-slider--4to3 .smb-slider__item__figure:before{padding-top:75%}.smb-slider__canvas{padding-bottom:35px;visibility:hidden}.smb-slider__canvas.slick-slider{margin-bottom:0}.smb-slider__canvas.slick-initialized{visibility:visible}.smb-slider__canvas.slick-initialized .slick-slide{align-items:center;display:flex;flex-direction:column;justify-content:center}.smb-slider__canvas.slick-initialized .slick-slide>*{flex:1 1 auto}.smb-slider__canvas[dir=rtl] .slick-next{left:10px;right:auto}.smb-slider__canvas[dir=rtl] .slick-prev{left:auto;right:10px}.smb-slider__canvas[dir=rtl] .smb-slider__item__caption{direction:ltr}.smb-slider__item__figure,.smb-slider__item__figure>img{width:100%}.smb-slider__item__caption{display:flex;flex-direction:row;justify-content:center;margin-top:var(--_margin-1);--_font-size-level:-1;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-slider .slick-next{right:10px;z-index:1}.smb-slider .slick-prev{left:10px;z-index:1}.smb-slider .slick-dots{bottom:0;line-height:1;position:static}

</style>
<style id="snow-monkey-blocks-btn-box-style-inline-css" type="text/css">
.smb-btn-box{--smb-btn-box--background-color:#0000;--smb-btn-box--padding:var(--_padding2);--smb-btn--style--ghost--border-color:var(--smb-btn--background-color,currentColor);--smb-btn--style--ghost--color:currentColor;background-color:var(--smb-btn-box--background-color);padding-bottom:var(--smb-btn-box--padding);padding-top:var(--smb-btn-box--padding)}.smb-btn-box__lede{font-weight:700;margin-bottom:var(--_margin-1);text-align:center}.smb-btn-box__btn-wrapper{text-align:center}.smb-btn-box__note{margin-top:var(--_margin-1);text-align:center}.smb-btn-box.is-style-ghost .smb-btn{--smb-btn--color:var(--smb-btn--style--ghost--color);background-color:#0000;border:1px solid var(--smb-btn--style--ghost--border-color)}

</style>
<style id="snow-monkey-blocks-buttons-style-inline-css" type="text/css">
.smb-buttons{--smb-buttons--gap:var(--_margin1);display:flex;flex-wrap:wrap;gap:var(--smb-buttons--gap)}.smb-buttons.has-text-align-left,.smb-buttons.is-content-justification-left{justify-content:flex-start}.smb-buttons.has-text-align-center,.smb-buttons.is-content-justification-center{justify-content:center}.smb-buttons.has-text-align-right,.smb-buttons.is-content-justification-right{justify-content:flex-end}.smb-buttons.is-content-justification-space-between{justify-content:space-between}.smb-buttons>.smb-btn-wrapper{flex:0 1 auto;margin:0}.smb-buttons>.smb-btn-wrapper--full{flex:1 1 auto}@media not all and (min-width:640px){.smb-buttons>.smb-btn-wrapper--more-wider{flex:1 1 auto}}

</style>
<style id="snow-monkey-blocks-container-style-inline-css" type="text/css">
.smb-container__body{margin-left:auto;margin-right:auto;max-width:100%}.smb-container--no-gutters{padding-left:0!important;padding-right:0!important}:where(.smb-container__body.is-layout-constrained>*){--wp--style--global--content-size:100%;--wp--style--global--wide-size:100%}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/pricing-table/style-index.css?ver=21.0.6" id="snow-monkey-blocks-pricing-table-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-directory-structure-style-inline-css" type="text/css">
.smb-directory-structure{--smb-directory-structure--background-color:var(--_lightest-color-gray);--smb-directory-structure--padding:var(--_padding1);--smb-directory-structure--gap:var(--_margin-2);--smb-directory-structure--icon-color:inherit;background-color:var(--smb-directory-structure--background-color);overflow-x:auto;overflow-y:hidden;padding:var(--smb-directory-structure--padding)}.smb-directory-structure>*+*{margin-top:var(--smb-directory-structure--gap)}.smb-directory-structure__item p{align-items:flex-start;display:flex;flex-wrap:nowrap}.smb-directory-structure .fa-fw{color:var(--smb-directory-structure--icon-color);width:auto}.smb-directory-structure .svg-inline--fa{display:var(--fa-display,inline-block);height:1em;overflow:visible;vertical-align:-.125em}.smb-directory-structure__item__name{margin-left:.5em;white-space:nowrap}.smb-directory-structure__item__list{margin-left:1.5em}.smb-directory-structure__item__list>*{margin-top:var(--smb-directory-structure--gap)}

</style>
<style id="snow-monkey-blocks-faq-style-inline-css" type="text/css">
.smb-faq{--smb-faq--border-color:var(--_lighter-color-gray);--smb-faq--label-color:initial;--smb-faq--item-gap:var(--_margin1);--smb-faq--item-question-label-color:currentColor;--smb-faq--item-answer-label-color:currentColor;border-top:1px solid var(--smb-faq--border-color)}.smb-faq__item{border-bottom:1px solid var(--smb-faq--border-color);padding:var(--_padding1) 0}.smb-faq__item__answer,.smb-faq__item__question{display:flex;flex-direction:row;flex-wrap:nowrap}.smb-faq__item__answer__label,.smb-faq__item__question__label{flex:0 0 0%;margin-right:var(--smb-faq--item-gap);margin-top:calc(var(--_half-leading)*-1em - .125em);--_font-size-level:3;font-size:var(--_fluid-font-size);font-weight:400;line-height:var(--_line-height);min-width:.8em}.smb-faq__item__answer__body,.smb-faq__item__question__body{flex:1 1 auto;margin-top:calc(var(--_half-leading)*-1em)}.smb-faq__item__question{font-weight:700;margin:0 0 var(--smb-faq--item-gap)}.smb-faq__item__question__label{color:var(--smb-faq--item-question-label-color)}.smb-faq__item__answer__label{color:var(--smb-faq--item-answer-label-color)}:where(.smb-faq__item__answer__body.is-layout-constrained>*){--wp--style--global--content-size:100%;--wp--style--global--wide-size:100%}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/section-break-the-grid/style-index.css?ver=21.0.6" id="snow-monkey-blocks-section-break-the-grid-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/categories-list/style-index.css?ver=21.0.6" id="snow-monkey-blocks-categories-list-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/blocks/media-text/style-index.css?ver=21.0.6" id="snow-monkey-blocks-media-text-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-blocks-rating-box-style-inline-css" type="text/css">
.smb-rating-box{--smb-rating-box--gap:var(--_margin-1);--smb-rating-box--bar-border-radius:var(--_global--border-radius);--smb-rating-box--bar-background-color:var(--_lighter-color-gray);--smb-rating-box--bar-height:1rem;--smb-rating-box--rating-background-color:#f9bb2d}.smb-rating-box>.smb-rating-box__item__title{margin-bottom:var(--_margin-2)}.smb-rating-box__body>*+*{margin-top:var(--smb-rating-box--gap)}.smb-rating-box__item{display:grid;gap:var(--_margin-2)}.smb-rating-box__item__body{align-items:end;display:grid;gap:var(--_margin1);grid-template-columns:1fr auto}.smb-rating-box__item__numeric{--_font-size-level:-2;font-size:var(--_font-size);line-height:var(--_line-height)}.smb-rating-box__item__evaluation{grid-column:1/-1}.smb-rating-box__item__evaluation__bar,.smb-rating-box__item__evaluation__rating{border-radius:var(--smb-rating-box--bar-border-radius);height:var(--smb-rating-box--bar-height)}.smb-rating-box__item__evaluation__bar{background-color:var(--smb-rating-box--bar-background-color);position:relative}.smb-rating-box__item__evaluation__rating{background-color:var(--smb-rating-box--rating-background-color);left:0;position:absolute;top:0}.smb-rating-box__item__evaluation__numeric{bottom:calc(var(--smb-rating-box--bar-height) + var(--_s-2));position:absolute;right:0;--_font-size-level:-2;font-size:var(--_font-size);line-height:var(--_line-height)}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/blocks/checkboxes/style-index.css?ver=7.1.0" id="snow-monkey-forms-control-checkboxes-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/blocks/file/style-index.css?ver=9.0.0" id="snow-monkey-forms-control-file-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-forms-item-style-inline-css" type="text/css">
.smf-item label{cursor:pointer}.smf-item__description{color:var(--_dark-color-gray);margin-top:var(--_margin-2);--_font-size-level:-1;font-size:var(--_font-size);line-height:var(--_line-height)}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/blocks/radio-buttons/style-index.css?ver=7.1.0" id="snow-monkey-forms-control-radio-buttons-style-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/blocks/select/style-index.css?ver=7.1.0" id="snow-monkey-forms-control-select-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="snow-monkey-forms-control-text-style-inline-css" type="text/css">
.smf-form .smf-text-control__control{background-color:var(--_color-white);box-shadow:inset 0 1px 1px rgba(0,0,0,.035);max-width:100%;padding:.75rem 1rem;--_border-radius:var(--_global--border-radius);--_border-color:var(--_form-control-border-color);border:1px solid var(--_border-color);border-radius:var(--_border-radius);outline:0;--_transition-duration:var(--_global--transition-duration);--_transition-function-timing:var(--_global--transition-function-timing);--_transition-delay:var(--_global--transition-delay);transition:border var(--_transition-duration) var(--_transition-function-timing) var(--_transition-delay)}.smf-form .smf-text-control__control:hover{--_border-color:var(--_form-control-border-color-hover)}.smf-form .smf-text-control__control:active,.smf-form .smf-text-control__control:focus,.smf-form .smf-text-control__control:focus-within,.smf-form .smf-text-control__control[aria-selected=true]{--_border-color:var(--_form-control-border-color-focus)}.smf-form .smf-text-control__control>input,.smf-form .smf-text-control__control>textarea{border:none;outline:none}.smf-form .smf-text-control__control:disabled{background-color:var(--_lightest-color-gray)}textarea.smf-form .smf-text-control__control{height:auto;width:100%}.smf-text-control{line-height:1}

</style>
<style id="snow-monkey-forms-control-textarea-style-inline-css" type="text/css">
.smf-form .smf-textarea-control__control{background-color:var(--_color-white);box-shadow:inset 0 1px 1px rgba(0,0,0,.035);display:block;max-width:100%;padding:.75rem 1rem;width:100%;--_border-radius:var(--_global--border-radius);--_border-color:var(--_form-control-border-color);border:1px solid var(--_border-color);border-radius:var(--_border-radius);outline:0;--_transition-duration:var(--_global--transition-duration);--_transition-function-timing:var(--_global--transition-function-timing);--_transition-delay:var(--_global--transition-delay);transition:border var(--_transition-duration) var(--_transition-function-timing) var(--_transition-delay)}.smf-form .smf-textarea-control__control:hover{--_border-color:var(--_form-control-border-color-hover)}.smf-form .smf-textarea-control__control:active,.smf-form .smf-textarea-control__control:focus,.smf-form .smf-textarea-control__control:focus-within,.smf-form .smf-textarea-control__control[aria-selected=true]{--_border-color:var(--_form-control-border-color-focus)}.smf-form .smf-textarea-control__control>input,.smf-form .smf-textarea-control__control>textarea{border:none;outline:none}.smf-form .smf-textarea-control__control:disabled{background-color:var(--_lightest-color-gray)}textarea.smf-form .smf-textarea-control__control{height:auto;width:100%}

</style>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/packages/slick/slick.css?ver=1750061650" id="slick-carousel-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/packages/slick/slick-theme.css?ver=1750061650" id="slick-carousel-theme-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/packages/spider/dist/css/spider.css?ver=1750061650" id="spider-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/css/blocks.css?ver=1750061650" id="snow-monkey-blocks-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/css/fallback.css?ver=1750061650" id="sass-basis-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/css/nopro.css?ver=1750061650" id="snow-monkey-blocks/nopro-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/css/app.css?ver=1750061650" id="snow-monkey-forms-css" media="all" rel="stylesheet" type="text/css"/>
<style id="global-styles-inline-css" type="text/css">
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<script async="" src="https://www.googletagmanager.com/gtag/js?id=GT-WRDFDNHF&amp;cx=c&amp;gtm=45je5811v9129979600za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105087538~105087540~105103161~105103163" type="text/javascript"></script><script async="" charset="utf-8" crossorigin="anonymous" integrity="sha384-1i5SHiC9TarrFdackClm6FALE67s55dskz+cLvqzzcv4HwgW7OQmj6URzNfVom0/" src="https://www.gstatic.com/recaptcha/releases/DBIsSQ0s2djD_akThoRUDeHa/recaptcha__zh_cn.js" type="text/javascript"></script><script id="jquery-core-js" src="https://hirumori.co.jp/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" type="text/javascript"></script>
<script id="jquery-migrate-js" src="https://hirumori.co.jp/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" type="text/javascript"></script>
<script data-wp-strategy="defer" defer="defer" id="fontawesome6-js" src="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/js/fontawesome-all.js?ver=1750061650" type="text/javascript"></script>
<script data-wp-strategy="defer" defer="defer" id="spider-js" src="https://hirumori.co.jp/wp-content/plugins/snow-monkey-blocks/dist/packages/spider/dist/js/spider.js?ver=1750061650" type="text/javascript"></script>
<!-- Site Kit によって追加された Google タグ（gtag.js）スニペット -->
<!-- Google アナリティクス スニペット (Site Kit が追加) -->
<script async="" id="google_gtagjs-js" src="https://www.googletagmanager.com/gtag/js?id=GT-WRDFDNHF" type="text/javascript"></script>
<script id="google_gtagjs-js-after" type="text/javascript">
/* <![CDATA[ */
window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}
gtag("set","linker",{"domains":["hirumori.co.jp"]});
gtag("js", new Date());
gtag("set", "developer_id.dZTNiMT", true);
gtag("config", "GT-WRDFDNHF");
 window._googlesitekit = window._googlesitekit || {}; window._googlesitekit.throttledEvents = []; window._googlesitekit.gtagEvent = (name, data) => { var key = JSON.stringify( { name, data } ); if ( !! window._googlesitekit.throttledEvents[ key ] ) { return; } window._googlesitekit.throttledEvents[ key ] = true; setTimeout( () => { delete window._googlesitekit.throttledEvents[ key ]; }, 5 ); gtag( "event", name, { ...data, event_source: "site-kit" } ); } 
/* ]]> */
</script>
<!-- Site Kit によって追加された終了 Google タグ（gtag.js）スニペット -->
<link href="https://hirumori.co.jp/wp-json/" rel="https://api.w.org/"/><link href="https://hirumori.co.jp/wp-json/wp/v2/pages/50" rel="alternate" title="JSON" type="application/json"/><link href="https://hirumori.co.jp/" rel="canonical"/>
<link href="https://hirumori.co.jp/" rel="shortlink"/>
<link href="https://hirumori.co.jp/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fhirumori.co.jp%2F" rel="alternate" title="oEmbed (JSON)" type="application/json+oembed"/>
<link href="https://hirumori.co.jp/wp-json/oembed/1.0/embed?url=https%3A%2F%2Fhirumori.co.jp%2F&amp;format=xml" rel="alternate" title="oEmbed (XML)" type="text/xml+oembed"/>
<meta content="Site Kit by Google 1.152.1" name="generator"/>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/css/sns-botton.css?ver=2.11.3" rel="stylesheet"/>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/css/responsive.css?ver=2.11.3" media="screen and (max-width:1201px)" rel="stylesheet"/>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/css/footer-bar.css?ver=2.11.3" media="screen and (max-width:1201px)" rel="stylesheet"/>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/jquery.easing.1.4.js?ver=2.11.3"></script>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/jscript.js?ver=2.11.3"></script>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/tcd_cookie.js?ver=2.11.3"></script>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/comment.js?ver=2.11.3"></script>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/parallax.js?ver=2.11.3"></script>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/simplebar.css?ver=2.11.3" rel="stylesheet"/>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/simplebar.min.js?ver=2.11.3"></script>
<script src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/header_fix.js?ver=2.11.3"></script>
<style type="text/css">
body { font-size:16px; }
.common_headline { font-size:36px !important; }
@media screen and (max-width:750px) {
  body { font-size:14px; }
  .common_headline { font-size:20px !important; }
}
body, input, textarea { font-family: Arial, "Hiragino Sans", "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "游ゴシック", YuGothic, "メイリオ", Meiryo, sans-serif; }

.rich_font, .p-vertical { font-family: Arial, "Hiragino Sans", "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "游ゴシック", YuGothic, "メイリオ", Meiryo, sans-serif; font-weight:600; }

.rich_font_type1 { font-family: Arial, "ヒラギノ角ゴ ProN W3", "Hiragino Kaku Gothic ProN", "メイリオ", Meiryo, sans-serif; font-weight:600; }
.rich_font_type2 { font-family: Arial, "Hiragino Sans", "ヒラギノ角ゴ ProN", "Hiragino Kaku Gothic ProN", "游ゴシック", YuGothic, "メイリオ", Meiryo, sans-serif; font-weight:600; }
.rich_font_type3 { font-family: "Times New Roman" , "游明朝" , "Yu Mincho" , "游明朝体" , "YuMincho" , "ヒラギノ明朝 Pro W3" , "Hiragino Mincho Pro" , "HiraMinProN-W3" , "HGS明朝E" , "ＭＳ Ｐ明朝" , "MS PMincho" , serif; font-weight:600; }

.square_headline .headline { font-size:24px; }
.square_headline .sub_headline { font-size:14px; }
@media screen and (max-width:750px) {
  .square_headline .headline { font-size:18px; }
  .square_headline .sub_headline { font-size:12px; }
}

.pc body.use_header_fix #header:after { background:rgba(255,255,255,1); }
.pc body.use_header_fix #header:hover:after { background:rgba(255,255,255,1); }
#header_logo a { background:rgba(255,255,255,1); }
#header_logo .logo_text { color:#ffffff !important; font-size:32px; }
#footer_logo .logo_text { font-size:32px; }
@media screen and (max-width:1201px) {
  #header_logo .logo_text { font-size:24px; }
  #footer_logo .logo_text { font-size:24px; }
}
#global_menu > ul > li > a:hover, body.single #global_menu > ul > li.current-menu-item > a, body.single #global_menu > ul > li.active > a,
  #header.active #global_menu > ul > li.current-menu-item > a, #header.active #global_menu > ul > li.active > a, body.hide_header_image #global_menu > ul > li.current-menu-item > a, body.no_index_header_content #global_menu > ul > li.current-menu-item > a
    { color:#00729f !important; }
#global_menu ul ul a { color:#ffffff; background:#00729f; }
#global_menu ul ul a:hover { color:#ffffff; background:#00466d; }
#global_menu ul ul li.menu-item-has-children > a:before { color:#ffffff; }
.mobile #header:after { background:rgba(255,255,255,1); }
.mobile #header:hover:after { background:rgba(255,255,255,1); }
.mobile body.home.no_index_header_content #header { background:rgba(255,255,255,1); }
#drawer_menu { color:#ffffff; background:#000000; }
#drawer_menu a { color:#ffffff; }
#drawer_menu a:hover { color:#00466d; }
#mobile_menu a { color:#ffffff; border-color:#444444; }
#mobile_menu li li a { background:#333333; }
#mobile_menu a:hover, #drawer_menu .close_button:hover, #mobile_menu .child_menu_button:hover { color:#ffffff; background:#444444; }
#mobile_menu .child_menu_button .icon:before, #mobile_menu .child_menu_button:hover .icon:before { color:#ffffff; }
.megamenu .headline_area div.head .headline { font-size:24px; }
.megamenu_b .service_item_list .item a:after { background:rgba(0,0,0,0.5); }
#footer_banner a { color:#ffffff !important; }
#footer_banner .title { font-size:24px; }
#footer_banner .sub_title { font-size:14px; }
@media screen and (max-width:750px) {
  #footer_banner .title { font-size:18px; }
  #footer_banner .sub_title { font-size:12px; }
}
.author_profile .avatar_area img, .animate_image img, .animate_background .image {
  width:100%; height:auto;
  -webkit-transition: transform  0.5s ease;
  transition: transform  0.5s ease;
}
.author_profile a.avatar:hover img, .animate_image:hover img, .animate_background:hover .image {
  -webkit-transform: scale(1.2);
  transform: scale(1.2);
}


a { color:#000; }

#footer_menu .footer_menu li:first-of-type a, #return_top2 a:hover:before, #bread_crumb, #bread_crumb li.last, .tcd_banner_widget .headline, .post_slider_widget .slick-arrow:hover:before,
  #post_title2 .title, #service_banner .main_title, .megamenu .headline_area div.head .headline, .news_category_list li.active a, .news_category_sort_button li.active a, .sns_button_list.color_type2 li.contact a:before,
    .tab_content_top .tab .item.active, .cb_tab_content .tab_content_bottom .title, .cb_headline .headline, .faq_list .question.active, .faq_list .question:hover, .author_profile .author_link li.contact a:before, .author_profile .author_link li.user_url a:before
      { color:#00729f; }

.square_headline, .page_navi span.current, #post_pagination p, #comment_tab li.active a, .news_category_list li.active a:before, .news_category_sort_button li.active a:before,
  .tab_content_bottom .slick-dots button:hover::before, .tab_content_bottom .slick-dots .slick-active button::before,
    .faq_list .question.active:before, .faq_list .question:hover:before, .faq_list .question:hover:after, #return_top a, .styled_post_list1_widget .widget_headline
      { background:#00729f; }

.page_navi span.current, #post_pagination p, .design_headline2 span.title, #post_title2 .title, .project_list .category, #project_title_area .category
  { border-color:#00729f; }

.category_list_widget li.current-menu-item a, .category_list_widget li.current-menu-parent a { background:rgba(0,114,159,0.5); border-color:#fff; color:#fff; }

a:hover, #footer_top a:hover, #footer_social_link li a:hover:before, #footer_menu .footer_menu li:first-of-type a:hover, #next_prev_post a:hover, #bread_crumb li a:hover, #bread_crumb li.home a:hover:before,
  .single_copy_title_url_btn:hover, .tcdw_search_box_widget .search_area .search_button:hover:before, .widget_tab_post_list_button div:hover,
    #single_author_title_area .author_link li a:hover:before, .author_profile a:hover, #post_meta_bottom a:hover, .cardlink_title a:hover,
      .comment a:hover, .comment_form_wrapper a:hover, #searchform .submit_button:hover:before, .p-dropdown__title:hover:after
        { color:#00466d; }

.page_navi a:hover, #post_pagination a:hover, #p_readmore .button:hover, .c-pw__btn:hover, #comment_tab li a:hover, #submit_comment:hover, #cancel_comment_reply a:hover,
  #wp-calendar #prev a:hover, #wp-calendar #next a:hover, #wp-calendar td a:hover, #comment_tab li a:hover, #return_top a:hover
    { background-color:#00466d; }

.page_navi a:hover, #post_pagination a:hover, #comment_textarea textarea:focus, .c-pw__box-input:focus
  { border-color:#00466d; }

.post_content a, .custom-html-widget a { color:#000000; }
.post_content a:hover, .custom-html-widget a:hover { color:#00466d; }

.design_button.type1 a { color:#ffffff !important; background:#00729f; }
.design_button.type1 a:hover { color:#ffffff !important; background:#00466d; }
.cat_id1 { background-color:#00729f; }
.cat_id1:hover { background-color:#00466d; }
.cat_id15 { background-color:#00729f; }
.cat_id15:hover { background-color:#00466d; }
:root {
    /* 色指定 */
    --yellow: #ffed00;
    --blue: #84cabf;
    /* フォント指定 */
    --ttl-font: "Zen Kaku Gothic New", sans-serif;
    --txt-font: "Zen Kaku Gothic New", sans-serif;
    --en-font: "Roboto", sans-serif;
    /* 余白 */
    --content-inner-5: min(5vw,50px);
    --content-inner-3: min(3vw,30px);
}

body {
    overflow: hidden;
}

/* 全体の文字設定 */
.post_content :is(h2,h3,h4,h5,p,a,td,li) {
    font-family: var(--txt-font);
    color: #222;
    letter-spacing: .1em;
}

/* width shape 100% */
.wide {
    margin-right: calc(50% - 50vw);
    margin-left: calc(50% - 50vw);
    background: #f0f5f5;
}
.wide > .wp-block-group__inner-container {
    max-width: 1200px;
    width: 100%;
    margin: auto;
    padding: 100px 0;
}

/* セクション下の余白 */
.con-box {
    margin-bottom: 100px;
}

/* header */
 #header {
    position: relative;
 }
/* header-logo */
#header_logo img {
    height: 80%;
    width: auto;
}
/* header-menu */
.pc body.use_header_fix #header:after {
    top: 0;
}
#global_menu > ul {
    text-align: right;
}
#global_menu > ul > li > a {
    font-family: var(--txt-font);
    color: #222;
    border-bottom: 0px solid var(--blue);
    transition: .3s;
}
#global_menu > ul > li > a:hover,
#header.active #global_menu > ul > li.current-menu-item > a,
#global_menu > ul > li.current-menu-item > a,
#header.active #global_menu > ul > li.active > a,
body.single #global_menu > ul > li.current-menu-item > a,
body.single #global_menu > ul > li.active > a {
    border-width: 6px;
    color: var(--blue)!important;
}
#menu-item-132 a,
#global_menu > ul > li#menu-item-531 > a {
    z-index: 0;
    background: var(--blue);
    border-bottom: none!important;
}
#menu-item-132 a::before,
#global_menu > ul > li#menu-item-531 > a::before {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    background: var(--yellow);
    left: 0;
    z-index: -1;
    transform: translateY(100%);
    bottom: 6px;
    transition: .5s;
}
#global_menu > ul > #menu-item-132 > a:hover,
#header.active #global_menu > ul > #menu-item-132.active > a,
#global_menu > ul > li#menu-item-531 > a:hover,
#header.active #global_menu > ul > li#menu-item-531.active > a {
    color: #222!important;
}
#menu-item-132 a:hover::before,
#global_menu > ul > li#menu-item-531 > a:hover::before {
    bottom: 100%;
}
#global_menu ul ul a {
    background: var(--blue);
    font-family: var(--ttl-font);
    color: #222;
    font-weight: 600;
    letter-spacing: .1em;
}
#global_menu ul ul a:hover {
    background: var(--yellow);
    color: #222;
}
/* mobile */
/* ハンバーガー */
#global_menu_button span {
    background: #222;
}
#mobile_menu a {
    background: #fff;
    color: #222;
    font-family: var(--txt-font);
    font-size: 16px;
    letter-spacing: .1em;
}
#drawer_menu {
    background: #fff;
}
#mobile_menu .child_menu_button,
#mobile_menu a:hover,
#drawer_menu .close_button:hover,
#mobile_menu .child_menu_button:hover {
    background: var(--blue);
}
#mobile_menu li li a {
    background: var(--blue);
}
#mobile_menu a {
    border-color: #ccc;
}
#mobile_menu .menu-item-132 a,
#mobile_menu .menu-item-531 a {
    background: var(--blue);
    text-align: center;
    font-size: 18px;
    margin-top: 5px;
    width: 90%;
    margin-inline: auto;
    border-radius: 5px;
    padding: 0;
    transition: .3s;
    border: none;
}
#mobile_menu .menu-item-132 a:hover,
#mobile_menu .menu-item-531 a:hover {
    background: var(--yellow);
    color: #222;
}
#mobile_menu .menu-item-68 a {
    display: none;
}
/* page-header */
#page_header {
    background: none;
}
#page_header_inner {
    background: #fff;
    padding: 50px 0;
    max-width: 500px;
    width: 100%!important;
    transform: unset;
    top: auto;
    left: 0;
    bottom: 0;
    border-radius: 0 15px 0 0;
}
#page_header .catch {
    font-family: var(--ttl-font);
    color: #222;
    letter-spacing: 0.2em;
    font-size: 50px!important;
    font-size: clamp(25px,5vw,50px)!important;
    line-height: 1.0;
}
#page_header .catch span {
    font-family: var(--en-font);
    font-weight: 500;
    font-size: 30px;
    font-size: clamp(18px,3vw,30px);
    color: #999;
}
#page_header .bg_image {
    background-position-y: center!important;
    width: 90%;
    left: auto;
    right: 0;
}

/* h2title */
.h2title {
    margin-bottom: 50px;
}
.post_content .h2title h2 {
    font-size: clamp(30px,5vw,50px);
}
.post_content .h2title p {
    position: relative;
    font-family: var(--en-font);
    letter-spacing: .15em;
    color: #999;
    line-height: 1.5;
}
.post_content .h2title p::before,
.post_content .h2title p::after {
    position: absolute;
    content: '';
    width: 300px;
    height: 7px;
    background: var(--blue);
    left: 0;
    bottom: -20px;
}
.post_content .h2title p::after {
    background: var(--yellow);
    width: 30px;
    border-right: 5px solid #fff;
}

/* h3title */
.post_content .h3title {
    border-left: 5px solid var(--blue);
    padding-left: .5em;
}

/* col-content */
.col-content {
    position: relative;
    min-height: 600px;
    /* width: 100dvw; */
    margin-inline: calc(50% - 50vw);
    margin-bottom: 50px;
    gap: 5dvw;
}
.col-content figure {
    width: 50dvw;
    height: 100%;
    overflow: hidden;
}
.post_content .col-content figure img {
    object-fit: cover;
    width: 100%;
    height: 100%;
}
.col-content .txt {
    max-width: 600px;
    width: 100%;
}
/* right */
.col-content.right .txt {
    margin-left: auto;
}
/* col-content h2title */
.post_content .pool-wrap .h2title h2 span {
    font-size: clamp(25px,3vw,30px);
}
.post_content .pool-wrap .h2title h2 {
    line-height: 1.2;
}

/* original-btn */
.post_content .original-btn a {
    background: var(--blue);
    padding: 1em 2em;
    margin-top: 50px;
    transition: .3s;
}
.post_content .original-btn a:hover {
    background: var(--yellow);
}

/* slider */
.smb-spider-slider {
    overflow: unset;
}
.spider {
    margin-inline: calc(50% - 50vw);
}
.spider__canvas {
    height: 300px;
}
.smb-spider-slider__figure-wrapper {
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

/* link-banner */
.post_content .link-banner p {
    position: relative;
    border-bottom: 1px solid;
    color: #fff;
}
.post_content .link-banner p::before {
    position: absolute;
    content: '';
    width: 50px;
    height: 50px;
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/star01.png)no-repeat;
    background-size: contain;
    z-index: 50;
    left: 0;
    bottom: 0;
    transform: translate(-50%,50%);
    transition: all .5s ease;
}
.post_content .link-banner:hover p::before {
    left: 100%;
}
.post_content a.link-banner {
    margin-top: 50px;
    display: block;
}

/* intro */
.intro {
    position: relative;
    min-height: 70dvh;
}
.intro::before {
    position: absolute;
    content: '';
    width: clamp(250px,50vw,500px);
    height: clamp(125px,25vw,250px);
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/hoshi-bg02.png)no-repeat;
    background-size: contain;
    z-index: 50;
    transform: rotate(180deg);
    bottom: 0;
    right: 0;
}
.intro .txt-wrap {
    max-width: 1200px;
    width: 100%;
    background: rgb(255,255,255);
    padding: var(--content-inner-5);
    margin-inline: auto;
    border-radius: 10px;
    box-shadow: 0 0 10px #fff;
}
/* ttl */
.post_content .intro .ttl {
    position: relative;
    font-size: clamp(20px,3.6vw,36px)!important;
}
.post_content .intro .ttl::before {
    position: absolute;
    content: '';
    width: clamp(50px,7vw,70px);
    height: clamp(50px,7vw,70px);
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/logo-mark.png)no-repeat;
    background-size: contain;
    left: 0;
    right: 0;
    bottom: 0;
    margin-inline: auto;
    transform: translateY(75%);
}
/* txt */
.post_content .intro .txt {
    text-align: center;
}

/* main */
.post_content .main {
    border: 2px solid var(--blue);
    border-radius: 10px;
    padding: var(--content-inner-3);
    margin-bottom: 0;
    line-height: 1.5;
}
.post_content .main strong {
    font-size: clamp(18px,2vw,20px);
    font-weight: 500;
}

/* table */
body .post_content td {
    border: none;
    border-bottom: 1px solid #ddd;
    vertical-align: middle;
}
.post_content td:first-child {
    border-bottom: 3px solid var(--blue);
    font-weight: bold;
    width: 10em;
}

/* 新着情報 */
/* archive */
#blog_list .category a {
    background: var(--blue);
    transition: .3s;
}
#blog_list .category a:hover {
    opacity: .7;
}
#blog_archive,
#blog_list {
    border: none;
}
#blog_list .title span,
#blog_list .desc span {
    font-family: var(--txt-font);
    letter-spacing: .1em;
    color: #222;
}
.cat_id1 {
    background: var(--blue)!important;
}
.page_navi span.current {
    background: var(--blue);
    border-color: var(--blue);
}

/* footer */
#footer_menu ul {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 30px;
}
.post_content a.banner p {
    font-family: var(--ttl-font);
    color: #fff;
    border-bottom: 2px solid;
    display: inline-block;
    font-size: 50px!important;
    padding-inline: 1em;
}
#footer_menu_bottom li a {
    /* pointer-events: none; */
    font-family: var(--txt-font);
    letter-spacing: .1em;
    font-size: 18px;
}
#footer_menu_bottom ul {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding-block: 50px;
}
#footer_menu_bottom li {
    line-height: 2.0;
}
#footer_logo .logo_text {
    font-family: var(--txt-font);
    letter-spacing: .1em;
    font-weight: 600;
}
a:hover, #footer_top a:hover {
    color: var(--blue);
}
#footer_top {
    padding-block: 50px;
}
#copyright {
    position: relative;
    background: var(--blue);
}
#copyright::before {
    position: absolute;
    content: '';
    width: 1920px;
    height: 160px;
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/06/footer-bg-2.png)repeat-x;
    background-size: contain;
    z-index: -1;
    top: 0;
    transform: translateY(-95%);
    left: 0;
}
#footer_menu_bottom li a {
    display: inline-block;
    padding: .5em 1em;
    background: #fff;
}
#return_top a {
    background: var(--blue);
}

@media screen and (max-width:1450px) {
    #global_menu > ul > li > a {
        padding: 0 10px;
    }
}

@media screen and (max-width:1300px) {
    .wide > .wp-block-group__inner-container {
        padding: 100px var(--content-inner-5);
    }
}

@media screen and (max-width:1201px) {
    body.hide_sidebar #main_col {
        max-width: unset;
        width: auto!important;
        padding-inline: var(--content-inner-5);
    }
}

@media screen and (max-width:900px) {
    body.hide_sidebar #main_col {
        padding-inline: 0;
    }
}

@media screen and (min-width:1025px) {
    .spider__dots {
        display: none;
    }
}

@media screen and (max-width:781px) {
    .col-content {
        margin-inline: auto;
    }
    .col-content .txt {
        max-width: unset;
    }
    .spider__canvas {
        height: auto;
    }
    .content01 .col-content {
        flex-direction: column-reverse;
    }
    .wp-block-column.img-wrap {
        margin-inline: calc(50% - 50vw);
    }
    .col-content figure {
        width: 100%;
    }
    .con-box {
        margin-bottom: 50px;
    }
    .col-content.right {
        flex-direction: column-reverse;
    }
}

@media screen and (max-width:750px) {
    #container #page_header_inner {
        width: fit-content!important;
        padding: 30px 0;
        min-width: 200px;
        display: flex;
        justify-content: center;
    }
    #page_header .catch {
        display: inline-block;
        padding-inline: 1em;
    }
    #page_header .bg_image {
        width: 100%;
    }
}

@media screen and (max-width:500px) {
    #container #page_header_inner {
        padding-block: 15px;
    }
    body .post_content td {
        display: block;
        width: 100%!important;
        border-bottom: 2px solid var(--blue);
    }
    .post_content td:first-child {
        border-bottom: 1px solid #ddd;
    }
    .wide > .wp-block-group__inner-container {
        padding: 50px var(--content-inner-5);
    }
    #footer_menu_bottom li#menu-item-596 a {
        border-bottom: 1px solid #aaa;
        line-height: 1.0;
        padding: .1em;
        font-size: 14px;
    }
    .col-content  {
        gap: 0;
        min-height: unset;
    }
    .post_content .intro .txt {
        text-align: left;
    }
    #copyright::before {
        width: 850px;
        height: 124px;
    }
}

.grecaptcha-badge {
    z-index: 999;
    bottom: 70px!important;
}.styled_h2 {
  font-size:26px !important; text-align:left; color:#ffffff; background:#000000;  border-top:0px solid #dddddd;
  border-bottom:0px solid #dddddd;
  border-left:0px solid #dddddd;
  border-right:0px solid #dddddd;
  padding:15px 15px 15px 15px !important;
  margin:0px 0px 30px !important;
}
.styled_h3 {
  font-size:22px !important; text-align:left; color:#000000;   border-top:0px solid #dddddd;
  border-bottom:0px solid #dddddd;
  border-left:2px solid #000000;
  border-right:0px solid #dddddd;
  padding:6px 0px 6px 16px !important;
  margin:0px 0px 30px !important;
}
.styled_h4 {
  font-size:18px !important; text-align:left; color:#000000;   border-top:0px solid #dddddd;
  border-bottom:1px solid #dddddd;
  border-left:0px solid #dddddd;
  border-right:0px solid #dddddd;
  padding:3px 0px 3px 20px !important;
  margin:0px 0px 30px !important;
}
.styled_h5 {
  font-size:14px !important; text-align:left; color:#000000;   border-top:0px solid #dddddd;
  border-bottom:1px solid #dddddd;
  border-left:0px solid #dddddd;
  border-right:0px solid #dddddd;
  padding:3px 0px 3px 24px !important;
  margin:0px 0px 30px !important;
}
.q_custom_button1 {
  color:#ffffff !important;
  border-color:rgba(83,83,83,1);
}
.q_custom_button1.animation_type1 { background:#535353; }
.q_custom_button1:hover, .q_custom_button1:focus {
  color:#ffffff !important;
  border-color:rgba(125,125,125,1);
}
.q_custom_button1.animation_type1:hover { background:#7d7d7d; }
.q_custom_button1:before { background:#7d7d7d; }
.q_custom_button2 {
  color:#ffffff !important;
  border-color:rgba(83,83,83,1);
}
.q_custom_button2.animation_type1 { background:#535353; }
.q_custom_button2:hover, .q_custom_button2:focus {
  color:#ffffff !important;
  border-color:rgba(125,125,125,1);
}
.q_custom_button2.animation_type1:hover { background:#7d7d7d; }
.q_custom_button2:before { background:#7d7d7d; }
.speech_balloon_left1 .speach_balloon_text { background-color: #ffdfdf; border-color: #ffdfdf; color: #000000 }
.speech_balloon_left1 .speach_balloon_text::before { border-right-color: #ffdfdf }
.speech_balloon_left1 .speach_balloon_text::after { border-right-color: #ffdfdf }
.speech_balloon_left2 .speach_balloon_text { background-color: #ffffff; border-color: #ff5353; color: #000000 }
.speech_balloon_left2 .speach_balloon_text::before { border-right-color: #ff5353 }
.speech_balloon_left2 .speach_balloon_text::after { border-right-color: #ffffff }
.speech_balloon_right1 .speach_balloon_text { background-color: #ccf4ff; border-color: #ccf4ff; color: #000000 }
.speech_balloon_right1 .speach_balloon_text::before { border-left-color: #ccf4ff }
.speech_balloon_right1 .speach_balloon_text::after { border-left-color: #ccf4ff }
.speech_balloon_right2 .speach_balloon_text { background-color: #ffffff; border-color: #0789b5; color: #000000 }
.speech_balloon_right2 .speach_balloon_text::before { border-left-color: #0789b5 }
.speech_balloon_right2 .speach_balloon_text::after { border-left-color: #ffffff }
.qt_google_map .pb_googlemap_custom-overlay-inner { background:#00729f; color:#ffffff; }
.qt_google_map .pb_googlemap_custom-overlay-inner::after { border-color:#00729f transparent transparent transparent; }
</style>
<style id="current-page-style" type="text/css">
#header_slider .item1 .catch { font-size:38px; }
#header_slider .item1 .desc { font-size:18px; }
@media screen and (max-width:750px) {
  #header_slider .item1 .catch { font-size:20px; }
  #header_slider .item1 .desc { font-size:15px; }
}
#header_slider .item1 .overlay { background-color:rgba(0,0,0,0.3); }
#header_slider .item2 .catch { font-size:38px; }
#header_slider .item2 .desc { font-size:18px; }
@media screen and (max-width:750px) {
  #header_slider .item2 .catch { font-size:20px; }
  #header_slider .item2 .desc { font-size:15px; }
}
#header_slider .item2 .overlay { background-color:rgba(0,0,0,0.3); }
#header_slider .item3 .catch { font-size:38px; }
#header_slider .item3 .desc { font-size:18px; }
@media screen and (max-width:750px) {
  #header_slider .item3 .catch { font-size:20px; }
  #header_slider .item3 .desc { font-size:15px; }
}
#header_slider .item3 .overlay { background-color:rgba(0,0,0,0.3); }
.cb_blog_list.num1 .blog_list .title { font-size:20px; }
@media screen and (max-width:750px) {
  .cb_blog_list.num1 .blog_list .title { font-size:16px; }
}
.cb_free_space.num2 .post_content { padding-top:0px; padding-bottom:0px; }
@media screen and (max-width:750px) {
  .cb_free_space.num2 .post_content { padding-top:0px; padding-bottom:0px; }
}
.cb_free_space.num3 .post_content { padding-top:0px; padding-bottom:0px; }
@media screen and (max-width:750px) {
  .cb_free_space.num3 .post_content { padding-top:0px; padding-bottom:0px; }
}
.cb_free_space.num4 .post_content { padding-top:0px; padding-bottom:0px; }
@media screen and (max-width:750px) {
  .cb_free_space.num4 .post_content { padding-top:0px; padding-bottom:0px; }
}
.cb_free_space.num5 .post_content { padding-top:0px; padding-bottom:0px; }
@media screen and (max-width:750px) {
  .cb_free_space.num5 .post_content { padding-top:0px; padding-bottom:0px; }
}
/* kv */
#header_slider_wrap, #header_slider, #header_slider .item {
    height: 100dvh;
}
#header_slider .caption {
    left: 0;
    transform: translate(10%, -50%);
}
#header_slider .catch {
    font-family: var(--ttl-font);
    letter-spacing: .15em;
    text-align: left;
    color: #222;
}
/* #header_slider .item::before {
    position: absolute;
    content: '';
    width: 650px;
    height: 650px;
    background: #fff;
    border-radius: 50%;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0%;
    margin-block: auto;
} */
/* #header_slider .item::before {
    position: absolute;
    content: '';
    width: 650px;
    height: 650px;
    background: #fff;
    border-radius: 50%;
    z-index: 2;
    top: 0;
    bottom: 0;
    left: 0%;
    margin-block: auto;
    background-image: url(https://hirumori.co.jp/wp-content/uploads/2025/05/logo-mark.png);
    background-size: 20%;
    background-repeat: no-repeat;
    background-position: center 25%;
}
#header_slider .item::after {
    position: absolute;
    content: 'HIRUGAMINOMORI';
    top: 50%;
    margin-block: auto;
    color: #ddd;
    z-index: 10;
    font-family: var(--ttl-font);
    letter-spacing: .3em;
    font-weight: 600;
    font-size: 40px;
    transform: translate(15%, 190%);
}
#header_slider::before {
    position: absolute;
    content: '';
    width: 650px;
    height: 320px;
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/hoshi-bg02.png)no-repeat;
    background-size: contain;
    top: 0;
    left: 0;
    z-index: 1;
} */
#header_slider .bg_image {
    background-position-y: 100%!important;
}
#header_slider .catch {
    display: none;
}
#header_slider::before {
    position: absolute;
    content: '';
    width: clamp(280px,65vw,650px);
    height: clamp(300px,65vw,650px);
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/top-teim-1.png)no-repeat;
    z-index: 2;
    background-size: contain;
    top: 50%;
    transform: translateY(-50%);
    left: var(--content-inner-5);
}
/* ニュースティッカー */
#index_news_ticker_wrap {
    position: absolute;
    background: #fff;
    top: 29.5%;
    right: 1%;
    border-radius: 10px;
}
#index_news_ticker .title a,
.cb_blog_list .title span,
.cb_blog_list .desc span {
    font-family: var(--ttl-font);
    letter-spacing: .1em;
    color: #222;
}
#index_news_ticker .category,
.cb_blog_list .category a {
    background: var(--blue);
    font-family: var(--ttl-font);
    color: #222;
}
#index_news_ticker .category:hover,
.cb_blog_list .category a:hover {
    background: var(--yellow);
}

/* 新着情報 */
#container .design_button a {
    background: var(--blue);
    border-radius: 10px;
    color: #222!important;
    font-family: var(--ttl-font);
    font-weight: 600;
    letter-spacing: .1em;
    font-size: 18px;
    transition: .3s;
    border-radius: 50px;
}
#container .design_button a:hover {
    background: var(--yellow);
}
.cb_blog_list {
    padding-bottom: 150px;
}
.cb_headline .headline {
    color: #222;
    letter-spacing: .1em;
}
.cb_headline .sub_headline {
    color: #999;
    font-family: var(--ttl-font);
    letter-spacing: .1em;
    font-weight: 600;
    font-size: 30px;
}
.cb_blog_list .blog_list.inview {
    display: flex;
    flex-wrap: wrap;
    row-gap: 50px;
}

.cb_blog_list .item {
    height: auto;
    padding: 10px;
    width: 33%;
    flex-direction: column;
    border-bottom: none;
    gap: 45px;
}

.cb_blog_list .content {
    height: auto;
    width: auto;
    padding-left: 10px;
}

.cb_blog_list .image_link {
    display: block;
    width: auto;
    height: 220px;
}

.cb_blog_list .content {
    height: 170px;
    padding: 10px;
}

/* 予約 */
/* txt */
.wp-block-column.txt p {
    margin-bottom: 0;
}
.wp-block-column.txt p.point strong {
    font-size: clamp(20px,3vw,30px);
    padding: 0 1em;
    background-image: linear-gradient(45deg, rgb(255 0 0 / 30%), rgb(255 0 0 / 30%));
    background-size: 100% .5em;
    background-position: left bottom;
    background-repeat: no-repeat;
}
.wp-block-column.txt p.point {
    line-height: 1.5;
}
.post_content p.top-txt strong {
    font-size: 24px;
    font-weight: 500;
    display: inline-block;
    border-bottom: 2px solid red;
    line-height: 1.3;
    padding: 0 .1em;
}
/* link */
.wp-block-column.link {
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-banner.jpg);
    background-size: cover;
}
.wp-block-column.link a {
    position: relative;
    width: 100%;
    height: 100%;
    color: #fff;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}
.wp-block-column.link a::before {
    position: absolute;
    content: '';
    width: 350px;
    height: 165px;
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/hoshi-bg02.png)no-repeat;
    background-size: contain;
    bottom: 0;
    right: 0;
    transform: rotate(180deg);
}
.wp-block-column.link a span {
    position: relative;
    font-size: clamp(25px,3vw,30px);
    border-bottom: 1px solid;
    line-height: 1.5;
    padding: 0 1em;
    display: inline-block;
}
.wp-block-column.link a span::before {
    position: absolute;
    content: '';
    width: 30px;
    height: 30px;
    background: url(https://hirumori.co.jp/wp-content/uploads/2025/05/star01.png)no-repeat;
    z-index: 10;
    background-size: contain;
    bottom: -15px;
    left: 0;
    transition: all .5s ease;
    transform: translateX(-50%);
}
.wp-block-column.link a:hover span::before {
    left: 100%;
}
/* desc */
.wp-block-group.desc {
    padding: 10px;
    margin-top: 50px;
    border: 1px solid #999;
}
.wp-block-group.desc ul {
    margin: 0;
}
.wp-block-group.desc li {
    list-style: none;
    border-bottom: 1px dashed #999;
    font-size: 14px
}

.txt-inner {
    width: 600px;
    margin-left: auto;
    background: #fff;
    padding: var(--content-inner-3);
}
.wide.yoyaku-wrap > .wp-block-group__inner-container {
    max-width: unset;
    padding: 50px 0;
    padding-top: 100px;
}
#container .yoyaku-wrap h2 {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translate(-50%,-50%);
    font-size: clamp(20px,4vw,40px);
    background: var(--blue);
    padding: .5em;
    border-radius: 10px;
    color: #fff;
    border: 20px solid #fff;
}

/* youtube */
.insert-page.insert-page-490 {
    max-width: 1200px;
    width: 100%;
    margin-inline: auto;
    padding: 100px 0;
}

/* サービス */
/* .service-wrap {
    max-width: 1200px;
    width: 100%;
    margin-inline: auto;
} */
.service-wrap .wp-block-column {
    position: relative;
}
.service-wrap .wp-block-column .top-txt {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translate(-50%,-40%);
    background: #fff;
    padding: 35px;
    line-height: 1.0;
    font-size: clamp(30px,5vw,50px);
    border-radius: 10px;
    z-index: 50;
    writing-mode: tb;
    letter-spacing: .2em;
}
.service-wrap figure {
    position: relative;
    overflow: hidden;
}
.service-wrap a figure::before {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    background: rgb(0,0,0,.5);
    top: 0;
    transition: .5s;
    z-index: 1;
}
.service-wrap a:hover figure::before {
    top: 100%;
}
.service-wrap .wp-block-column {
    position: relative;
    height: 350px;
}
.service-wrap figure {
    position: relative;
    overflow: hidden;
    height: 100%;
}
.post_content .service-wrap figure img {
    max-width: fit-content;
    transform: translate(-25%, -25%);
}
.service-wrap {
    margin-top: 100px;
}

/*ブランコ*/
.cb_free_space.type1 {
    border: none;
}
.post_content .top-swing-wrap .original-btn a {
    margin-top: 0;
}

@media screen and (max-width:1200px) {
    .txt-inner {
        width: auto;
    }
}

@media screen and (max-width:1000px) {
    #container .yoyaku-wrap h2 {
        left: 0;
        transform: translateY(-50%);
        width: 100%;
    }
}

@media screen and (max-width:950px) {
    .cb_blog_list .item {
        width: 50%;
    }
}

@media screen and (max-width:781px) {
    .post_content .service-wrap figure img {
        transform: unset;
        height: 120%;
    }
    .service-wrap .wp-block-column .top-txt {
        margin-bottom: 0;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .wp-block-columns.service-wrap {
        gap: 0;
    }
    .youtube-wrap .wp-block-column {
        display: flex;
        justify-content: center;
    }
    .youtube-container {
        padding-inline: var(--content-inner-5);
    }
    .wp-block-column.link {
        min-height: 300px;
    }
    .wp-block-group.desc {
        margin-top: 0;
    }
    .wp-block-column.link a span {
        font-weight: 600;
    }
}

@media screen and (max-width:750px) {
    .cb_blog_list .item {
        width: 100%;
        gap: 0;
    }
    .cb_blog_list .content {
        height: auto;
    }
}

@media screen and (max-width:550px) {
    .cb_blog_list .blog_list.inview {
        row-gap: 20px;
        flex-direction: column;
    }
}

@media screen and (min-width:501px) {
    #container .yoyaku-wrap h2 br {
        display: none;
    }
}

@media screen and (max-width:500px) {
    #header_slider::before {
        transform: translate(-50%,-50%);
        left: 50%;
    }
    .wp-block-column.link a::before {
        opacity: .5;
    }
    .service-wrap a figure::before {
        content: none;
    }
    .post_content .top-swing-wrap .original-btn a {
        margin-bottom: 50px;
    }
}</style>
<script type="text/javascript">
jQuery(document).ready(function($){

  var slideWrapper = $('#header_slider'),
      iframes = slideWrapper.find('.youtube-player'),
      ytPlayers = {},
      timers = { slickNext: null };

  // YouTube IFrame Player API script load
  if ($('#header_slider .youtube-player').length) {
    if (!$('script[src="//www.youtube.com/iframe_api"]').length) {
      var tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      var firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    }
  }

  // YouTube IFrame Player API Ready
  window.onYouTubeIframeAPIReady = function(){
    slideWrapper.find('.youtube-player').each(function(){
      var ytPlayerId = $(this).attr('id');
      if (!ytPlayerId) return;
      var player = new YT.Player(ytPlayerId, {
        events: {
          onReady: function(e) {
            $('#'+ytPlayerId).css('opacity', 0).css('pointerEvents', 'none');
            iframes = slideWrapper.find('.youtube-player');
            ytPlayers[ytPlayerId] = player;
            ytPlayers[ytPlayerId].mute();
            ytPlayers[ytPlayerId].lastStatus = -1;
            var item = $('#'+ytPlayerId).closest('.item');
            if (item.hasClass('slick-current')) {
              playPauseVideo(item, 'play');
            }
          },
          onStateChange: function(e) {
            if (e.data === 0) { // ended
              $('#'+ytPlayerId).stop().css('opacity', 0);
              if (timers.slickNext) {
                clearTimeout(timers.slickNext);
                timers.slickNext = null;
              }
              setTimeout(function(){
                slideWrapper.slick('slickNext');
              }, 100); // タイムアウトを追加して動画が終わったら強制スライド
            } else if (e.data === 1) { // play
              $('#'+ytPlayerId).not(':animated').css('opacity', 1);
              var slide = $('#'+ytPlayerId).closest('.item');
              var slickIndex = slide.attr('data-slick-index') || 0;
              clearInterval(timers[slickIndex]);
              timers[slickIndex] = setInterval(function(){
                var state = ytPlayers[ytPlayerId].getPlayerState();
                if (state != 1 && state != 3) {
                  clearInterval(timers[slickIndex]);
                } else if (ytPlayers[ytPlayerId].getDuration() - ytPlayers[ytPlayerId].getCurrentTime() < 1) {
                  clearInterval(timers[slickIndex]);
                  if (timers.slickNext) {
                    clearTimeout(timers.slickNext);
                    timers.slickNext = null;
                  }
                  setTimeout(function(){
                    slideWrapper.slick('slickNext');
                  }, 100); // タイムアウトを追加して動画が終わったら強制スライド
                }
              }, 200);
            } else if (e.data === 3) { // buffering
              if (ytPlayers[ytPlayerId].lastStatus === -1) {
                $('#'+ytPlayerId).delay(100).animate({opacity: 1}, 400);
              }
            }
            ytPlayers[ytPlayerId].lastStatus = e.data;
          }
        }
      });
    });
  };

  // play or puase video
  function playPauseVideo(slide, control){
    if (!slide) {
      slide = slideWrapper.find('.slick-current');
    }
    // animate caption and logo
    function captionAnimation() {
      if( !$('body').hasClass('stop_index_slider_animation') ){

      if( slide.hasClass('first_item') ){
        $('#header_logo a').addClass('animate');
        $('#global_menu').addClass('animate');
        $('#header_slider_nav_wrap').addClass('animate');
        $('.mobile #global_menu_button').addClass('animate');
        $('#header_search').addClass('animate');
        $('#header_logo a').on('animationend webkitAnimationEnd oAnimationEnd mozAnimationEnd', function(){
          $(".first_animate_item").each(function(i){
            $(this).delay(i *700).queue(function(next) {
              $(this).addClass('animate');
              next();
            });
            if( $(this).hasClass('animation_type2') ){
              $catch_word = $("span",this);
              $catch_word.each(function(i){
                $(this).delay(i * 50).queue(function(next) {
                  $(this).addClass('animate');
                  next();
                });
              });
            };
          }).promise().done(function () {
            slide.removeClass('first_item');
          });
        });
      } else {
        slide.find(".animate_item").each(function(i){
          $(this).delay(i *700).queue(function(next) {
            $(this).addClass('animate');
            next();
          });
          if( $(this).hasClass('animation_type2') ){
            $catch_word = $("span",this);
            $catch_word.each(function(i){
              $(this).delay(i * 50).queue(function(next) {
                $(this).addClass('animate');
                next();
              });
            });
          };
        });
      }

      }
    }
    // youtube item --------------------------
    if (slide.hasClass('youtube')) {
      var ytPlayerId = slide.find('.youtube-player').attr('id');
      if (ytPlayerId) {
        switch (control) {
          case 'play':
            if (ytPlayers[ytPlayerId]) {
              ytPlayers[ytPlayerId].seekTo(0, true);
              ytPlayers[ytPlayerId].playVideo();
            }
            setTimeout(function(){
              captionAnimation();
            }, 1000);
            if (timers.slickNext) {
              clearTimeout(timers.slickNext);
              timers.slickNext = null;
            }
            break;
          case 'pause':
            slide.find(".animate_item").removeClass('animate animate_mobile');
            slide.find(".animate_item span").removeClass('animate');
            if (ytPlayers[ytPlayerId]) {
              ytPlayers[ytPlayerId].pauseVideo();
            }
            break;
        }
      }
    // video item ------------------------
    } else if (slide.hasClass('video')) {
      var video = slide.find('video').get(0);
      if (video) {
        switch (control) {
          case 'play':
            video.currentTime = 0;
            video.play();
            setTimeout(function(){
              captionAnimation();
            }, 1000);
            var slickIndex = slide.attr('data-slick-index') || 0;
            clearInterval(timers[slickIndex]);
            timers[slickIndex] = setInterval(function(){
              if (video.paused) {
                // clearInterval(timers[slickIndex]);
              } else if (video.duration - video.currentTime < 2) {
                clearInterval(timers[slickIndex]);
                if (timers.slickNext) {
                  clearTimeout(timers.slickNext);
                  timers.slickNext = null;
                }
                slideWrapper.slick('slickNext');
                setTimeout(function(){
                  video.currentTime = 0;
                }, 2000);
              }
            }, 200);
            break;
          case 'pause':
            slide.find(".animate_item").removeClass('animate animate_mobile');
            slide.find(".animate_item span").removeClass('animate');
            video.pause();
            break;
        }
      }
    // normal image item --------------------
    } else if (slide.hasClass('image_item')) {
      switch (control) {
        case 'play':
          setTimeout(function(){
            captionAnimation();
          }, 1000);
          if (timers.slickNext) {
            clearTimeout(timers.slickNext);
            timers.slickNext = null;
          }
          timers.slickNext = setTimeout(function(){
            slideWrapper.slick('slickNext');
          }, 5000);
          break;
        case 'pause':
          slide.find(".animate_item").removeClass('animate animate_mobile');
          slide.find(".animate_item span").removeClass('animate');
          break;
      }
    }
  }


  // resize video
  function video_resize(object){
    var slider_height = $('#header_slider').innerHeight();
    var slider_width = slider_height*(16/9);
    var win_width = $(window).width();
    var win_height = win_width*(9/16);
    if(win_width > slider_width) {
      object.addClass('type1');
      object.removeClass('type2');
      object.css({'width': '100%', 'height': win_height});
    } else {
      object.removeClass('type1');
      object.addClass('type2');
      object.css({'width':slider_width, 'height':slider_height });
    }
  }

  // Adjust height for mobile device
  function adjust_height(){
    var winH = $(window).innerHeight();
    $('#header_slider_wrap').css('height', winH);
    $('#header_slider').css('height', winH);
    $('#header_slider .item').css('height', winH);
  }

  // DOM Ready
  $(function() {
    slideWrapper.on('beforeChange', function(event, slick, currentSlide, nextSlide) {
      if (currentSlide == nextSlide) return;
      slick.$slides.eq(nextSlide).addClass('animate');
      setTimeout(function(){
        playPauseVideo(slick.$slides.eq(currentSlide), 'pause');
      }, 100); // タイムアウトを追加
      playPauseVideo(slick.$slides.eq(nextSlide), 'play');
    });
    slideWrapper.on('afterChange', function(event, slick, currentSlide) {
      slick.$slides.not(':eq(' + currentSlide + ')').removeClass('animate');
    });
    slideWrapper.on('swipe', function(event, slick, direction){
      slideWrapper.slick('setPosition');
    });

    //start the slider
    slideWrapper.slick({
      slide: '.item',
      infinite: true,
            dots: true,
            arrows: false,
      slidesToShow: 1,
      slidesToScroll: 1,
      swipe: false,
      pauseOnFocus: false,
      pauseOnHover: false,
      autoplay: false,
      fade: true,
      autoplaySpeed:5000,
      speed:1500,
      easing: 'easeOutExpo',
          });

    
    // initialize / first animate
    // adjust_height();
    video_resize($('.video_wrap'));
    playPauseVideo($('#header_slider .item1'), 'play');
    $('#header_slider .item1').addClass('animate');
  });

  // Resize event
  var currentWidth = $(window).innerWidth();
  $(window).on('resize', function(){
    // adjust_height();
    if (currentWidth == $(this).innerWidth()) {
      return;
    } else {
      video_resize($('.video_wrap'));
    };
  });
});
</script>
<script type="text/javascript">
jQuery(document).ready(function($){

    if( $('.cb_tab_content').length ){
    $('.tab_content_top .tab .item').on('click', function() {
      $(this).siblings().removeClass('active');
      $(this).addClass('active');
      tab_id = $(this).data('tab-num');
      $(this).closest('.cb_tab_content').find('.tab_content_bottom .item').removeClass('active');
      $('#' + tab_id).addClass('active');
      return false;
    });
    $('.tab_content_bottom .image_area_inner').each(function(){
      var image_num = $('.image',this).length;
      if(image_num > 1){
        $(this).slick({
          infinite: true,
          dots: true,
          arrows: false,
          slidesToShow: 1,
          slidesToScroll: 1,
          swipeToSlide: false,
          adaptiveHeight: false,
          pauseOnHover: true,
          autoplay: true,
          fade: true,
          easing: 'easeOutExpo',
          speed: 700,
          autoplaySpeed: 5000
        });
      };
    });
  };

    if( $('.cb_service_list').length ){

    $(".cb_service_item_list .item").hover(function(){
      $(this).siblings().removeClass('active');
      $(this).addClass('active');
      service_id = $(this).data('service-id');
      $(this).closest('.cb_service_list').find('.cb_service_image_list .image').removeClass('active');
      $('#' + service_id).addClass('active');
      $(this).siblings().each(function(){
         var title_height = $('.title',this).height();
         var content_height = $('.content',this).height() - title_height + 10;
         $(this).find('.content').css({'cssText':'bottom:-' + content_height + 'px;'});
      });
      $('.content',this).css({'cssText':'bottom:0px;'});
    }, function(){
      $(this).removeClass('active');
      var title_height = $('.title',this).height();
      var content_height = $('.content',this).height() - title_height + 10;
      $('.content',this).css({'cssText':'bottom:-' + content_height + 'px;'});
    });

    $('.cb_service_item_list .item').each(function(){
      var title_height = $('.title',this).height();
      var content_height = $('.content',this).height() - title_height + 10;
      $('.content',this).css({'cssText':'bottom:-' + content_height + 'px;'});
      if($(this).hasClass('active')){
        $('.content',this).css({'cssText':'bottom:0px;'});
      }
    });

    $(window).on('resize', function(){
      $('.cb_service_item_list .item').each(function(){
        var title_height = $('.title',this).height();
        var content_height = $('.content',this).height() - title_height + 10;
        $('.content',this).css({'cssText':'bottom:-' + content_height + 'px;'});
        if($(this).hasClass('active')){
          $('.content',this).css({'cssText':'bottom:0px;'});
        }
      });
    });

  };

    if( $('.cb_news_list .news_category_list').length ){
    $('.cb_news_list .news_category_list a[href^="#"]').on('click',function(e) {
      e.preventDefault();
      e.stopPropagation();
      $(this).parent().siblings().removeClass('active');
      $(this).parent().addClass('active');
      var news_category_id = $(this).attr('data-news-category');
      if(news_category_id){
        $(this).closest('.cb_news_list').find('.news_list').removeClass('active inview animate');
        $(news_category_id).addClass('active');
      }
    });
  };

});
</script>
<script type="text/javascript">
jQuery(document).ready(function($){

  $('.faq_list .question').on('click', function() {
    $('.faq_list .question').not($(this)).removeClass('active');
    if( $(this).hasClass('active') ){
      $(this).removeClass('active');
    } else {
      $(this).addClass('active');
    }
    $(this).next('.answer').slideToggle(600 ,'easeOutExpo');
    $('.faq_list .answer').not($(this).next('.answer')).slideUp(600 ,'easeOutExpo');
  });

  
  
});
</script>
<script type="text/javascript">
jQuery(document).ready(function($){

  if( $('.megamenu_a .slider').length ){
    $('.megamenu_a .slider').slick({
      infinite: true,
      dots: false,
      arrows: false,
      slidesToShow: 3,
      slidesToScroll: 1,
      swipeToSlide: true,
      touchThreshold: 20,
      adaptiveHeight: false,
      pauseOnHover: true,
      autoplay: true,
      fade: false,
      easing: 'easeOutExpo',
      speed: 700,
      autoplaySpeed: 5000
    });
    $('.megamenu_a .prev_item').on('click', function() {
      $(this).closest('.megamenu_a').find('.slider').slick('slickPrev');
    });
    $('.megamenu_a .next_item').on('click', function() {
      $(this).closest('.megamenu_a').find('.slider').slick('slickNext');
    });
  };

  if( $('.megamenu_b .service_list_area').length ){
    $(".megamenu_b .service_item_list .item").hover(function(){
      $(this).siblings().removeClass('active');
      $(this).addClass('active');
      service_id = $(this).data('service-id');
      $(this).closest('.service_list_area').find('.service_image_list .image').removeClass('active');
      $('#' + service_id).addClass('active');
    }, function(){
      $(this).removeClass('active');
    });
  };

});
</script>
<!-- Google tag (gtag.js) -->
<script async="" src="https://www.googletagmanager.com/gtag/js?id=G-NKGNZ6E55K"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-NKGNZ6E55K');
</script>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&amp;family=Zen+Kaku+Gothic+New&amp;display=swap" rel="stylesheet"/>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/css/design-plus.css?ver=2.11.3" rel="stylesheet"/>
<script>
jQuery(function ($) {
	var $window = $(window);
	var $body = $('body');
  // quick tag - underline ------------------------------------------
  if ($('.q_underline').length) {
    var gradient_prefix = null;

    $('.q_underline').each(function(){
      var bbc = $(this).css('borderBottomColor');
      if (jQuery.inArray(bbc, ['transparent', 'rgba(0, 0, 0, 0)']) == -1) {
        if (gradient_prefix === null) {
          gradient_prefix = '';
          var ua = navigator.userAgent.toLowerCase();
          if (/webkit/.test(ua)) {
            gradient_prefix = '-webkit-';
          } else if (/firefox/.test(ua)) {
            gradient_prefix = '-moz-';
          } else {
            gradient_prefix = '';
          }
        }
        $(this).css('borderBottomColor', 'transparent');
        if (gradient_prefix) {
          $(this).css('backgroundImage', gradient_prefix+'linear-gradient(left, transparent 50%, '+bbc+ ' 50%)');
        } else {
          $(this).css('backgroundImage', 'linear-gradient(to right, transparent 50%, '+bbc+ ' 50%)');
        }
      }
    });

    $window.on('scroll.q_underline', function(){
      $('.q_underline:not(.is-active)').each(function(){
        var top = $(this).offset().top;
        if ($window.scrollTop() > top - window.innerHeight) {
          $(this).addClass('is-active');
        }
      });
      if (!$('.q_underline:not(.is-active)').length) {
        $window.off('scroll.q_underline');
      }
    });
  }
</script>
<link href="https://hirumori.co.jp/wp-content/uploads/2025/05/cropped-logo-mark-32x32.png" rel="icon" sizes="32x32"/>
<link href="https://hirumori.co.jp/wp-content/uploads/2025/05/cropped-logo-mark-192x192.png" rel="icon" sizes="192x192"/>
<link href="https://hirumori.co.jp/wp-content/uploads/2025/05/cropped-logo-mark-180x180.png" rel="apple-touch-icon"/>
<meta content="https://hirumori.co.jp/wp-content/uploads/2025/05/cropped-logo-mark-270x270.png" name="msapplication-TileImage"/>
</head>
<body class="home wp-singular page-template-default page page-id-50 wp-embed-responsive wp-theme-solaris_tcd088 stop_index_slider_animation hide_sidebar use_header_fix use_mobile_header_fix" id="body">
<header id="header">
<div id="header_logo">
<h1 class="logo">
<a href="https://hirumori.co.jp/" title="昼神温泉　ホテルひるがみの森">
<img alt="昼神温泉　ホテルひるがみの森" class="logo_image pc" height="81" src="https://hirumori.co.jp/wp-content/uploads/2025/06/logo-head03.png?1754389815" title="昼神温泉　ホテルひるがみの森" width="326"/>
<img alt="昼神温泉　ホテルひるがみの森" class="logo_image mobile" height="81" src="https://hirumori.co.jp/wp-content/uploads/2025/06/logo-head03.png?1754389815" title="昼神温泉　ホテルひるがみの森" width="326"/> </a>
</h1>
</div>
<a href="#" id="global_menu_button"><span></span><span></span><span></span></a>
<nav id="global_menu">
<ul class="menu" id="menu-global_nav"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-50 current_page_item menu-item-68" id="menu-item-68"><a aria-current="page" href="https://hirumori.co.jp/">ホーム</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-138" id="menu-item-138"><a href="https://hirumori.co.jp/room/">客室</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-139" id="menu-item-139"><a href="https://hirumori.co.jp/meal/">料理</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-140" id="menu-item-140"><a href="https://hirumori.co.jp/hot_spring/">温泉</a>
<ul class="sub-menu">
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-380" id="menu-item-380"><a href="https://hirumori.co.jp/hot_spring/#day">日帰り入浴</a></li>
</ul>
</li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-73" id="menu-item-73"><a href="https://hirumori.co.jp/facilities/">施設</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-72" id="menu-item-72"><a href="https://hirumori.co.jp/sightseeing/">観光</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-71" id="menu-item-71"><a href="https://hirumori.co.jp/access/">アクセス</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-69" id="menu-item-69"><a href="https://hirumori.co.jp/news/">新着情報</a>
<ul class="sub-menu">
<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-764" id="menu-item-764"><a href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-765" id="menu-item-765"><a href="https://hirumori.co.jp/category/blog-category/">ブログ</a></li>
</ul>
</li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-70" id="menu-item-70"><a href="https://hirumori.co.jp/contact/">お問い合わせ</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-132" id="menu-item-132"><a href="https://www.jhpds.net/hirumori/uw/uwp3100/uww3101.do?yadNo=306060" target="_blank">ご予約はこちら</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-531" id="menu-item-531"><a href="https://www.jhpds.net/hirumori/hi/hip9000/hiw9001Init.do?yadNo=306060" target="_blank">予約照会・キャンセル</a></li>
</ul> </nav>
</header>
<div id="container">
<div id="header_slider_wrap">
<div class="slick-initialized slick-slider slick-dotted" id="header_slider">
<div class="slick-list draggable"><div class="slick-track" style="opacity: 1; width: 5760px;"><div aria-describedby="slick-slide-control00" aria-hidden="true" class="item image_item item1 first_item slick-slide bg_animation_type7" data-slick-index="0" id="slick-slide00" role="tabpanel" style="width: 1920px; position: relative; left: 0px; top: 0px; z-index: 998; opacity: 0; transition: opacity 1500ms;" tabindex="-1">
<div class="caption">
<h2 class="animate_item first_animate_item catch rich_font_type3 animation_type2"><span>長</span><span>野</span><span>県</span><span>阿</span><span>智</span><span>村</span><span>の</span><br/><span>昼</span><span>神</span><span>温</span><span>泉</span><span>郷</span><span>へ</span><span>よ</span><span>う</span><span>こ</span><span class="last_word">そ</span></h2>
</div><!-- END .caption -->
<div class="overlay"></div>
<div class="bg_image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/06/kv04.jpg) no-repeat center top; background-size:cover;"></div>
</div><div aria-describedby="slick-slide-control01" aria-hidden="true" class="item image_item item2 slick-slide bg_animation_type7 animate" data-slick-index="1" id="slick-slide01" role="tabpanel" style="width: 1920px; position: relative; left: -1920px; top: 0px; z-index: 998; opacity: 0; transition: opacity 1500ms;" tabindex="0">
<div class="caption">
<h2 class="animate_item catch rich_font_type3 animation_type1">長野県阿智村の<br/>
昼神温泉郷へようこそ</h2>
</div><!-- END .caption -->
<div class="overlay"></div>
<div class="bg_image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/05/kv01.jpg) no-repeat center top; background-size:cover;"></div>
</div><div aria-describedby="slick-slide-control02" aria-hidden="false" class="item image_item item3 slick-slide bg_animation_type7 animate slick-current slick-active" data-slick-index="2" id="slick-slide02" role="tabpanel" style="width: 1920px; position: relative; left: -3840px; top: 0px; z-index: 1000; opacity: 1; transition: opacity 1500ms;" tabindex="-1">
<div class="caption">
<h2 class="animate_item catch rich_font_type3 animation_type1">長野県阿智村の<br/>
昼神温泉郷へようこそ</h2>
</div><!-- END .caption -->
<div class="overlay"></div>
<div class="bg_image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/05/kv03.jpg) no-repeat center top; background-size:cover;"></div>
</div></div></div><!-- END .item -->
<!-- END .item -->
<!-- END .item -->
<ul class="slick-dots" role="tablist" style="display: block;"><li class="" role="presentation"><button aria-controls="slick-slide00" aria-label="1 of 3" id="slick-slide-control00" role="tab" tabindex="-1" type="button">1</button></li><li class="" role="presentation"><button aria-controls="slick-slide01" aria-label="2 of 3" aria-selected="true" id="slick-slide-control01" role="tab" tabindex="0" type="button">2</button></li><li class="slick-active" role="presentation"><button aria-controls="slick-slide02" aria-label="3 of 3" id="slick-slide-control02" role="tab" tabindex="-1" type="button">3</button></li></ul></div><!-- END #header_slider -->
</div><!-- END #header_slider_wrap -->
<div id="index_content_builder">
<div class="cb_content white_content cb_blog_list num1" id="cb_content_1">
<h2 class="cb_headline inview"><span class="headline rich_font common_headline">新着情報</span><span class="sub_headline">NEWS・BLOG</span></h2>
<div class="blog_list inview">
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/07/19/%e7%a5%9e%e3%81%8c%e5%ae%bf%e3%82%8b%e6%b8%a9%e6%b3%89%e5%9c%b0%e3%80%80%e6%98%bc%e3%80%8c%e7%a5%9e%e3%80%8d%e6%b8%a9%e6%b3%89/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/05/room5.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-25T10:14:40+09:00">2025.07.19</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/07/19/%e7%a5%9e%e3%81%8c%e5%ae%bf%e3%82%8b%e6%b8%a9%e6%b3%89%e5%9c%b0%e3%80%80%e6%98%bc%e3%80%8c%e7%a5%9e%e3%80%8d%e6%b8%a9%e6%b3%89/"><span>”神が宿る”温泉地　昼「神」温泉</span></a></h3>
<p class="desc"><span>昼神温泉の由来は、大きく分けて2つの伝説が伝えられています。一つは、日本武尊（やまとたけるのみこと）が神坂峠を越える際に、たまたま噛んでいた蒜（にんにく）を投げつけて荒ぶる山の神を退治したという伝説から「蒜噛（ひるがみ）」と呼ばれ、それが転訛して「昼神」になったという説。も</span></p>
</div>
</div>
</article>
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/06/01/20211121-751/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/06/プール営業のお知らせ.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-14T14:17:25+09:00">2025.06.01</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/06/01/20211121-751/"><span>2025年プール営業のご案内</span></a></h3>
<p class="desc"><span>2025年プール営業は、「夏季のみ」となります。★　営業日・時間　★【7・8月】7月19日～8月31日　全日【9月】6・7・13・14・20・21日＜営業時間＞　10：30～20：00(2025年ナイトプールは営業いたしません)★　ご利用料金　★大人 1,300円小人 1,</span></p>
</div>
</div>
</article>
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/05/01/bbq/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/06/05013.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-14T14:15:31+09:00">2025.05.01</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/05/01/bbq/"><span>炭火で本格BBQ(バーベキュー）！</span></a></h3>
<p class="desc"><span>ひるがみの森では、『BBQ（バーベキュー）』もできちゃいます！！手ぶらでOK！もちろん、食材の持ち込みやご注文もOK！温泉街の高台に佇む当館からは、温泉街が一望できます♪さぁ、ひるがみの森へレッツゴー！！＜営業期間＞2025年　4</span></p>
</div>
</div>
</article>
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/07/02/%ef%bc%bc%e3%81%86%e3%81%be%e3%81%84%e3%82%82%e3%82%93%e3%83%95%e3%82%a7%e3%82%a2%e9%96%8b%e5%82%ac%e4%b8%ad%ef%bc%81%ef%bc%9f%ef%bc%8f/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/07/牡蠣.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-14T14:20:47+09:00">2025.07.02</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/07/02/%ef%bc%bc%e3%81%86%e3%81%be%e3%81%84%e3%82%82%e3%82%93%e3%83%95%e3%82%a7%e3%82%a2%e9%96%8b%e5%82%ac%e4%b8%ad%ef%bc%81%ef%bc%9f%ef%bc%8f/"><span>海なし県で味わう、絶品の海の幸に舌鼓間違いなし‼</span></a></h3>
<p class="desc"><span>日本一の星空の村「阿智村」にある昼神温泉郷☆彡信州の山々の雄大な自然に囲まれた、昼神温泉の高台に佇む「ひるがみの森」。大人気の「南信州最大級の大型ブランコ」や、当館でしか手に入らない「宿泊証明書」。世界に一枚だけの「思い出の旅行写真」が撮れるガラスボード看板等々、　当館でしか体験出来ないことが盛り</span></p>
</div>
</div>
</article>
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/07/19/20250518-788/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/06/0514.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-20T11:50:06+09:00">2025.07.19</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/07/19/20250518-788/"><span>★★★早いもの順！スピード勝負！！当館人気3種の『ハイグレード客室』へ無料グレードアッププラン</span></a></h3>
<p class="desc"><span>＼早いもの順！スピード勝負！よ～ぃ、ドン！！！／当館人気3種の『ハイグレード客室』へ無料グレードアッププラン～【9月対象SALE】8/31までの予約で最大33％オフ！数量限定客室のため、予約が埋まり次第、即終了！！ご宿泊料金は「おまかせ客室」の1番安価な料金。なのに</span></p>
</div>
</div>
</article>
<article class="item">
<a class="image_link animate_background" href="https://hirumori.co.jp/2025/07/08/20210418-727/">
<div class="image_wrap">
<div class="image" style="background:url(https://hirumori.co.jp/wp-content/uploads/2025/06/0402.jpg) no-repeat center center; background-size:cover;"></div>
</div>
</a>
<div class="content">
<div class="content_inner">
<ul class="meta clearfix">
<li class="category"><a class="cat_id1" href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="date"><time class="entry-date updated" datetime="2025-07-14T14:28:30+09:00">2025.07.08</time></li>
</ul>
<h3 class="title"><a href="https://hirumori.co.jp/2025/07/08/20210418-727/"><span>好評につき期間延長しちゃいます「＃車中泊」押し活キャンペーン！</span></a></h3>
<p class="desc"><span>ひる森の〈車中泊 押し活キャンペーン！〉　大好評につき、『9月』も開催決定！2025年9月1日　～　9月30日　まで期間限定で割引解放♪(通常)駐車場料金2,500円/台　→　ここから500円引　＋　ひるがみの森の名入りハンドタオルプレゼント2,000円/台　＋　館内利用料　で24</span></p>
</div>
</div>
</article>
</div><!-- END .blog_list -->
<div class="design_button inview type1 shape_type2">
<a href="https://hirumori.co.jp/news/"><span>新着情報一覧</span></a>
</div>
</div><!-- END .cb_content -->
<div class="cb_content cb_free_space num2 type2" id="cb_content_2">
<div class="post_content clearfix inview">
<div class="insert-page insert-page-481" data-post-id="481">
<div class="wp-block-group wide yoyaku-wrap"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<h2 class="wp-block-heading has-text-align-center">公式WEBサイトでの<br/>ご予約が一番お得！</h2>
<div class="wp-block-columns is-layout-flex wp-container-core-columns-is-layout-9d6595d7 wp-block-columns-is-layout-flex">
<div class="wp-block-column txt is-layout-flow wp-block-column-is-layout-flow">
<div class="wp-block-group txt-inner"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<p class="top-txt">公式WEBサイトからのご予約で、<br/>チェックアウト時のご精算時に、ご宿泊料金より</p>
<p class="point"><strong>大人1人1泊あたり1,000円割引</strong><br/>（お子様は、1人500円割引）</p>
<div class="wp-block-group desc"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<p>【注意事項】</p>
<ul class="wp-block-list">
<li>「1泊2食付のプラン」のみ割引対象となります。</li>
<li>お子様は「食事・布団付き」でご予約の場合に限ります。</li>
<li>他の割引券等と併用はできません。</li>
<li>ご予約方法は、インターネット申し込みのみとなります。</li>
<li>ご予約の際、精算方法は「現地決済」でご予約ください。</li>
</ul>
</div></div>
</div></div>
</div>
<div class="wp-block-column link is-layout-flow wp-block-column-is-layout-flow">
<a href="https://www.jhpds.net/hirumori/uw/uwp3100/uww3101.do?yadNo=306060" target="_blank"><span>ご予約はこちらから</span></a>
</div>
</div>
</div></div>
</div>
</div>
</div><!-- END .cb_free_space -->
<div class="cb_content cb_free_space num3 type2" id="cb_content_3">
<div class="post_content clearfix inview">
<p></p><div class="insert-page insert-page-494" data-post-id="494">
<div class="wp-block-columns service-wrap is-layout-flex wp-container-core-columns-is-layout-9d6595d7 wp-block-columns-is-layout-flex">
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<a href="https://hirumori.co.jp/room/">
<p class="has-text-align-center top-txt">客室</p>
<figure class="wp-block-image size-large"><img alt="" class="wp-image-137" decoding="async" fetchpriority="high" height="576" sizes="(max-width: 1024px) 100vw, 1024px" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb34120000434fjb887B.jpg" srcset="https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-room-1024x576.jpg 1024w, https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-room-300x169.jpg 300w, https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-room-768x432.jpg 768w, https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-room-1536x864.jpg 1536w, https://hirumori.co.jp/wp-content/uploads/2025/05/FHD-room.jpg 1920w" width="1024"/></figure>
</a>
</div>
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<a href="https://hirumori.co.jp/meal/">
<p class="has-text-align-center top-txt">料理</p>
<figure class="wp-block-image size-large"><img alt="" class="wp-image-564" decoding="async" height="576" sizes="(max-width: 1024px) 100vw, 1024px" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4w120000434fjfC92C.jpg" srcset="https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-meal01-1024x576.jpg 1024w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-meal01-300x169.jpg 300w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-meal01-768x432.jpg 768w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-meal01-1536x864.jpg 1536w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-meal01.jpg 1920w" width="1024"/></figure>
</a>
</div>
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<a href="https://hirumori.co.jp/hot_spring/">
<p class="has-text-align-center top-txt">温泉</p>
<figure class="wp-block-image size-large"><img alt="" class="wp-image-562" decoding="async" height="576" sizes="(max-width: 1024px) 100vw, 1024px" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb0h120000434fjlE794.jpg" srcset="https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-hot_spring01-1024x576.jpg 1024w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-hot_spring01-300x169.jpg 300w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-hot_spring01-768x432.jpg 768w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-hot_spring01-1536x864.jpg 1536w, https://hirumori.co.jp/wp-content/uploads/2025/06/FHD-hot_spring01.jpg 1920w" width="1024"/></figure>
</a>
</div>
</div>
</div><p></p>
</div>
</div><!-- END .cb_free_space -->
<div class="cb_content cb_free_space num4 type2" id="cb_content_4">
<div class="post_content clearfix inview">
<p></p><div class="insert-page insert-page-490" data-post-id="490">
<div class="wp-block-group youtube-container"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<div class="wp-block-group h2title"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<h2 class="wp-block-heading">YouTubeチャンネル</h2>
<p>YOUTUBE CHANNEL</p>
</div></div>
<div class="wp-block-columns youtube-wrap is-layout-flex wp-container-core-columns-is-layout-9d6595d7 wp-block-columns-is-layout-flex">
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<iframe allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen="" frameborder="0" height="315" referrerpolicy="strict-origin-when-cross-origin" src="https://www.youtube.com/embed/oyz9893Oj70?si=SwVH3Of-4BRmnXs6" title="YouTube video player" width="560"></iframe>
</div>
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<iframe allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen="" frameborder="0" height="315" referrerpolicy="strict-origin-when-cross-origin" src="https://www.youtube.com/embed/ZNva_8_NfBc?si=cb52jlfZzl7k2jGQ" title="YouTube video player" width="560"></iframe>
</div>
<div class="wp-block-column is-layout-flow wp-block-column-is-layout-flow">
<iframe allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen="" frameborder="0" height="315" referrerpolicy="strict-origin-when-cross-origin" src="https://www.youtube.com/embed/Y8Nu-VQzudU?si=O9u7IdC3TIg1Uvz4" title="YouTube video player" width="560"></iframe>
</div>
</div>
<div class="wp-block-buttons original-btn is-content-justification-center is-layout-flex wp-container-core-buttons-is-layout-16018d1d wp-block-buttons-is-layout-flex">
<div class="wp-block-button"><a class="wp-block-button__link wp-element-button" href="https://www.youtube.com/@hirumori_4321/videos" rel="noreferrer noopener" target="_blank">チャンネルを見る</a></div>
</div>
</div></div>
<p></p>
</div><p></p>
</div>
</div><!-- END .cb_free_space -->
<div class="cb_content cb_free_space num5 type1" id="cb_content_5">
<div class="post_content clearfix inview">
<div class="top-swing-wrap">
<div class="insert-page insert-page-887" data-post-id="887">
<div class="wp-block-group con-box content01"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<div class="wp-block-columns col-content is-layout-flex wp-container-core-columns-is-layout-9d6595d7 wp-block-columns-is-layout-flex">
<div class="wp-block-column img-wrap is-layout-flow wp-block-column-is-layout-flow">
<figure class="wp-block-image size-large"><img alt="" class="wp-image-246" decoding="async" height="576" sizes="(max-width: 1024px) 100vw, 1024px" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb35120000434fjrF3F1.jpg" srcset="https://hirumori.co.jp/wp-content/uploads/2025/05/facilities01-1024x576.jpg 1024w, https://hirumori.co.jp/wp-content/uploads/2025/05/facilities01-300x169.jpg 300w, https://hirumori.co.jp/wp-content/uploads/2025/05/facilities01-768x432.jpg 768w, https://hirumori.co.jp/wp-content/uploads/2025/05/facilities01-1536x864.jpg 1536w, https://hirumori.co.jp/wp-content/uploads/2025/05/facilities01.jpg 1920w" width="1024"/></figure>
</div>
<div class="wp-block-column is-vertically-aligned-center txt-wrap is-layout-flow wp-block-column-is-layout-flow">
<div class="wp-block-group h2title"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<h2 class="wp-block-heading">大型ブランコ</h2>
<p>LARGE SWING</p>
</div></div>
<div class="wp-block-group txt"><div class="wp-block-group__inner-container is-layout-constrained wp-block-group-is-layout-constrained">
<p>ホテルひるがみの森の人気スポット「大型ブランコ」は、大人も思わず笑顔になる特別な体験ができる場所です。<br/>大型ブランコの他に、夏には室内温水プールやウォータースライダーも開催いたします。<br/>ご友人同士やご家族で楽しむことができるような施設をご用意しております。</p>
<div class="wp-block-buttons is-layout-flex wp-block-buttons-is-layout-flex">
<div class="wp-block-button original-btn"><a class="wp-block-button__link wp-element-button" href="https://hirumori.co.jp/facilities/">施設について詳しく見る</a></div>
</div>
</div></div>
</div>
</div>
</div></div>
</div>
</div>
</div>
</div><!-- END .cb_free_space -->
</div><!-- END #index_content_builder -->
<footer id="footer">
<div id="return_top2">
<a href="#body"><span>TOP</span></a>
</div>
<div id="footer_top">
<div id="footer_logo">
<p class="logo">
<a href="https://hirumori.co.jp/" title="昼神温泉　ホテルひるがみの森">
<span class="logo_text">昼神温泉　ホテルひるがみの森</span>
</a>
</p>
</div>
<ul class="sns_button_list clearfix color_type2" id="footer_sns">
<li class="insta"><a href="https://www.instagram.com/hirugami_no_mori/" rel="nofollow noopener" target="_blank" title="Instagram"><span>Instagram</span></a></li> <li class="youtube"><a href="https://www.youtube.com/@hirumori_4321/videos" rel="nofollow noopener" target="_blank" title="Youtube"><span>Youtube</span></a></li> </ul>
</div><!-- END #footer_top -->
<div id="footer_banner">
</div><!-- END #footer_banner -->
<div id="footer_menu_bottom">
<ul class="menu" id="menu-footer-nav"><li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-552" id="menu-item-552"><a href="tel:0265-43-4321">TEL：0265-43-4321</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-596" id="menu-item-596"><a href="https://www.google.com/maps/place/%E3%83%9B%E3%83%86%E3%83%AB%E3%81%B2%E3%82%8B%E3%81%8C%E3%81%BF%E3%81%AE%E6%A3%AE/@35.453985,137.712953,16z/data=!4m9!3m8!1s0x601ca30846ec1cf7:0xd6ca85aa63bf5efd!5m2!4m1!1i2!8m2!3d35.4539846!4d137.7129525!16s%2Fg%2F1trrd89n?hl=ja&amp;entry=ttu&amp;g_ep=EgoyMDI1MDYwNC4wIKXMDSoASAFQAw%3D%3D" target="_blank">〒395-0304 長野県下伊那郡阿智村智里567-10</a></li>
</ul> </div>
<p id="copyright">Copyright © 株式会社やどはく</p>
</footer>
</div><!-- #container -->
<div id="return_top">
<a href="#body"><span>TOP</span></a>
</div>
<div id="drawer_menu">
<nav>
<ul class="menu" id="mobile_menu"><li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-home current-menu-item page_item page-item-50 current_page_item menu-item-68"><a aria-current="page" href="https://hirumori.co.jp/">ホーム</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-138"><a href="https://hirumori.co.jp/room/">客室</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-139"><a href="https://hirumori.co.jp/meal/">料理</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-140"><a href="https://hirumori.co.jp/hot_spring/">温泉</a>
<ul class="sub-menu">
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-380"><a href="https://hirumori.co.jp/hot_spring/#day">日帰り入浴</a></li>
</ul>
</li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-73"><a href="https://hirumori.co.jp/facilities/">施設</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-72"><a href="https://hirumori.co.jp/sightseeing/">観光</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-71"><a href="https://hirumori.co.jp/access/">アクセス</a></li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-69"><a href="https://hirumori.co.jp/news/">新着情報</a>
<ul class="sub-menu">
<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-764"><a href="https://hirumori.co.jp/category/news-category/">お知らせ</a></li>
<li class="menu-item menu-item-type-taxonomy menu-item-object-category menu-item-765"><a href="https://hirumori.co.jp/category/blog-category/">ブログ</a></li>
</ul>
</li>
<li class="menu-item menu-item-type-post_type menu-item-object-page menu-item-70"><a href="https://hirumori.co.jp/contact/">お問い合わせ</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-132"><a href="https://www.jhpds.net/hirumori/uw/uwp3100/uww3101.do?yadNo=306060" target="_blank">ご予約はこちら</a></li>
<li class="menu-item menu-item-type-custom menu-item-object-custom menu-item-531"><a href="https://www.jhpds.net/hirumori/hi/hip9000/hiw9001Init.do?yadNo=306060" target="_blank">予約照会・キャンセル</a></li>
</ul> </nav>
<div id="mobile_banner">
</div><!-- END #footer_mobile_banner -->
</div>
<script>


jQuery(document).ready(function($){

  
  $("#page_header .bg_image").addClass('animate');
  $("#page_header .animate_item").each(function(i){
    $(this).delay(i *700).queue(function(next) {
      $(this).addClass('animate');
      next();
    });
  });

  if( $('#page_header .animation_type2').length ){
    setTimeout(function(){
      $("#page_header .animation_type2 span").each(function(i){
        $(this).delay(i * 50).queue(function(next) {
          $(this).addClass('animate');
          next();
        });
      });
    }, 500);
  };

  $(window).on('scroll', function(i) {
    var scTop = $(this).scrollTop();
    var scBottom = scTop + $(this).height();
    $('.inview').each( function(i) {
      var thisPos = $(this).offset().top + 100;
      if ( thisPos < scBottom ) {
        $(this).addClass('animate');
      }
    });
  });

});

jQuery(window).on('load', function(i) {
  var scTop = jQuery(this).scrollTop();
  var scBottom = scTop + jQuery(this).height();
  jQuery('.inview').each( function(i) {
    var thisPos = jQuery(this).offset().top + 100;
    if ( thisPos < scBottom ) {
      jQuery(this).addClass('animate');
    }
  });
});

</script>
<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/solaris_tcd088\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<link href="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/slick.css?ver=1.0.0" id="slick-style-css" media="all" rel="stylesheet" type="text/css"/>
<style id="core-block-supports-inline-css" type="text/css">
.wp-container-core-columns-is-layout-9d6595d7{flex-wrap:nowrap;}.wp-container-core-buttons-is-layout-16018d1d{justify-content:center;}
</style>
<script async="async" data-wp-strategy="async" id="comment-reply-js" src="https://hirumori.co.jp/wp-includes/js/comment-reply.min.js?ver=6.8.2" type="text/javascript"></script>
<script id="snow-monkey-forms-js-before" type="text/javascript">
/* <![CDATA[ */
var snowmonkeyforms = {"view_json_url":"https:\/\/hirumori.co.jp\/wp-json\/snow-monkey-form\/v1\/view?ver=1754389815","nonce":"41512e99c3"}
/* ]]> */
</script>
<script id="snow-monkey-forms-js" src="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/js/app.js?ver=1750061650" type="text/javascript"></script>
<script id="google-recaptcha-js" src="https://www.google.com/recaptcha/api.js?render=6LfrXmMrAAAAAK7eIiKIoc5ai-71rM2uvj5qjQtP&amp;ver=3.0" type="text/javascript"></script>
<script id="wp-hooks-js" src="https://hirumori.co.jp/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" type="text/javascript"></script>
<script id="wp-i18n-js" src="https://hirumori.co.jp/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" type="text/javascript"></script>
<script id="wp-i18n-js-after" type="text/javascript">
/* <![CDATA[ */
wp.i18n.setLocaleData( { 'text direction\u0004ltr': [ 'ltr' ] } );
/* ]]> */
</script>
<script id="snow-monkey-forms@recaptcha-js" src="https://hirumori.co.jp/wp-content/plugins/snow-monkey-forms/dist/js/recaptcha.js?ver=1750061650" type="text/javascript"></script>
<script id="snow-monkey-forms@recaptcha-js-after" type="text/javascript">
/* <![CDATA[ */
var snowmonkeyforms_recaptcha = {"siteKey":"6LfrXmMrAAAAAK7eIiKIoc5ai-71rM2uvj5qjQtP"}
/* ]]> */
</script>
<script id="slick-script-js" src="https://hirumori.co.jp/wp-content/themes/solaris_tcd088/js/slick.min.js?ver=1.0.0" type="text/javascript"></script>
<div><div class="grecaptcha-badge" data-style="bottomright" style="width: 256px; height: 60px; display: block; transition: right 0.3s; position: fixed; bottom: 14px; right: -186px; box-shadow: gray 0px 0px 5px; border-radius: 2px; overflow: hidden;"><div class="grecaptcha-logo"><iframe frameborder="0" height="60" name="a-i1yovuqu8li2" role="presentation" sandbox="allow-forms allow-popups allow-same-origin allow-scripts allow-top-navigation allow-modals allow-popups-to-escape-sandbox allow-storage-access-by-user-activation" scrolling="no" src="https://www.google.com/recaptcha/api2/anchor?ar=1&amp;k=6LfrXmMrAAAAAK7eIiKIoc5ai-71rM2uvj5qjQtP&amp;co=aHR0cHM6Ly9oaXJ1bW9yaS5jby5qcDo0NDM.&amp;hl=zh-CN&amp;v=DBIsSQ0s2djD_akThoRUDeHa&amp;size=invisible&amp;anchor-ms=20000&amp;execute-ms=15000&amp;cb=dxxhhwxakn42" title="reCAPTCHA" width="256"></iframe></div><div class="grecaptcha-error"></div><textarea class="g-recaptcha-response" id="g-recaptcha-response-100000" name="g-recaptcha-response" style="width: 250px; height: 40px; border: 1px solid rgb(193, 193, 193); margin: 10px 25px; padding: 0px; resize: none; display: none;"></textarea></div><iframe style="display: none;"></iframe></div></body></html>