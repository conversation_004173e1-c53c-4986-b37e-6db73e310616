<!DOCTYPE html>
<html class="lenis" lang="ja"><head>
<!-- Google Tag Manager -->
<script async="" src="https://s.yimg.jp/images/listing/tool/cv/ytag.js" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-9HKNHJFSLF&amp;cx=c&amp;gtm=45He5811v846561558za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105087538~105087540~105103161~105103163" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-3JZK7RXJ4Y&amp;cx=c&amp;gtm=45He5811v846561558za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105087538~105087540~105103161~105103163" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=AW-337705360&amp;cx=c&amp;gtm=45He5811v846561558za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105087538~105087540~105103161~105103163" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtag/js?id=G-27BED32N64&amp;cx=c&amp;gtm=45He5811v846561558za200&amp;tag_exp=101509157~103116026~103200004~103233427~104684208~104684211~105087538~105087540~105103161~105103163" type="text/javascript"></script><script async="" src="https://www.googletagmanager.com/gtm.js?id=GTM-NXH7F45"></script><script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-NXH7F45');</script>
<!-- End Google Tag Manager -->
<!-- Google Tag Manager (noscript) -->
<noscript><iframe height="0" src="https://www.googletagmanager.com/ns.html?id=GTM-NXH7F45" style="display:none;visibility:hidden" width="0"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->
<!-- Barba.js用のGTM設定 -->
<script>
        // GTMの自動ページビューを無効化（SPA用）
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            'event': 'gtm.js',
            'send_page_view': false
        });
        </script>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<title>強羅花壇 | GORA KADAN [ブランドサイト]</title>
<meta content="閑院宮別邸に端を発する強羅花壇は、閑院宮載仁親王が自ら創業し、命名された場所。「強羅花壇」という言葉の由来とともに、和の心でもてなすという精神は今なおこの場所の在り方として受け継がれています。" name="description"/>
<link href="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/favicon.ico" rel="icon"/>
<!-- <link rel="apple-touch-icon" sizes="180x180" href="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/apple-touch-icon.png"> -->
<meta content="強羅花壇 | GORA KADAN [ブランドサイト]" property="og:title"/>
<meta content="強羅花壇 | GORA KADAN [ブランドサイト]" property="og:site_name"/>
<meta content="website" property="og:type"/>
<meta content="閑院宮別邸に端を発する強羅花壇は、閑院宮載仁親王が自ら創業し、命名された場所。「強羅花壇」という言葉の由来とともに、和の心でもてなすという精神は今なおこの場所の在り方として受け継がれています。" property="og:description"/>
<meta content="https://www.gorakadan.com/?utm_source=google&amp;utm_medium=maps" property="og:url"/>
<meta content="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/ogp.jpg" property="og:image"/>
<meta content="summary_large_image" name="twitter:card"/>
<meta content="強羅花壇 | GORA KADAN [ブランドサイト]" name="twitter:site"/>
<meta content="強羅花壇 | GORA KADAN [ブランドサイト]" name="twitter:title"/>
<meta content="閑院宮別邸に端を発する強羅花壇は、閑院宮載仁親王が自ら創業し、命名された場所。「強羅花壇」という言葉の由来とともに、和の心でもてなすという精神は今なおこの場所の在り方として受け継がれています。" name="twitter:description"/>
<meta content="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/ogp.jpg" name="twitter:image"/>
<script data-cfasync="false" data-no-defer="1" data-no-minify="1" data-no-optimize="1">var ewww_webp_supported=!1;function check_webp_feature(A,e){var w;e=void 0!==e?e:function(){},ewww_webp_supported?e(ewww_webp_supported):((w=new Image).onload=function(){ewww_webp_supported=0<w.width&&0<w.height,e&&e(ewww_webp_supported)},w.onerror=function(){e&&e(!1)},w.src="data:image/webp;base64,"+{alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA=="}[A])}check_webp_feature("alpha");</script><script data-cfasync="false" data-no-defer="1" data-no-minify="1" data-no-optimize="1">var Arrive=function(c,w){"use strict";if(c.MutationObserver&&"undefined"!=typeof HTMLElement){var r,a=0,u=(r=HTMLElement.prototype.matches||HTMLElement.prototype.webkitMatchesSelector||HTMLElement.prototype.mozMatchesSelector||HTMLElement.prototype.msMatchesSelector,{matchesSelector:function(e,t){return e instanceof HTMLElement&&r.call(e,t)},addMethod:function(e,t,r){var a=e[t];e[t]=function(){return r.length==arguments.length?r.apply(this,arguments):"function"==typeof a?a.apply(this,arguments):void 0}},callCallbacks:function(e,t){t&&t.options.onceOnly&&1==t.firedElems.length&&(e=[e[0]]);for(var r,a=0;r=e[a];a++)r&&r.callback&&r.callback.call(r.elem,r.elem);t&&t.options.onceOnly&&1==t.firedElems.length&&t.me.unbindEventWithSelectorAndCallback.call(t.target,t.selector,t.callback)},checkChildNodesRecursively:function(e,t,r,a){for(var i,n=0;i=e[n];n++)r(i,t,a)&&a.push({callback:t.callback,elem:i}),0<i.childNodes.length&&u.checkChildNodesRecursively(i.childNodes,t,r,a)},mergeArrays:function(e,t){var r,a={};for(r in e)e.hasOwnProperty(r)&&(a[r]=e[r]);for(r in t)t.hasOwnProperty(r)&&(a[r]=t[r]);return a},toElementsArray:function(e){return e=void 0!==e&&("number"!=typeof e.length||e===c)?[e]:e}}),e=(l.prototype.addEvent=function(e,t,r,a){a={target:e,selector:t,options:r,callback:a,firedElems:[]};return this._beforeAdding&&this._beforeAdding(a),this._eventsBucket.push(a),a},l.prototype.removeEvent=function(e){for(var t,r=this._eventsBucket.length-1;t=this._eventsBucket[r];r--)e(t)&&(this._beforeRemoving&&this._beforeRemoving(t),(t=this._eventsBucket.splice(r,1))&&t.length&&(t[0].callback=null))},l.prototype.beforeAdding=function(e){this._beforeAdding=e},l.prototype.beforeRemoving=function(e){this._beforeRemoving=e},l),t=function(i,n){var o=new e,l=this,s={fireOnAttributesModification:!1};return o.beforeAdding(function(t){var e=t.target;e!==c.document&&e!==c||(e=document.getElementsByTagName("html")[0]);var r=new MutationObserver(function(e){n.call(this,e,t)}),a=i(t.options);r.observe(e,a),t.observer=r,t.me=l}),o.beforeRemoving(function(e){e.observer.disconnect()}),this.bindEvent=function(e,t,r){t=u.mergeArrays(s,t);for(var a=u.toElementsArray(this),i=0;i<a.length;i++)o.addEvent(a[i],e,t,r)},this.unbindEvent=function(){var r=u.toElementsArray(this);o.removeEvent(function(e){for(var t=0;t<r.length;t++)if(this===w||e.target===r[t])return!0;return!1})},this.unbindEventWithSelectorOrCallback=function(r){var a=u.toElementsArray(this),i=r,e="function"==typeof r?function(e){for(var t=0;t<a.length;t++)if((this===w||e.target===a[t])&&e.callback===i)return!0;return!1}:function(e){for(var t=0;t<a.length;t++)if((this===w||e.target===a[t])&&e.selector===r)return!0;return!1};o.removeEvent(e)},this.unbindEventWithSelectorAndCallback=function(r,a){var i=u.toElementsArray(this);o.removeEvent(function(e){for(var t=0;t<i.length;t++)if((this===w||e.target===i[t])&&e.selector===r&&e.callback===a)return!0;return!1})},this},i=new function(){var s={fireOnAttributesModification:!1,onceOnly:!1,existing:!1};function n(e,t,r){return!(!u.matchesSelector(e,t.selector)||(e._id===w&&(e._id=a++),-1!=t.firedElems.indexOf(e._id)))&&(t.firedElems.push(e._id),!0)}var c=(i=new t(function(e){var t={attributes:!1,childList:!0,subtree:!0};return e.fireOnAttributesModification&&(t.attributes=!0),t},function(e,i){e.forEach(function(e){var t=e.addedNodes,r=e.target,a=[];null!==t&&0<t.length?u.checkChildNodesRecursively(t,i,n,a):"attributes"===e.type&&n(r,i)&&a.push({callback:i.callback,elem:r}),u.callCallbacks(a,i)})})).bindEvent;return i.bindEvent=function(e,t,r){t=void 0===r?(r=t,s):u.mergeArrays(s,t);var a=u.toElementsArray(this);if(t.existing){for(var i=[],n=0;n<a.length;n++)for(var o=a[n].querySelectorAll(e),l=0;l<o.length;l++)i.push({callback:r,elem:o[l]});if(t.onceOnly&&i.length)return r.call(i[0].elem,i[0].elem);setTimeout(u.callCallbacks,1,i)}c.call(this,e,t,r)},i},o=new function(){var a={};function i(e,t){return u.matchesSelector(e,t.selector)}var n=(o=new t(function(){return{childList:!0,subtree:!0}},function(e,r){e.forEach(function(e){var t=e.removedNodes,e=[];null!==t&&0<t.length&&u.checkChildNodesRecursively(t,r,i,e),u.callCallbacks(e,r)})})).bindEvent;return o.bindEvent=function(e,t,r){t=void 0===r?(r=t,a):u.mergeArrays(a,t),n.call(this,e,t,r)},o};d(HTMLElement.prototype),d(NodeList.prototype),d(HTMLCollection.prototype),d(HTMLDocument.prototype),d(Window.prototype);var n={};return s(i,n,"unbindAllArrive"),s(o,n,"unbindAllLeave"),n}function l(){this._eventsBucket=[],this._beforeAdding=null,this._beforeRemoving=null}function s(e,t,r){u.addMethod(t,r,e.unbindEvent),u.addMethod(t,r,e.unbindEventWithSelectorOrCallback),u.addMethod(t,r,e.unbindEventWithSelectorAndCallback)}function d(e){e.arrive=i.bindEvent,s(i,e,"unbindArrive"),e.leave=o.bindEvent,s(o,e,"unbindLeave")}}(window,void 0),ewww_webp_supported=!1;function check_webp_feature(e,t){var r;ewww_webp_supported?t(ewww_webp_supported):((r=new Image).onload=function(){ewww_webp_supported=0<r.width&&0<r.height,t(ewww_webp_supported)},r.onerror=function(){t(!1)},r.src="data:image/webp;base64,"+{alpha:"UklGRkoAAABXRUJQVlA4WAoAAAAQAAAAAAAAAAAAQUxQSAwAAAARBxAR/Q9ERP8DAABWUDggGAAAABQBAJ0BKgEAAQAAAP4AAA3AAP7mtQAAAA==",animation:"UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA"}[e])}function ewwwLoadImages(e){if(e){for(var t=document.querySelectorAll(".batch-image img, .image-wrapper a, .ngg-pro-masonry-item a, .ngg-galleria-offscreen-seo-wrapper a"),r=0,a=t.length;r<a;r++)ewwwAttr(t[r],"data-src",t[r].getAttribute("data-webp")),ewwwAttr(t[r],"data-thumbnail",t[r].getAttribute("data-webp-thumbnail"));for(var i=document.querySelectorAll("div.woocommerce-product-gallery__image"),r=0,a=i.length;r<a;r++)ewwwAttr(i[r],"data-thumb",i[r].getAttribute("data-webp-thumb"))}for(var n=document.querySelectorAll("video"),r=0,a=n.length;r<a;r++)ewwwAttr(n[r],"poster",e?n[r].getAttribute("data-poster-webp"):n[r].getAttribute("data-poster-image"));for(var o,l=document.querySelectorAll("img.ewww_webp_lazy_load"),r=0,a=l.length;r<a;r++)e&&(ewwwAttr(l[r],"data-lazy-srcset",l[r].getAttribute("data-lazy-srcset-webp")),ewwwAttr(l[r],"data-srcset",l[r].getAttribute("data-srcset-webp")),ewwwAttr(l[r],"data-lazy-src",l[r].getAttribute("data-lazy-src-webp")),ewwwAttr(l[r],"data-src",l[r].getAttribute("data-src-webp")),ewwwAttr(l[r],"data-orig-file",l[r].getAttribute("data-webp-orig-file")),ewwwAttr(l[r],"data-medium-file",l[r].getAttribute("data-webp-medium-file")),ewwwAttr(l[r],"data-large-file",l[r].getAttribute("data-webp-large-file")),null!=(o=l[r].getAttribute("srcset"))&&!1!==o&&o.includes("R0lGOD")&&ewwwAttr(l[r],"src",l[r].getAttribute("data-lazy-src-webp"))),l[r].className=l[r].className.replace(/\bewww_webp_lazy_load\b/,"");for(var s=document.querySelectorAll(".ewww_webp"),r=0,a=s.length;r<a;r++)e?(ewwwAttr(s[r],"srcset",s[r].getAttribute("data-srcset-webp")),ewwwAttr(s[r],"src",s[r].getAttribute("data-src-webp")),ewwwAttr(s[r],"data-orig-file",s[r].getAttribute("data-webp-orig-file")),ewwwAttr(s[r],"data-medium-file",s[r].getAttribute("data-webp-medium-file")),ewwwAttr(s[r],"data-large-file",s[r].getAttribute("data-webp-large-file")),ewwwAttr(s[r],"data-large_image",s[r].getAttribute("data-webp-large_image")),ewwwAttr(s[r],"data-src",s[r].getAttribute("data-webp-src"))):(ewwwAttr(s[r],"srcset",s[r].getAttribute("data-srcset-img")),ewwwAttr(s[r],"src",s[r].getAttribute("data-src-img"))),s[r].className=s[r].className.replace(/\bewww_webp\b/,"ewww_webp_loaded");window.jQuery&&jQuery.fn.isotope&&jQuery.fn.imagesLoaded&&(jQuery(".fusion-posts-container-infinite").imagesLoaded(function(){jQuery(".fusion-posts-container-infinite").hasClass("isotope")&&jQuery(".fusion-posts-container-infinite").isotope()}),jQuery(".fusion-portfolio:not(.fusion-recent-works) .fusion-portfolio-wrapper").imagesLoaded(function(){jQuery(".fusion-portfolio:not(.fusion-recent-works) .fusion-portfolio-wrapper").isotope()}))}function ewwwWebPInit(e){ewwwLoadImages(e),ewwwNggLoadGalleries(e),document.arrive(".ewww_webp",function(){ewwwLoadImages(e)}),document.arrive(".ewww_webp_lazy_load",function(){ewwwLoadImages(e)}),document.arrive("videos",function(){ewwwLoadImages(e)}),"loading"==document.readyState?document.addEventListener("DOMContentLoaded",ewwwJSONParserInit):("undefined"!=typeof galleries&&ewwwNggParseGalleries(e),ewwwWooParseVariations(e))}function ewwwAttr(e,t,r){null!=r&&!1!==r&&e.setAttribute(t,r)}function ewwwJSONParserInit(){"undefined"!=typeof galleries&&check_webp_feature("alpha",ewwwNggParseGalleries),check_webp_feature("alpha",ewwwWooParseVariations)}function ewwwWooParseVariations(e){if(e)for(var t=document.querySelectorAll("form.variations_form"),r=0,a=t.length;r<a;r++){var i=t[r].getAttribute("data-product_variations"),n=!1;try{for(var o in i=JSON.parse(i))void 0!==i[o]&&void 0!==i[o].image&&(void 0!==i[o].image.src_webp&&(i[o].image.src=i[o].image.src_webp,n=!0),void 0!==i[o].image.srcset_webp&&(i[o].image.srcset=i[o].image.srcset_webp,n=!0),void 0!==i[o].image.full_src_webp&&(i[o].image.full_src=i[o].image.full_src_webp,n=!0),void 0!==i[o].image.gallery_thumbnail_src_webp&&(i[o].image.gallery_thumbnail_src=i[o].image.gallery_thumbnail_src_webp,n=!0),void 0!==i[o].image.thumb_src_webp&&(i[o].image.thumb_src=i[o].image.thumb_src_webp,n=!0));n&&ewwwAttr(t[r],"data-product_variations",JSON.stringify(i))}catch(e){}}}function ewwwNggParseGalleries(e){if(e)for(var t in galleries){var r=galleries[t];galleries[t].images_list=ewwwNggParseImageList(r.images_list)}}function ewwwNggLoadGalleries(e){e&&document.addEventListener("ngg.galleria.themeadded",function(e,t){window.ngg_galleria._create_backup=window.ngg_galleria.create,window.ngg_galleria.create=function(e,t){var r=$(e).data("id");return galleries["gallery_"+r].images_list=ewwwNggParseImageList(galleries["gallery_"+r].images_list),window.ngg_galleria._create_backup(e,t)}})}function ewwwNggParseImageList(e){for(var t in e){var r=e[t];if(void 0!==r["image-webp"]&&(e[t].image=r["image-webp"],delete e[t]["image-webp"]),void 0!==r["thumb-webp"]&&(e[t].thumb=r["thumb-webp"],delete e[t]["thumb-webp"]),void 0!==r.full_image_webp&&(e[t].full_image=r.full_image_webp,delete e[t].full_image_webp),void 0!==r.srcsets)for(var a in r.srcsets)nggSrcset=r.srcsets[a],void 0!==r.srcsets[a+"-webp"]&&(e[t].srcsets[a]=r.srcsets[a+"-webp"],delete e[t].srcsets[a+"-webp"]);if(void 0!==r.full_srcsets)for(var i in r.full_srcsets)nggFSrcset=r.full_srcsets[i],void 0!==r.full_srcsets[i+"-webp"]&&(e[t].full_srcsets[i]=r.full_srcsets[i+"-webp"],delete e[t].full_srcsets[i+"-webp"])}return e}check_webp_feature("alpha",ewwwWebPInit);</script><meta content="max-image-preview:large" name="robots"/>
<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
<link href="https://www.gorakadan.com/en/" hreflang="en" rel="alternate"/>
<link href="https://www.gorakadan.com/" hreflang="ja" rel="alternate"/>
<link href="https://www.gorakadan.com/" hreflang="x-default" rel="alternate"/>
<link href="https://www.gorakadan.com/wp-includes/css/dist/block-library/style.min.css?ver=6.8.2" id="wp-block-library-css" media="all" rel="stylesheet" type="text/css"/>
<style id="classic-theme-styles-inline-css" type="text/css">
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<style id="global-styles-inline-css" type="text/css">
:root{--wp--preset--aspect-ratio--square: 1;--wp--preset--aspect-ratio--4-3: 4/3;--wp--preset--aspect-ratio--3-4: 3/4;--wp--preset--aspect-ratio--3-2: 3/2;--wp--preset--aspect-ratio--2-3: 2/3;--wp--preset--aspect-ratio--16-9: 16/9;--wp--preset--aspect-ratio--9-16: 9/16;--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flex{display: flex;}.is-layout-flex{flex-wrap: wrap;align-items: center;}.is-layout-flex > :is(*, div){margin: 0;}body .is-layout-grid{display: grid;}.is-layout-grid > :is(*, div){margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
:root :where(.wp-block-pullquote){font-size: 1.5em;line-height: 1.6;}
</style>
<link href="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/css/app.css?ver=1754400140" id="theme-styles-css" media="all" rel="stylesheet" type="text/css"/>
<link href="https://www.gorakadan.com/wp-includes/css/dashicons.min.css?ver=6.8.2" id="dashicons-css" media="all" rel="stylesheet" type="text/css"/>
<script id="wpml-cookie-js-extra" type="text/javascript">
/* <![CDATA[ */
var wpml_cookies = {"wp-wpml_current_language":{"value":"ja","expires":1,"path":"\/"}};
var wpml_cookies = {"wp-wpml_current_language":{"value":"ja","expires":1,"path":"\/"}};
/* ]]> */
</script>
<script data-wp-strategy="defer" defer="defer" id="wpml-cookie-js" src="https://www.gorakadan.com/wp-content/plugins/sitepress-multilingual-cms/res/js/cookies/language-cookie.js?ver=475000" type="text/javascript"></script>
<link href="https://www.gorakadan.com/wp-json/" rel="https://api.w.org/"/><link href="https://www.gorakadan.com/wp-json/wp/v2/pages/13" rel="alternate" title="JSON" type="application/json"/><link href="https://www.gorakadan.com/" rel="canonical"/>
<link href="https://www.gorakadan.com/" rel="shortlink"/>
<meta content="WPML ver:4.7.5 stt:1,28;" name="generator"/>
<meta content="cwcqTEQZx-zHJuIk5jqVT-wwL4tkpHNfZZdG8SX6Bzg" name="google-site-verification"/>
<script async="" src="https://googleads.g.doubleclick.net/pagead/viewthroughconversion/337705360/?random=1754400160688&amp;cv=11&amp;fst=1754400160688&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5811v879190198z8846561558za200zb846561558zd846561558xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527906~104528500~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Fwww.gorakadan.com%2F%3Futm_source%3Dgoogle%26utm_medium%3Dmaps&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E5%BC%B7%E7%BE%85%E8%8A%B1%E5%A3%87%20%7C%20GORA%20KADAN%20%5B%E3%83%96%E3%83%A9%E3%83%B3%E3%83%89%E3%82%B5%E3%82%A4%E3%83%88%5D&amp;npa=0&amp;pscdl=noapi&amp;auid=1447106691.**********&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config&amp;rfmt=3&amp;fmt=4" type="text/javascript"></script><style>/**
 * Swiper 11.2.6
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: March 19, 2025
 */

/* FONT_START */
@font-face {
  font-family: 'swiper-icons';
  src: url("data:application/font-woff;charset=utf-8;base64, d09GRgABAAAAAAZgABAAAAAADAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABGRlRNAAAGRAAAABoAAAAci6qHkUdERUYAAAWgAAAAIwAAACQAYABXR1BPUwAABhQAAAAuAAAANuAY7+xHU1VCAAAFxAAAAFAAAABm2fPczU9TLzIAAAHcAAAASgAAAGBP9V5RY21hcAAAAkQAAACIAAABYt6F0cBjdnQgAAACzAAAAAQAAAAEABEBRGdhc3AAAAWYAAAACAAAAAj//wADZ2x5ZgAAAywAAADMAAAD2MHtryVoZWFkAAABbAAAADAAAAA2E2+eoWhoZWEAAAGcAAAAHwAAACQC9gDzaG10eAAAAigAAAAZAAAArgJkABFsb2NhAAAC0AAAAFoAAABaFQAUGG1heHAAAAG8AAAAHwAAACAAcABAbmFtZQAAA/gAAAE5AAACXvFdBwlwb3N0AAAFNAAAAGIAAACE5s74hXjaY2BkYGAAYpf5Hu/j+W2+MnAzMYDAzaX6QjD6/4//Bxj5GA8AuRwMYGkAPywL13jaY2BkYGA88P8Agx4j+/8fQDYfA1AEBWgDAIB2BOoAeNpjYGRgYNBh4GdgYgABEMnIABJzYNADCQAACWgAsQB42mNgYfzCOIGBlYGB0YcxjYGBwR1Kf2WQZGhhYGBiYGVmgAFGBiQQkOaawtDAoMBQxXjg/wEGPcYDDA4wNUA2CCgwsAAAO4EL6gAAeNpj2M0gyAACqxgGNWBkZ2D4/wMA+xkDdgAAAHjaY2BgYGaAYBkGRgYQiAHyGMF8FgYHIM3DwMHABGQrMOgyWDLEM1T9/w8UBfEMgLzE////P/5//f/V/xv+r4eaAAeMbAxwIUYmIMHEgKYAYjUcsDAwsLKxc3BycfPw8jEQA/gZBASFhEVExcQlJKWkZWTl5BUUlZRVVNXUNTQZBgMAAMR+E+gAEQFEAAAAKgAqACoANAA+AEgAUgBcAGYAcAB6AIQAjgCYAKIArAC2AMAAygDUAN4A6ADyAPwBBgEQARoBJAEuATgBQgFMAVYBYAFqAXQBfgGIAZIBnAGmAbIBzgHsAAB42u2NMQ6CUAyGW568x9AneYYgm4MJbhKFaExIOAVX8ApewSt4Bic4AfeAid3VOBixDxfPYEza5O+Xfi04YADggiUIULCuEJK8VhO4bSvpdnktHI5QCYtdi2sl8ZnXaHlqUrNKzdKcT8cjlq+rwZSvIVczNiezsfnP/uznmfPFBNODM2K7MTQ45YEAZqGP81AmGGcF3iPqOop0r1SPTaTbVkfUe4HXj97wYE+yNwWYxwWu4v1ugWHgo3S1XdZEVqWM7ET0cfnLGxWfkgR42o2PvWrDMBSFj/IHLaF0zKjRgdiVMwScNRAoWUoH78Y2icB/yIY09An6AH2Bdu/UB+yxopYshQiEvnvu0dURgDt8QeC8PDw7Fpji3fEA4z/PEJ6YOB5hKh4dj3EvXhxPqH/SKUY3rJ7srZ4FZnh1PMAtPhwP6fl2PMJMPDgeQ4rY8YT6Gzao0eAEA409DuggmTnFnOcSCiEiLMgxCiTI6Cq5DZUd3Qmp10vO0LaLTd2cjN4fOumlc7lUYbSQcZFkutRG7g6JKZKy0RmdLY680CDnEJ+UMkpFFe1RN7nxdVpXrC4aTtnaurOnYercZg2YVmLN/d/gczfEimrE/fs/bOuq29Zmn8tloORaXgZgGa78yO9/cnXm2BpaGvq25Dv9S4E9+5SIc9PqupJKhYFSSl47+Qcr1mYNAAAAeNptw0cKwkAAAMDZJA8Q7OUJvkLsPfZ6zFVERPy8qHh2YER+3i/BP83vIBLLySsoKimrqKqpa2hp6+jq6RsYGhmbmJqZSy0sraxtbO3sHRydnEMU4uR6yx7JJXveP7WrDycAAAAAAAH//wACeNpjYGRgYOABYhkgZgJCZgZNBkYGLQZtIJsFLMYAAAw3ALgAeNolizEKgDAQBCchRbC2sFER0YD6qVQiBCv/H9ezGI6Z5XBAw8CBK/m5iQQVauVbXLnOrMZv2oLdKFa8Pjuru2hJzGabmOSLzNMzvutpB3N42mNgZGBg4GKQYzBhYMxJLMlj4GBgAYow/P/PAJJhLM6sSoWKfWCAAwDAjgbRAAB42mNgYGBkAIIbCZo5IPrmUn0hGA0AO8EFTQAA");
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}
:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}
.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}
.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}
.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(--swiper-wrapper-transition-timing-function, initial);
  box-sizing: content-box;
}
.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}
.swiper-horizontal {
  touch-action: pan-y;
}
.swiper-vertical {
  touch-action: pan-x;
}
.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}
.swiper-slide-invisible-blank {
  visibility: hidden;
}
/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}
.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}
.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}
.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}
.swiper-3d {
  perspective: 1200px;
}
.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}
/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}
.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}
.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}
.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}
.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}
.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: '';
  flex-shrink: 0;
  order: 9999;
}
.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper > .swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper > .swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}
.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}
/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}
.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}
.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
}
.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}
.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}
.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}
.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}
@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */
.swiper-virtual .swiper-slide {
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
}
.swiper-virtual.swiper-css-mode .swiper-wrapper::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}
.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after {
  height: 1px;
  width: var(--swiper-virtual-size);
}
.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after {
  width: 1px;
  height: var(--swiper-virtual-size);
}
:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}
.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - (var(--swiper-navigation-size) / 2));
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}
.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}
.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}
.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}
.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform-origin: center;
}
.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}
.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-lock {
  display: none;
}
/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: 'prev';
}
.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: 'next';
}
/* Navigation font end */
:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}
.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}
.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}
.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}
/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}
/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}
.swiper-pagination-bullet {
  width: var(--swiper-pagination-bullet-width, var(--swiper-pagination-bullet-size, 8px));
  height: var(--swiper-pagination-bullet-height, var(--swiper-pagination-bullet-size, 8px));
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}
button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
          appearance: none;
}
.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}
.swiper-pagination-bullet:only-child {
  display: none !important;
}
.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}
.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}
.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}
.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform,
        200ms top;
}
.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}
.swiper-horizontal > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
        200ms left;
}
.swiper-horizontal.swiper-rtl > .swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transition: 200ms transform,
    200ms right;
}
/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}
/* Progress */
.swiper-pagination-progressbar {
  background: var(--swiper-pagination-progressbar-bg-color, rgba(0, 0, 0, 0.25));
  position: absolute;
}
.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}
.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}
.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}
.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}
.swiper-pagination-lock {
  display: none;
}
:root {
  /*
  --swiper-scrollbar-border-radius: 10px;
  --swiper-scrollbar-top: auto;
  --swiper-scrollbar-bottom: 4px;
  --swiper-scrollbar-left: auto;
  --swiper-scrollbar-right: 4px;
  --swiper-scrollbar-sides-offset: 1%;
  --swiper-scrollbar-bg-color: rgba(0, 0, 0, 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(0, 0, 0, 0.5);
  --swiper-scrollbar-size: 4px;
  */
}
.swiper-scrollbar {
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  position: relative;
  touch-action: none;
  background: var(--swiper-scrollbar-bg-color, rgba(0, 0, 0, 0.1));
}
.swiper-scrollbar-disabled > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-disabled {
  display: none !important;
}
.swiper-horizontal > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-horizontal {
  position: absolute;
  left: var(--swiper-scrollbar-sides-offset, 1%);
  bottom: var(--swiper-scrollbar-bottom, 4px);
  top: var(--swiper-scrollbar-top, auto);
  z-index: 50;
  height: var(--swiper-scrollbar-size, 4px);
  width: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}
.swiper-vertical > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-vertical {
  position: absolute;
  left: var(--swiper-scrollbar-left, auto);
  right: var(--swiper-scrollbar-right, 4px);
  top: var(--swiper-scrollbar-sides-offset, 1%);
  z-index: 50;
  width: var(--swiper-scrollbar-size, 4px);
  height: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}
.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--swiper-scrollbar-drag-bg-color, rgba(0, 0, 0, 0.5));
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  left: 0;
  top: 0;
}
.swiper-scrollbar-cursor-drag {
  cursor: move;
}
.swiper-scrollbar-lock {
  display: none;
}
/* Zoom container styles start */
.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.swiper-zoom-container > img,
.swiper-zoom-container > svg,
.swiper-zoom-container > canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
/* Zoom container styles end */
.swiper-slide-zoomed {
  cursor: move;
  touch-action: none;
}
/* a11y */
.swiper .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}
.swiper-free-mode > .swiper-wrapper {
  transition-timing-function: ease-out;
  margin: 0 auto;
}
.swiper-grid > .swiper-wrapper {
  flex-wrap: wrap;
}
.swiper-grid-column > .swiper-wrapper {
  flex-wrap: wrap;
  flex-direction: column;
}
.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}
.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}
.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper.swiper-cube {
  overflow: visible;
}
.swiper-cube .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}
.swiper-cube .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-cube.swiper-rtl .swiper-slide {
  transform-origin: 100% 0;
}
.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-next,
.swiper-cube .swiper-slide-prev {
  pointer-events: auto;
  visibility: visible;
}
.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
}
.swiper-cube .swiper-cube-shadow:before {
  content: '';
  background: #000;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px);
}
.swiper-cube .swiper-slide-next + .swiper-slide {
  pointer-events: auto;
  visibility: visible;
}
/* Cube slide shadows start */
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* Cube slide shadows end */
.swiper.swiper-flip {
  overflow: visible;
}
.swiper-flip .swiper-slide {
  pointer-events: none;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  z-index: 1;
}
.swiper-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-flip .swiper-slide-active,
.swiper-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
/* Flip slide shadows start */
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right {
  z-index: 0;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
/* Flip slide shadows end */
.swiper-creative .swiper-slide {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  overflow: hidden;
  transition-property: transform, opacity, height;
}
.swiper.swiper-cards {
  overflow: visible;
}
.swiper-cards .swiper-slide {
  transform-origin: center bottom;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  overflow: hidden;
}
</style><style>.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}
.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}
.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}
.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}
.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}
</style></head>
<body class="home wp-singular page-template-default page page-id-13 wp-theme-gorakadan-brand webp-support" data-barba="wrapper" style="padding-right: 0px;">
<script data-cfasync="false" data-no-defer="1" data-no-minify="1" data-no-optimize="1">if(typeof ewww_webp_supported==="undefined"){var ewww_webp_supported=!1}if(ewww_webp_supported){document.body.classList.add("webp-support")}</script>
<div class="js_menu menu fixed top-0 left-0 w-full h-full z-50" id="menu">
<div class="menu__bg absolute top-0 left-0 w-full h-full bg-[#edf0e8] z-10"></div>
<div class="menu__content relative grid grid-cols-1 h-full pt-[110px] px-[42px] pb-[32px] overflow-y-scroll z-20 lg:grid-cols-[9fr_16fr] lg:py-0 lg:pl-more-30 lg:pr-more-60">
<div class="lg:relative lg:col-start-2 lg:flex lg:justify-center lg:flex-col lg:pl-[28%]">
<div class="grid grid-cols-1 justify-items-start gap-y-[40px] w-full lg:gap-y-more-60">
<a class="inline-block text-more-17 font-abc lg:text-more-20" href="https://www.gorakadan.com">Home</a>
<div class="grid grid-cols-1 gap-y-[4px] w-full lg:gap-y-more-12">
<h4 class="font-abc text-more-13">強羅花壇での時間</h4>
<div class="grid grid-cols-1 gap-y-[24px] lg:grid-cols-[auto_33%] lg:items-center">
<h2 class="font-abc text-more-28 leading-none text-[var(--c-txt-black-01)] mt-[0.4em] mr-auto flex items-center gap-x-[0.5em] lg:text-more-30 lg:mt-0">
<span class="tracking-wider">PASSAGE</span><span class="tracking-wider">OF</span><span class="tracking-wider">TIME</span>
</h2>
<a class="btn-black -bg-w w-[44%] ml-auto lg:w-full" href="https://www.gorakadan.com/passage-of-time">
<span class="btn-black__bg"></span>
<span class="btn-black__text" data-name="btn-blue-text"><div style="position:relative;display:inline-block;">コ</div><div style="position:relative;display:inline-block;">ラ</div><div style="position:relative;display:inline-block;">ム</div><div style="position:relative;display:inline-block;">を</div><div style="position:relative;display:inline-block;">読</div><div style="position:relative;display:inline-block;">む</div></span>
</a>
</div>
</div>
</div>
<div class="relative grid grid-cols-1 gap-y-[12px] mt-[45px] pt-[45px] lg:mt-more-50 lg:pt-more-50 lg:gap-y-more-30">
<div class="absolute top-0 left-0 w-full h-[1px] bg-gray01 opacity-50"></div>
<h4 class="font-abc text-more-13">強羅花壇の空間</h4>
<div class="grid grid-cols-1 gap-y-[32px] lg:gap-y-more-50">
<div class="grid grid-cols-[auto_44%] items-start mt-[16px] lg:grid-cols-[auto_33%]">
<div class="grid grid-cols-1 justify-start gap-y-[16px] lg:gap-y-[8px]">
<div class="w-[80%] lg:w-[38%]">
<svg class="" fill="none" height="23" viewbox="0 0 129 23" width="129" xmlns="http://www.w3.org/2000/svg">
<path d="M33.4825 22.7473C33.3394 22.7635 33.1837 22.6958 33.0502 22.5563C32.94 22.4408 32.8208 22.3342 32.7017 22.2288C32.0472 21.6545 31.855 20.9617 32.125 20.1425C32.2496 19.7659 32.1394 19.6018 31.7358 19.5886C31.4274 19.5784 31.1178 19.5778 30.8095 19.588C30.5843 19.5958 30.3753 19.5479 30.1687 19.4695C29.813 19.3347 29.6544 19.0551 29.7238 18.6718C29.8568 17.9413 30.137 17.3173 30.8352 16.9329C31.0981 16.788 31.3119 16.5431 31.5226 16.3191C31.7274 16.1023 31.6789 15.9862 31.3903 15.9173C30.9262 15.8077 30.4604 15.7041 29.9981 15.5862C29.7508 15.5233 29.4447 15.4844 29.4034 15.1718C29.3519 14.7814 29.2873 14.3442 29.6519 14.0592C30.4298 13.4514 31.0723 12.6909 31.867 12.1077C32.258 11.8203 32.631 11.5077 33.0586 11.2658C33.5975 10.9604 34.6442 11.1328 35.079 11.7089C35.4712 12.2299 35.9371 12.6975 36.3017 13.2472C36.397 13.3915 36.555 13.4975 36.6934 13.6095C36.861 13.7448 37.0018 13.6502 37.0425 13.4939C37.1496 13.0825 37.4472 12.858 37.7682 12.6287C38.0772 12.4077 38.2862 12.0789 38.5454 11.8041C38.87 11.4604 39.2377 11.1807 39.6545 10.9562C39.8934 10.8274 40.1263 10.7568 40.3503 10.9358C40.5886 11.125 40.773 11.3676 40.7628 11.6927C40.7569 11.8795 40.6934 12.0646 40.682 12.252C40.6665 12.5011 40.5569 12.6897 40.3676 12.837C40.061 13.0754 39.9628 13.4143 39.9024 13.773C39.8814 13.8963 39.9083 14.0293 39.9868 14.1095C40.0874 14.2125 40.1826 14.0861 40.2736 14.0383C40.5551 13.8909 40.8581 13.8999 41.1491 13.9604C41.4263 14.0179 41.5563 13.94 41.6173 13.6652C41.7707 12.9694 42.1395 12.4059 42.7102 11.976C43.0102 11.7508 43.1288 11.7813 43.2886 12.1227C43.3186 12.1855 43.3371 12.2652 43.3868 12.3059C43.7377 12.5933 43.8449 12.9885 43.9 13.4119C43.9282 13.6299 44.0252 13.6873 44.2677 13.6562C44.7156 13.5987 45.1324 13.4287 45.5737 13.3568C45.9288 13.2993 46.1054 13.4233 46.1396 13.7843C46.1695 14.0993 46.0928 14.4005 45.9737 14.6862C45.8827 14.9059 45.7677 15.1179 45.6432 15.3209C45.4917 15.5676 45.2473 15.6802 44.9653 15.6892C44.727 15.6969 44.6959 15.8095 44.7725 16C44.8204 16.1197 44.8659 16.2407 44.9168 16.3592C45.0282 16.6209 44.9449 16.7754 44.6563 16.7652C44.3623 16.755 44.1084 16.8365 43.8647 16.9832C43.6174 17.1317 43.5898 17.2838 43.7593 17.5227C43.8934 17.7113 44.1114 17.8557 44.0749 18.1371C44.0563 18.282 44.0174 18.403 43.8988 18.4988C43.4533 18.8605 43.0114 19.2251 42.6335 19.6623C42.4156 19.9144 42.0192 19.8814 41.8874 19.5647C41.6814 19.0706 41.3149 18.706 40.9862 18.3132C40.764 18.0479 40.794 17.8143 40.9371 17.5503C40.9748 17.4802 41.0174 17.4125 41.0557 17.3431C41.2976 16.903 41.2569 16.791 40.7934 16.6173C40.4868 16.5023 40.4443 16.3389 40.6557 16.0928C40.7245 16.0125 40.8389 15.9491 40.776 15.8155C40.7191 15.6958 40.6042 15.6898 40.4928 15.6946C40.3179 15.7017 40.1581 15.6658 40.009 15.5724C39.8545 15.4748 39.7646 15.5437 39.7257 15.7011C39.6395 16.0485 39.5407 16.394 39.4796 16.7455C39.4245 17.0616 39.585 17.3515 39.6742 17.6419C39.8066 18.073 39.8072 18.4802 39.6359 18.894C39.4802 19.2694 39.4634 19.697 39.2227 20.0395C38.9712 20.397 38.7425 20.7623 38.5862 21.1761C38.5131 21.3701 38.282 21.3665 38.1185 21.1904C37.894 20.9491 37.7921 20.6443 37.7209 20.3323C37.6395 19.9736 37.5688 19.6138 37.5694 19.2407C37.57 18.8736 37.3658 18.7575 37.0736 18.9659C36.6419 19.2742 36.1981 19.4234 35.6514 19.3449C35.1346 19.2707 34.6275 19.4413 34.1286 19.5814C33.8855 19.6497 33.8412 19.8659 33.852 20.0581C33.8789 20.5132 33.9197 20.9737 34.2286 21.3491C34.5041 21.6826 34.3855 22.024 34.2448 22.3581C34.1137 22.6689 33.8394 22.7443 33.4825 22.7473ZM33.3963 18.4251C33.9382 18.4928 34.3843 18.27 34.8221 18.0335C34.9969 17.9395 35.0412 17.8185 34.9005 17.6227C34.6682 17.3 34.352 17.0742 34.0604 16.8209C33.9221 16.7012 33.746 16.6982 33.558 16.788C33.0712 17.0209 32.6741 17.3539 32.337 17.77C32.1232 18.0335 32.1646 18.212 32.5017 18.3042C32.7981 18.3862 33.1011 18.4515 33.3963 18.4251ZM37.6832 14.7329C37.5449 15.2431 37.146 15.4916 36.7658 15.7628C36.5496 15.9167 36.3317 15.9736 36.0664 15.9281C35.8634 15.8928 35.6472 15.8898 35.4484 15.9736C35.252 16.0556 35.2364 16.1994 35.4053 16.3305C35.4592 16.3724 35.5275 16.397 35.5832 16.4377C36.173 16.8682 36.9365 17.0179 37.4395 17.591C37.5437 17.7101 37.6173 17.6359 37.6461 17.5179C37.7221 17.1994 37.7928 16.882 37.7473 16.5473C37.6994 16.1946 37.7059 15.8389 37.7449 15.4838C37.7724 15.2341 37.8018 14.9808 37.6832 14.7329ZM34.1808 14.4472C34.3328 14.4664 34.4107 14.4251 34.4712 14.3538C34.5472 14.264 34.4838 14.182 34.4364 14.1107C34.2592 13.8442 34.0478 13.6095 33.7652 13.4496C33.6412 13.3802 33.5089 13.3305 33.3676 13.3897C33.0754 13.5125 32.7735 13.6233 32.5412 13.8502C32.4191 13.9694 32.2017 14.0778 32.2861 14.2778C32.3694 14.4754 32.6233 14.4706 32.7891 14.4454C33.2748 14.3724 33.7532 14.3293 34.1808 14.4472Z" fill="#111111"></path>
<path d="M30.2272 8.25834C30.2266 7.35354 30.223 6.44815 30.229 5.54334C30.2308 5.19722 30.1925 4.85352 30.2003 4.50441C30.2123 3.98464 30.5194 3.76548 30.8955 3.56128C31.0524 3.47565 31.2177 3.40679 31.3787 3.32894C31.7853 3.13195 32.171 3.34093 32.5638 3.39243C32.6889 3.40858 32.8045 3.48943 32.9296 3.51696C33.1716 3.56967 33.1548 3.7104 33.0566 3.86968C32.9422 4.05591 32.8326 4.25172 32.6835 4.408C32.2865 4.82297 32.2973 5.314 32.353 5.83198C32.4183 6.44336 32.5254 7.05054 32.5266 7.66792C32.5272 8.19966 32.9704 8.5859 33.4961 8.53199C33.935 8.48709 34.3249 8.24337 34.7662 8.21883C35.2943 8.18948 35.8015 7.97511 36.3255 7.99067C36.8698 8.00685 37.3932 7.95714 37.9165 7.82421C38.4052 7.69966 38.9094 7.65355 39.4034 7.54577C40.0728 7.39965 40.7645 7.4296 41.4501 7.46493C41.7285 7.47929 42.0082 7.4745 42.2872 7.4715C42.5681 7.46792 42.7603 7.33977 42.837 7.05953C42.9812 6.53197 43.0992 5.99784 43.1866 5.45831C43.2387 5.13555 43.1316 5.02298 42.7968 5.03915C42.2322 5.06609 41.6681 5.03855 41.1028 5.14814C40.7171 5.22299 40.4878 5.39963 40.371 5.74814C40.2393 6.14156 39.9627 6.40084 39.6159 6.59904C39.3381 6.75773 39.1148 6.7248 38.8872 6.50264C38.7082 6.32719 38.5405 6.14156 38.4423 5.90922C38.35 5.69185 38.2111 5.58286 37.9638 5.58586C37.6878 5.58946 37.4477 5.64693 37.2943 5.89245C37.2375 5.98347 37.1956 6.09125 37.177 6.19664C37.098 6.63796 36.8009 6.93379 36.498 7.22001C36.2513 7.45296 35.9961 7.44157 35.768 7.1787C35.574 6.95475 35.3458 6.75234 35.2183 6.47629C35.1536 6.33797 35.0488 6.30203 34.9057 6.30084C34.5692 6.29724 34.2548 6.40623 33.9488 6.51222C33.3698 6.71342 33.044 6.3733 32.747 5.98826C32.4734 5.63317 32.5644 5.24514 32.7722 4.89723C32.9267 4.63855 33.1554 4.44392 33.4799 4.4056C33.7847 4.36968 34.0884 4.31279 34.3943 4.29543C34.8243 4.27147 35.0033 4.1559 35.1123 3.73194C35.1829 3.4535 35.2434 3.16906 35.2674 2.88343C35.2967 2.53193 35.4297 2.22953 35.6213 1.94569C35.889 1.54989 36.2878 1.47504 36.6806 1.75228C37.2435 2.1487 37.5728 2.6445 37.3866 3.37625C37.3177 3.64812 37.3507 3.93075 37.4153 4.20619C37.4375 4.30022 37.471 4.37627 37.5746 4.39902C37.8608 4.4613 38.3956 4.05531 38.4117 3.7595C38.4381 3.26069 38.4057 2.76308 38.3393 2.27024C38.2596 1.687 38.4034 1.1385 38.604 0.612151C38.7435 0.246868 39.101 0.0845928 39.4776 0.00674468C39.5788 -0.0141975 39.6776 0.0145295 39.7591 0.0804081C40.074 0.337289 40.4094 0.575018 40.6962 0.860662C41.0058 1.16785 41.1567 1.54509 41.019 2.00198C40.919 2.33433 40.9082 2.68823 40.7884 3.01936C40.7501 3.12535 40.7681 3.25769 40.7813 3.37505C40.8213 3.74212 40.9411 3.85111 41.3082 3.8038C41.6417 3.76129 41.983 3.74632 42.3016 3.62356C42.5777 3.51696 42.8699 3.54392 43.1543 3.51457C43.5567 3.47267 43.9124 3.27925 44.2968 3.18045C44.8789 3.03135 45.5035 3.27445 45.6771 3.8014C45.7418 3.99842 45.8364 4.18584 45.9052 4.38225C46.0406 4.76908 45.9406 5.10083 45.6675 5.39844C45.3453 5.74874 45.1579 6.15174 45.204 6.64516C45.2711 7.35893 45.0094 7.97032 44.5962 8.53141C44.5196 8.63559 44.443 8.741 44.3789 8.85296C44.0274 9.47153 43.4465 9.70986 42.7926 9.43262C42.3459 9.24339 41.8968 9.17693 41.4309 9.2386C40.989 9.29788 40.5501 9.20507 40.1004 9.30088C39.5848 9.41106 39.0836 9.56973 38.571 9.68172C37.6339 9.88651 36.7022 10.1123 35.7231 10.0787C35.0303 10.0554 34.3339 10.1452 33.6386 10.1775C33.3877 10.1895 33.1997 10.3722 32.9686 10.4153C32.2752 10.544 31.6344 10.3614 31.0254 10.0356C31.0087 10.0266 30.9967 10.0057 30.9793 9.99848C30.2793 9.70867 30.132 9.14818 30.2266 8.46734C30.2356 8.39907 30.2278 8.3284 30.2272 8.25834Z" fill="#111111"></path>
<path d="M5.99703 8.58825C6.03892 7.90143 6.13712 7.18943 6.1084 6.47087C6.10299 6.34153 6.09162 6.21279 6.07905 6.08463C6.06169 5.91757 6.01556 5.76068 5.82515 5.72415C5.62457 5.68523 5.4503 5.70979 5.28324 5.87625C4.86286 6.29421 4.51437 6.81159 4.00357 7.1044C3.48919 7.39902 2.94009 7.72238 2.27121 7.45651C1.88618 7.30321 1.46883 7.23556 1.08979 7.04872C0.673609 6.84392 0.378371 6.51159 0.0963633 6.16668C-0.0114545 6.03494 -0.0821026 5.83493 0.184957 5.77746C0.691554 5.66847 1.01552 5.28104 1.40056 4.99539C1.77061 4.72115 2.1335 4.44868 2.57241 4.29659C2.82273 4.20977 3.02872 4.04329 3.22272 3.87922C3.73293 3.44808 4.29998 3.09837 4.84013 2.71095C5.01976 2.58219 5.22575 2.51633 5.45151 2.49418C6.08084 2.4307 6.62994 2.5313 7.18204 2.91813C7.64792 3.24509 8.13837 3.55168 8.62161 3.86066C9.32341 4.31097 9.53719 4.90978 9.3725 5.69961C9.19465 6.55351 9.05515 7.41519 8.87968 8.2703C8.65633 9.36012 8.33957 10.4278 8.15094 11.5248C8.07909 11.9434 7.96769 12.3673 7.6689 12.6985C7.22217 13.1937 6.67365 13.435 5.9982 13.3314C5.9 13.3159 5.80121 13.2979 5.70658 13.2698C5.62157 13.244 5.55209 13.2566 5.48382 13.3099C5.35447 13.4099 5.33474 13.8566 5.44011 14.0991C5.53415 14.3171 5.71316 14.3123 5.89221 14.2734C6.14374 14.2189 6.39581 14.1584 6.63773 14.0722C7.4078 13.7979 7.87127 14.3542 7.9246 14.9913C7.96052 15.4279 7.84013 15.8374 7.72698 16.2488C7.56112 16.8518 7.34373 17.447 7.22575 18.0548C7.06827 18.8686 7.04674 19.7075 6.9557 20.5345C6.9012 21.0315 6.84371 21.5429 6.35989 21.8363C5.96527 22.0752 5.50239 22.0591 5.06348 22.0932C4.87127 22.1082 4.72752 21.9118 4.58922 21.7657C4.16406 21.3172 4.07005 20.698 3.76466 20.1872C3.64909 19.9938 3.53232 19.7932 3.48981 19.562C3.43948 19.289 3.50955 19.1854 3.78381 19.1758C3.87244 19.1722 3.96286 19.1854 4.05028 19.2016C4.46467 19.2764 4.53473 19.2453 4.58323 18.8381C4.63652 18.3932 4.75567 17.962 4.82273 17.5213C4.86107 17.2656 4.81794 17.0081 4.81915 16.7524C4.82094 16.4141 4.43773 16.2368 4.14249 16.4069C4.03171 16.471 3.93471 16.5518 3.80957 16.5985C3.60954 16.674 3.45027 16.6428 3.282 16.5189C2.7269 16.1093 2.66166 15.9752 2.8455 15.3117C2.96165 14.8913 3.10894 14.4782 3.27063 14.0728C3.52811 13.4266 3.68381 12.7703 3.64551 12.0673C3.62153 11.6392 3.84371 11.4482 4.27604 11.4949C4.52216 11.5212 4.76407 11.5883 5.0084 11.6344C5.34132 11.6979 5.40718 11.6536 5.48265 11.3308C5.6587 10.5775 5.86348 9.83018 5.97605 9.06252C5.9982 8.91282 6.00061 8.76611 5.99703 8.58825Z" fill="#111111"></path>
<path d="M89.9632 7.61342C89.9614 7.25832 90.0554 7.11101 90.5949 6.99365C90.7949 6.95052 91.0057 6.94034 91.2159 6.98108C91.7428 7.08286 92.2482 6.90323 92.7416 6.78646C93.7584 6.54513 94.7872 6.38645 95.8153 6.21639C96.3525 6.12716 96.901 6.12237 97.4411 6.06548C98.1279 5.99303 98.8291 5.97207 99.492 5.75172C99.7429 5.66787 99.9914 5.54153 100.205 5.38525C100.438 5.21458 100.666 5.22836 100.932 5.24154C101.602 5.27565 101.975 5.71818 102.315 6.19483C102.566 6.54633 102.438 6.97808 102.351 7.32598C101.971 8.83558 101.403 10.2805 100.565 11.5979C100.354 11.9302 100.153 12.3374 99.695 12.4524C99.5645 12.4853 99.4333 12.5099 99.3154 12.4506C99.0201 12.3009 98.7184 12.2925 98.392 12.3075C97.8597 12.3314 97.3231 12.2949 96.7908 12.2937C96.198 12.2925 95.604 12.4284 95.0321 12.5883C94.2854 12.7973 93.5321 12.9781 92.7788 13.1578C92.2159 13.2919 91.8338 12.9428 91.5129 12.5428C90.9231 11.8063 90.6219 10.9542 90.5638 10.017C90.5297 9.46253 90.3787 8.94517 90.135 8.44994C90.0194 8.21462 89.9584 7.96432 89.9632 7.61342ZM94.6405 11.1374C94.9926 11.2182 95.2501 11.056 95.5291 11.0506C96.3848 11.0338 97.2405 11.0805 98.0974 11.023C98.2914 11.0099 98.4052 10.9368 98.5273 10.8135C98.7244 10.6134 98.7435 10.3494 98.798 10.1003C98.9423 9.44038 99.2459 8.82601 99.3351 8.14995C99.3938 7.70863 99.1968 7.51401 98.7711 7.64754C98.5651 7.71163 98.3603 7.74395 98.1531 7.71521C97.1764 7.57868 96.1986 7.55233 95.2153 7.63497C95.0015 7.65293 94.7818 7.70263 94.5632 7.65054C94.1069 7.54155 93.677 7.63917 93.253 7.81223C93.0189 7.90743 92.9105 8.06253 92.9165 8.31522C92.9327 9.0745 92.7752 9.81703 92.6674 10.5619C92.559 11.3111 92.9806 11.4063 93.5303 11.3332C93.0835 10.9847 92.8823 10.5248 93.0902 10.1392C93.4333 9.50206 93.7375 8.84037 94.1782 8.25952C94.3387 8.04815 94.5345 7.93676 94.8165 7.94934C95.1638 7.96491 95.5129 7.93437 95.8614 7.93318C96.0405 7.93198 96.2111 7.94934 96.3423 8.11461C96.4195 8.21163 96.5423 8.2757 96.653 8.34156C96.968 8.52899 97.3064 8.68288 97.5992 8.89905C97.9393 9.15056 98.083 9.50745 98.0094 9.94638C97.9417 10.3488 97.6363 10.3955 97.3231 10.4919C96.777 10.6602 96.2111 10.6943 95.6566 10.7991C95.3237 10.8613 94.9758 10.9134 94.6405 11.1374ZM95.9171 9.59846C95.9112 9.42481 95.492 8.92001 95.3255 8.88229C95.2393 8.86254 95.1626 8.88169 95.1069 8.94517C94.8495 9.23858 94.653 9.57212 94.5321 9.94159C94.4776 10.1081 94.5608 10.241 94.7542 10.1482C95.0782 9.99309 95.4285 9.91882 95.7602 9.79008C95.8471 9.75594 95.9219 9.70925 95.9171 9.59846Z" fill="#111111"></path>
<path d="M92.8811 18.7816C92.8721 18.3528 92.938 17.9307 92.9841 17.5061C93.0272 17.1127 92.8398 16.7474 92.8092 16.3618C92.7584 15.7103 92.447 15.1474 92.2086 14.56C92.0158 14.0839 92.2362 13.739 92.744 13.6755C93.1937 13.6198 93.6326 13.6761 94.0757 13.721C94.3374 13.7474 94.538 13.9067 94.7362 14.0498C95.2039 14.3863 95.6853 14.3073 96.1799 14.1486C96.5853 14.0186 96.9105 13.7552 97.2566 13.5216C97.8057 13.1516 98.4548 13.0073 99.0273 13.3186C99.4285 13.5366 99.8081 13.8857 100.095 14.2839C100.157 14.3707 100.136 14.466 100.113 14.5689C100.008 15.0474 99.8291 15.5007 99.6656 15.9606C99.4548 16.5546 99.383 17.1791 99.3063 17.8007C99.2716 18.0863 99.2692 18.3762 99.2399 18.6624C99.1578 19.4612 98.2309 20.0588 97.4776 19.9906C97.0189 19.9486 96.8021 19.6043 96.9201 19.1588C96.9428 19.0726 96.9632 18.981 97.0081 18.9055C97.4063 18.2367 97.3231 17.554 96.9955 16.9091C96.7518 16.43 96.8135 15.9713 96.9021 15.4917C96.9416 15.2791 97.0422 15.027 96.8686 14.8695C96.6961 14.7133 96.4985 14.9145 96.3201 14.9797C95.826 15.1606 95.3494 15.3863 94.8236 15.4785C94.5686 15.5228 94.4057 15.7785 94.4781 16.0809C94.6302 16.7187 94.6716 17.3792 94.8823 18.0037C94.9105 18.087 94.9153 18.2085 95.0075 18.2235C95.1296 18.2432 95.162 18.1073 95.1931 18.0253C95.3135 17.7043 95.3308 17.3798 95.1278 17.0821C94.9111 16.7654 94.8931 16.4055 94.9165 16.0492C94.9416 15.6642 95.3069 15.6151 95.7231 15.8007C96.4512 16.1258 96.5087 16.8169 96.7333 17.4271C96.9087 17.9055 96.874 18.3732 96.7237 18.857C96.5919 19.2786 96.5997 19.7367 96.3811 20.1427C96.2776 20.3349 96.4051 20.4421 96.6057 20.4313C96.8835 20.4157 97.1608 20.3774 97.4213 20.275C98.3536 19.9091 99.271 19.9762 100.182 20.3528C100.357 20.4253 100.53 20.5055 100.712 20.5522C100.945 20.6127 100.939 20.7355 100.843 20.9055C100.727 21.1139 100.609 21.3217 100.499 21.5331C100.445 21.6361 100.375 21.7187 100.268 21.7552C99.4692 22.0235 98.6812 22.2205 97.8297 21.8984C97.1991 21.6594 96.5243 21.7187 95.8542 21.8026C95.2841 21.8732 94.8823 22.1223 94.5865 22.6127C94.3949 22.9295 94.0488 22.9589 93.7063 22.9241C93.0308 22.8559 92.4655 22.5469 91.941 22.1337C91.7565 21.9888 91.7332 21.8415 91.8128 21.6415C91.9961 21.181 92.3661 21.0133 92.8248 20.9876C93.1919 20.9672 93.5452 20.8505 93.9183 20.8516C94.1368 20.8522 94.3332 20.7199 94.4907 20.5582C94.7578 20.2846 94.9476 19.9564 95.1267 19.6235C95.1799 19.5253 95.2267 19.3816 95.0823 19.3073C94.9578 19.2433 94.8829 19.351 94.8087 19.4319C94.8021 19.4391 94.7931 19.4462 94.7901 19.4552C94.5697 20.1463 94.0428 20.0019 93.5458 19.8995C93.0715 19.8013 92.8847 19.5462 92.8811 19.0504V18.7816Z" fill="#111111"></path>
<path d="M70.0851 22.6268C69.0204 22.7801 68.0144 22.237 67.0796 21.5819C66.5227 21.1908 66.0862 20.6447 65.8263 19.9842C65.4215 18.9549 65.2257 17.8788 65.1467 16.7866C65.103 16.1854 65.1592 15.574 65.2023 14.9692C65.2287 14.6022 65.2293 14.2357 65.2389 13.8692C65.2586 13.0931 65.3957 12.3297 65.4928 11.5626C65.5329 11.2434 65.8215 11.0273 65.9874 10.7584C66.4527 10.0057 67.2018 9.57453 67.8826 9.06496C68.0659 8.92783 68.2527 8.79609 68.4413 8.66735C68.7766 8.43859 69.1713 8.50386 69.4186 8.82063C69.8072 9.31825 70.0192 9.8955 70.1371 10.5039C70.2455 11.0608 69.7287 11.5273 69.1269 11.4285C68.9682 11.4027 68.812 11.3871 68.6533 11.3871C68.1305 11.3871 67.8658 11.5877 67.7353 12.0949C67.6096 12.5841 67.6437 13.0836 67.667 13.58C67.709 14.453 67.8628 15.3165 67.8778 16.1926C67.888 16.7878 67.9341 17.3824 67.9617 17.9776C67.967 18.0926 68.015 18.192 68.0694 18.2848C68.3706 18.8052 68.6072 19.3633 69.1862 19.6812C69.527 19.8687 69.8797 19.8441 70.2264 19.8759C70.4886 19.8998 70.9012 19.3627 70.9803 19.0351C71.1336 18.3992 71.1731 17.7477 71.2707 17.104C71.3078 16.8567 71.4503 16.6656 71.6216 16.4938C71.797 16.3171 71.9773 16.3405 72.0923 16.5387C72.2695 16.8453 72.4994 17.1327 72.5479 17.5016C72.6204 18.0519 72.8881 18.5615 72.8773 19.1291C72.8647 19.7938 72.8653 20.4591 72.8468 21.1238C72.8414 21.3196 72.7168 21.4825 72.5887 21.619C71.8809 22.3765 71.4066 22.6944 70.0851 22.6268Z" fill="#111111"></path>
<path d="M16.0652 2.90069C16.0544 3.47975 15.8221 3.96717 15.4903 4.34742C14.8292 5.10432 14.4574 6.01451 13.9717 6.86722C13.3017 8.04448 12.9424 9.34209 12.4514 10.5888C12.3214 10.9182 12.2167 11.2667 12.216 11.6307C12.2154 11.9619 12.3035 12.0798 12.6286 12.0852C13.2646 12.096 13.8975 12.0343 14.5274 11.9511C14.7682 11.9194 14.8604 11.717 14.9382 11.5194C15.1089 11.0852 15.2741 10.6493 15.4406 10.214C15.5532 9.92055 15.7676 9.69179 15.9436 9.4397C16.0789 9.24449 16.273 9.25527 16.4065 9.45108C16.4622 9.53191 16.5047 9.62472 16.5395 9.71754C16.8191 10.4643 17.1993 11.1637 17.5664 11.8685C17.6886 12.1032 17.7089 12.3397 17.5844 12.5523C17.3892 12.8852 17.1568 13.1978 16.9299 13.511C16.8119 13.6751 16.628 13.696 16.4365 13.7397C15.9598 13.8487 15.5263 13.7439 15.0724 13.6206C14.7286 13.5272 14.3562 13.6457 14.0041 13.6284C13.1747 13.5883 12.4154 13.7912 11.6579 14.0966C11.1442 14.3038 10.6675 14.1865 10.2735 13.7625C10.113 13.59 9.91063 13.4535 9.71722 13.3146C9.38488 13.0751 9.25492 12.7481 9.38667 12.3589C9.47889 12.0864 9.58309 11.8122 9.72559 11.5637C10.0968 10.9176 10.1382 10.1774 10.3495 9.48701C10.6316 8.56484 10.8603 7.62891 11.0298 6.68159C11.2358 5.52947 11.6987 4.49833 12.3005 3.49711C12.9035 2.49411 13.6226 1.5905 14.3669 0.69947C14.5819 0.441384 14.8622 0.416841 15.1562 0.51205C15.4412 0.604865 15.6712 0.747988 15.7191 1.09529C15.7862 1.57614 15.8382 2.06117 16.0161 2.52165C16.0646 2.64681 16.0532 2.7953 16.0652 2.90069Z" fill="#111111"></path>
<path d="M64.1603 5.00898C64.5675 4.93115 64.9848 4.91437 65.3968 4.86169C65.8812 4.79941 66.0178 4.63354 66.0686 4.14132C66.107 3.77185 66.1615 3.39999 66.0716 3.02753C66.0477 2.92872 66.0226 2.83411 65.9633 2.75327C65.7621 2.47961 65.8147 2.20895 65.977 1.94547C66.107 1.73351 66.2519 1.5311 66.386 1.32212C66.5172 1.11853 66.695 1.02272 66.9405 1.05206C67.8998 1.16642 68.7507 1.54667 69.4184 2.23172C69.8968 2.72214 69.9046 3.11376 69.292 3.41675C69.0004 3.56047 68.7615 3.79161 68.4369 3.89221C68.2304 3.95628 68.1507 4.15928 68.2148 4.38503C68.283 4.62275 68.4711 4.55329 68.6346 4.54372C69.4884 4.49281 70.2801 4.22635 71.0436 3.85568C71.3609 3.70239 71.7759 3.88862 71.9544 4.25329C72.4106 5.18145 72.0735 5.91079 71.0825 6.2132C70.7274 6.32158 70.3986 6.35511 70.0382 6.25451C69.7142 6.16349 69.3753 6.12517 69.0417 6.07128C68.7777 6.02875 68.6262 6.17906 68.4884 6.38625C68.1974 6.82279 67.9573 7.29525 67.5639 7.66112C67.1513 8.04495 67.0783 8.03119 66.6657 7.65393C66.3543 7.3695 66.195 7.07128 66.2327 6.64015C66.2633 6.29223 66.1723 6.23295 65.8196 6.24373C65.4728 6.25451 65.1249 6.22098 64.7788 6.29823C64.513 6.35811 64.3321 6.46649 64.2824 6.7659C64.2213 7.13297 64.174 7.50602 63.9824 7.84315C63.7112 8.321 63.2483 8.41083 62.8243 8.04316C62.5435 7.79885 62.3069 7.51262 62.159 7.17129C62.062 6.94793 61.9321 6.92039 61.7368 7.0132C61.2877 7.22637 60.8333 7.42998 60.3907 7.65633C59.8476 7.93417 59.3314 7.87909 58.8362 7.55633C58.6339 7.42399 58.4212 7.32757 58.1943 7.24733C57.965 7.16651 57.7314 7.04853 57.7266 6.7659C57.7219 6.50242 57.9254 6.35751 58.1494 6.27966C58.5805 6.12996 58.9045 5.8048 59.3021 5.60002C59.5254 5.48504 59.7416 5.43234 59.9841 5.48444C60.1985 5.53054 60.4147 5.55211 60.6351 5.54732C61.1249 5.53774 61.4075 5.21438 61.2692 4.74132C61.0997 4.16047 60.8925 3.58801 60.4386 3.14848C60.283 2.99699 60.2835 2.794 60.3674 2.61913C60.6105 2.11195 60.8883 1.66463 61.5393 1.56763C62.1243 1.4808 62.6824 1.46764 63.2357 1.6832C63.6513 1.84487 63.7255 2.00896 63.5974 2.44907C63.4872 2.82753 63.3267 3.20179 63.4177 3.60958C63.4973 3.96766 63.5878 4.32275 63.6758 4.67904C63.7375 4.93055 63.8884 5.0551 64.1603 5.00898Z" fill="#111111"></path>
<path d="M14.3845 19.3338C14.3755 19.5368 14.3593 19.6758 14.3659 19.8147C14.3737 19.9949 14.4569 20.0842 14.6335 19.9644C14.8934 19.7889 15.13 19.5889 15.1808 19.2542C15.2108 19.0596 15.3228 18.9662 15.5036 18.9129C16.024 18.7596 16.5551 18.7548 17.0857 18.7991C17.3575 18.8219 17.6024 18.9404 17.7563 19.1931C18.1982 19.9189 18.4468 20.6848 18.2755 21.5465C18.206 21.8961 18.1055 21.9489 17.7635 21.88C17.033 21.7339 16.2988 21.6069 15.5839 21.3925C15.5156 21.3722 15.4467 21.3824 15.382 21.4201C14.8749 21.7123 14.339 21.9584 13.8934 22.3512C13.5341 22.668 13.2551 22.6261 12.9162 22.2752C12.4497 21.7919 12.2778 21.2273 12.306 20.5662C12.3233 20.1512 12.2407 19.7356 12.2461 19.3165C12.2503 19.0446 12.0736 18.8536 11.8359 18.7446C11.6024 18.638 11.4048 18.75 11.2479 18.9183C10.9886 19.1961 10.6856 19.2961 10.3077 19.232C9.88381 19.1602 9.66342 18.8817 9.57662 18.5051C9.44008 17.9117 9.58258 17.3194 9.65085 16.729C9.65984 16.65 9.68857 16.5715 9.68619 16.4937C9.66284 15.8488 10.0401 15.4242 10.4862 15.041C10.6311 14.9164 10.8173 14.8973 10.9958 14.953C11.2646 15.0368 11.5407 15.0559 11.8167 15.0919C12.0778 15.1254 12.2156 15.0206 12.2743 14.7853C12.3156 14.6218 12.3323 14.453 12.3731 14.2895C12.4688 13.9056 12.667 13.7967 13.0371 13.9284C13.5569 14.1128 14.0473 14.3458 14.3868 14.808C14.506 14.9703 14.6749 14.9715 14.8467 14.9404C15.2905 14.8595 15.7389 14.8859 16.1857 14.8781C16.4881 14.8727 16.7342 14.9817 16.9545 15.2074C17.1659 15.4248 17.3222 15.6751 17.4971 15.9152C17.6498 16.1242 17.6755 16.3817 17.5881 16.6015C17.306 17.3099 16.8881 17.9362 16.3054 18.435C16.0701 18.6368 15.7767 18.6464 15.4904 18.5428C15.285 18.468 15.088 18.3686 14.8695 18.3368C14.5078 18.2841 14.3012 18.5033 14.3521 18.8614C14.3773 19.038 14.3773 19.2183 14.3845 19.3338ZM14.4934 16.6176C14.4904 17.02 14.5402 17.0739 14.9515 17.1476C15.1168 17.1769 15.2749 17.1763 15.3737 17.0146C15.4779 16.8446 15.4126 16.6787 15.3042 16.547C15.1413 16.35 14.8988 16.2931 14.6605 16.3135C14.4832 16.3284 14.4898 16.5033 14.4934 16.6176ZM12.0731 16.7715C12.079 16.5464 11.9419 16.4919 11.782 16.4865C11.497 16.4769 11.2126 16.7769 11.2209 17.0709C11.2257 17.2482 11.2533 17.4177 11.4904 17.4194C11.8018 17.4213 12.0736 17.1075 12.0731 16.7715Z" fill="#111111"></path>
<path d="M86.8939 18.2005C86.0735 18.2298 85.4562 17.8119 84.8035 17.5101C84.6256 17.4274 84.4633 17.261 84.516 17.0316C84.5693 16.8041 84.7454 16.658 84.9711 16.6065C85.2603 16.5412 85.5077 16.4023 85.7442 16.2334C85.8634 16.1484 85.9149 16.037 85.8711 15.8969C85.8059 15.6879 85.7376 15.4795 85.6562 15.2759C85.5945 15.1214 85.5011 15.0717 85.3753 15.2322C85.2711 15.3651 85.1651 15.4981 85.0454 15.616C84.8436 15.813 84.6885 15.7855 84.4897 15.5915C83.7927 14.9124 83.2615 14.1184 82.8148 13.2597C82.7238 13.0855 82.5879 12.9352 82.4705 12.7753C82.3202 12.5711 82.3986 12.4567 82.6088 12.3687C83.1525 12.14 83.7316 12.119 84.3052 12.0783C84.7711 12.0453 84.8077 12.0226 84.8124 11.5513C84.8142 11.3597 84.7777 11.1783 84.731 10.9932C84.5538 10.286 84.904 9.77285 85.6214 9.67284C85.7783 9.65129 85.9358 9.63033 86.0939 9.61895C86.3753 9.5974 86.5963 9.77404 86.6694 10.1166C86.7957 10.704 87.0747 11.2393 87.2346 11.813C87.3801 12.3363 87.7688 12.4531 88.2328 12.4753C88.5101 12.4885 88.7921 12.4369 89.061 12.5435C89.17 12.5866 89.3041 12.601 89.3179 12.7549C89.3305 12.895 89.2358 12.9783 89.1358 13.0435C88.8125 13.2543 88.5011 13.4861 88.1478 13.6483C87.8981 13.7627 87.7807 13.9471 87.8346 14.222C87.9101 14.6094 88.1376 14.71 88.4334 14.4591C89.0047 13.9735 89.7047 13.6783 90.276 13.1938C90.4077 13.0819 90.5874 13.0915 90.7305 13.1639C90.8808 13.2406 90.8305 13.4082 90.8227 13.5442C90.8095 13.7555 90.6802 13.9124 90.5538 14.0663C90.0933 14.6256 89.5502 15.1364 89.1891 15.7537C88.8119 16.3981 88.3131 16.9927 88.1358 17.7448C88.1071 17.867 88.04 17.9328 87.9376 17.9855C87.5915 18.1616 87.2263 18.2322 86.8939 18.2005Z" fill="#111111"></path>
<path d="M59.4957 19.8827C59.4927 19.1983 59.7166 18.6666 59.8172 18.1115C59.9358 17.46 59.9238 16.833 59.728 16.2031C59.6705 16.0174 59.5417 15.9785 59.3825 15.9995C58.9849 16.0522 58.5968 16.051 58.2106 15.9126C57.7609 15.751 57.6064 15.5695 57.6268 15.1707C57.6609 14.5072 57.7854 14.3276 58.3034 14.2156C59.3154 13.9971 59.9639 13.3695 60.3274 12.421C60.5801 11.7617 60.8352 11.1042 61.0819 10.4425C61.295 9.86946 61.6681 9.73114 62.1945 10.0521C62.5071 10.2431 62.7131 10.5305 62.9095 10.8371C63.1657 11.2377 63.4783 11.6042 63.6951 12.0294C64.049 12.724 63.8472 13.2647 63.1574 13.6402C62.7573 13.8569 62.3604 14.0929 62.0442 14.4306C61.7831 14.7096 61.7406 14.9809 61.9448 15.2977C62.3819 15.9743 62.4586 16.7138 62.4064 17.4935C62.3406 18.4827 62.1741 19.463 62.1573 20.4576C62.1495 20.9349 61.8819 21.3037 61.5208 21.5936C61.2406 21.8193 60.9538 21.7804 60.6999 21.5343C60.4567 21.2989 60.213 21.0636 59.9543 20.8462C59.616 20.5624 59.4292 20.2253 59.4957 19.8827Z" fill="#111111"></path>
<path d="M95.8436 5.34346C95.6023 5.39736 95.3592 5.29256 95.1071 5.18778C94.3286 4.86202 93.561 5.03747 92.8155 5.30455C92.3334 5.47761 91.8502 5.65185 91.416 5.95186C91.1023 6.16802 90.743 6.06563 90.4711 5.78777C90.1663 5.4758 89.8292 5.19796 89.6328 4.78956C89.4945 4.50333 89.4561 4.24885 89.6717 3.98956C89.8813 3.73805 90.0807 3.47757 90.2927 3.22787C90.5094 2.97398 90.7987 2.98475 91.0603 3.10212C91.8526 3.45721 92.6843 3.32668 93.5023 3.31829C93.8885 3.31471 94.1526 2.97696 94.1382 2.58474C94.1298 2.34822 94.0885 2.1117 94.049 1.87697C93.9753 1.43683 94.0927 1.17275 94.5304 1.1015C95.1382 1.00269 95.6945 0.788316 96.2514 0.545214C96.5556 0.412872 96.7664 0.533829 96.8772 0.845806C97.0173 1.24042 97.0257 1.62127 96.8047 1.99493C96.7347 2.11289 96.688 2.24881 96.655 2.38296C96.5556 2.79075 96.7257 2.93325 97.1317 2.85961C97.8934 2.72248 98.6598 2.6123 99.4263 2.5075C99.588 2.48595 99.7377 2.44882 99.8832 2.38594C100.315 2.20031 100.739 2.24462 101.157 2.42846C101.394 2.53266 101.413 2.64642 101.225 2.81829C100.581 3.40692 99.7964 3.69794 98.9688 3.9171C98.3161 4.08955 97.6586 4.23447 96.9964 4.36021C96.743 4.40813 96.6454 4.60514 96.5754 4.81831C96.4077 5.3303 96.4101 5.33149 95.8436 5.34346Z" fill="#111111"></path>
<path d="M42.3929 22.2932C42.1761 22.2638 41.9372 22.2129 41.7366 22.0417C41.1174 21.5117 40.4258 21.0794 39.7665 20.6039C39.5563 20.4524 39.4659 20.3482 39.5719 20.0776C39.845 19.3782 40.2988 19.1818 40.9623 19.5267C41.427 19.7686 41.8875 20.0183 42.3474 20.2692C42.518 20.3626 42.6995 20.347 42.8707 20.3105C43.0923 20.2632 43.3282 20.2135 43.5186 20.0991C44.0474 19.78 44.6384 19.5967 45.1755 19.2967C45.6809 19.0147 46.2061 19.074 46.7127 19.3225C46.8109 19.3704 46.9037 19.4201 47.0133 19.4428C47.2977 19.5033 47.3827 19.7327 47.4109 19.9764C47.4379 20.2105 47.2983 20.365 47.0911 20.4644C46.0971 20.9434 45.066 21.3303 44.0318 21.7117C43.7917 21.8003 43.5623 21.9261 43.3408 22.0566C43.0551 22.2249 42.7456 22.274 42.3929 22.2932Z" fill="#111111"></path>
<path d="M69.5017 15.0933C69.0316 15.1041 68.6345 14.9891 68.3441 14.6244C68.0262 14.2256 68.0369 13.7065 68.3579 13.3089C68.7549 12.8166 69.3016 12.5316 69.8328 12.2214C70.0034 12.1214 70.1639 12.0016 70.322 11.8813C70.6783 11.6094 70.8346 11.2717 70.6999 10.8148C70.625 10.5633 70.7489 10.3938 70.9849 10.416C71.4579 10.4603 71.934 10.4789 72.3993 10.601C72.752 10.6932 72.9628 10.9046 73.0077 11.2507C73.0717 11.7495 73.2801 12.2687 72.8807 12.731C72.8088 12.8142 72.7394 12.9065 72.6304 12.9376C72.0208 13.1136 71.5933 13.513 71.2106 14.0041C70.8741 14.4358 70.4394 14.7729 69.9459 15.028C69.7921 15.1077 69.6358 15.0783 69.5017 15.0933Z" fill="#111111"></path>
<path d="M34.7036 19.9219C35.2329 20.05 35.7622 20.1776 36.291 20.3075C36.3485 20.3213 36.4012 20.3542 36.4581 20.3698C36.9503 20.5057 37.0419 20.7327 36.7707 21.1638C36.6174 21.4075 36.4659 21.6542 36.2934 21.8848C35.9694 22.3159 35.4928 22.2956 35.2042 21.8506C34.9922 21.5225 34.7497 21.2303 34.3886 21.0458C34.1347 20.9165 34.0006 20.3872 34.1353 20.1411C34.2634 19.9057 34.4922 19.9722 34.7036 19.9219Z" fill="#111111"></path>
<path d="M31.0952 21.7906C30.6389 21.814 30.3706 21.52 30.0496 21.3565C29.7346 21.196 29.5143 20.917 29.3251 20.6242C29.1604 20.3691 29.2203 20.1739 29.5023 20.0942C30.1005 19.9247 30.7017 19.9798 31.2874 20.1607C31.5281 20.2349 31.7514 20.8613 31.6981 21.2116C31.6275 21.6793 31.5005 21.7912 31.0952 21.7906Z" fill="#111111"></path>
<path d="M117.877 8.72758C117.933 8.34675 117.984 7.97667 117.999 7.59823C118.013 7.20302 117.769 6.69044 117.321 6.67606C117.059 6.67009 116.778 6.75331 116.602 7.21919C116.497 7.50242 116.55 7.75452 116.596 8.01141C116.596 8.01141 116.737 8.77429 116.851 9.35455C116.851 9.35455 117.06 10.2725 117.075 10.3456C117.097 10.4635 117.054 10.4965 117.005 10.5102C116.95 10.5288 116.886 10.4821 116.853 10.4007C116.804 10.2743 116.45 9.41144 116.336 9.14675C116.092 8.58687 115.902 8.10302 115.644 7.60841C115.56 7.44553 115.416 7.32697 115.241 7.27668C115.051 7.22158 114.862 7.26411 114.685 7.36769C114.463 7.49643 114.402 7.70362 114.37 7.90542C114.329 8.15393 114.457 8.33057 114.559 8.51202C114.559 8.51202 115.192 9.40305 115.709 9.95515C115.831 10.0851 116.254 10.5779 116.314 10.6468C116.366 10.7061 116.35 10.7647 116.327 10.7977C116.304 10.83 116.23 10.836 116.181 10.8019C116.077 10.7324 115.42 10.1887 115.259 10.0629C114.889 9.76952 114.536 9.44795 114.152 9.15933C113.887 8.95752 113.576 8.85813 113.336 8.94555C113.102 9.03058 112.955 9.20364 112.924 9.42759C112.88 9.75156 113.063 10.0282 113.309 10.2072C113.572 10.3965 113.871 10.5126 114.158 10.6246C114.286 10.6731 114.414 10.7222 114.539 10.7791C114.82 10.9055 115.648 11.1983 115.748 11.2288C115.827 11.2516 115.994 11.3246 115.965 11.4348C115.939 11.5426 115.691 11.4773 115.587 11.463C115.029 11.3797 114.378 11.2288 113.82 11.1396C113.82 11.1396 113.484 11.0845 113.334 11.0498C113.08 10.9911 112.778 11.054 112.546 11.2516C112.348 11.418 112.267 11.6037 112.28 11.8091C112.3 12.1815 112.447 12.3546 112.892 12.5157C113.014 12.56 113.209 12.5725 113.346 12.5522C113.814 12.4809 114.156 12.3767 114.545 12.2977C114.814 12.2426 115.566 12.0348 115.76 11.9965C115.845 11.9779 115.908 11.9821 115.933 12.0576C115.957 12.1324 115.904 12.1815 115.85 12.2061C115.485 12.3666 115.057 12.5642 114.699 12.7186C114.36 12.8654 113.989 13.0199 113.637 13.1809C113.375 13.3007 113.024 13.4803 112.912 13.942C112.857 14.166 113.014 14.4792 113.185 14.5995C113.415 14.7624 113.653 14.7301 113.896 14.5504C114.315 14.239 115.206 13.5145 115.697 13.1157C115.835 13.0019 115.969 12.8839 116.098 12.7678C116.192 12.6845 116.259 12.7049 116.295 12.7414C116.326 12.772 116.346 12.8246 116.269 12.9181L116.036 13.1971C115.689 13.6163 115.326 14.0498 114.986 14.4875C114.826 14.6971 114.665 14.9091 114.502 15.2367C114.315 15.6151 114.408 15.9714 114.754 16.1894C114.909 16.287 115.088 16.2726 115.288 16.1462C115.444 16.0486 115.642 15.8959 115.742 15.6882C115.949 15.257 116.141 14.8091 116.326 14.3756C116.409 14.1803 116.493 13.9851 116.578 13.7893C116.618 13.7001 116.659 13.6127 116.702 13.5247C116.753 13.4193 116.792 13.3073 116.839 13.1995C116.871 13.1282 116.918 13.1079 116.987 13.1282C117.056 13.1486 117.069 13.2091 117.059 13.2522C117.009 13.4881 116.909 13.9175 116.849 14.2414C116.798 14.5037 116.747 14.7666 116.698 15.0289L116.686 15.1001C116.647 15.3055 116.607 15.5175 116.602 15.7289C116.59 16.1648 116.702 16.4474 117.009 16.6349C117.142 16.7163 117.299 16.7367 117.453 16.6959C117.612 16.651 117.696 16.5331 117.771 16.3887C117.95 16.0385 117.923 15.6762 117.881 15.3343C117.854 15.1265 117.827 14.9169 117.799 14.7073C117.799 14.7073 117.618 13.3276 117.608 13.2504C117.6 13.1893 117.642 13.0995 117.724 13.0911C117.797 13.0851 117.866 13.124 117.881 13.1971C117.948 13.5474 118.039 13.8809 118.107 14.2312C118.33 15.3732 118.739 16.1648 119.395 16.1869C119.663 16.1953 119.836 16.5247 119.845 16.6918C119.849 16.8061 119.741 16.8037 119.613 16.8139C119.497 16.8241 119.368 16.7995 119.186 16.7349C119.157 16.7247 119.12 16.7103 119.081 16.6941H119.08C118.856 16.6295 118.52 16.3624 118.52 16.3624C118.52 16.3624 118.442 16.6073 118.151 16.887L118.147 16.8912C117.917 17.0906 117.699 17.2091 117.423 17.2534C117.172 17.29 116.938 17.2397 116.723 17.0972C116.584 17.0055 116.438 16.9073 116.326 16.7774C116.244 16.6816 116.043 16.3624 116.043 16.3624C116.043 16.3624 115.664 16.6061 115.53 16.6738C115.115 16.881 114.705 16.8121 114.353 16.5738C114.071 16.3828 113.902 16.1115 113.865 15.7881C113.838 15.5498 113.924 15.157 113.924 15.157C113.924 15.157 113.458 15.1959 113.403 15.1839H113.397C113.096 15.1636 112.845 15.0312 112.627 14.7768C112.448 14.569 112.355 14.3348 112.342 14.0642C112.335 13.9037 112.367 13.7384 112.438 13.5594L112.44 13.5534C112.44 13.5534 112.572 13.2869 112.766 13.0528C112.473 12.9959 112.285 12.8552 112.285 12.8552C111.96 12.621 111.777 12.2875 111.742 11.9396C111.675 11.2923 112.082 10.9234 112.236 10.7689C112.412 10.5941 112.717 10.5066 112.717 10.5066C112.717 10.5066 112.503 10.2438 112.406 10.0342C112.406 10.0324 112.402 10.0282 112.402 10.0282C112.212 9.58629 112.373 8.98208 112.687 8.6725C112.911 8.45274 113.215 8.37968 113.417 8.36111C113.578 8.34675 113.826 8.40782 113.826 8.40782C113.826 8.40782 113.774 8.20243 113.786 8.02759C113.816 7.58387 114.05 7.13416 114.424 6.92218C114.791 6.7168 115.243 6.65152 115.774 6.94853C115.774 6.94853 115.945 7.04014 116.059 7.20123C116.059 7.18865 116.061 6.98506 116.193 6.7653C116.405 6.4539 116.784 6.14253 117.401 6.14852C117.919 6.1551 118.194 6.48086 118.441 6.81259C118.441 6.81259 118.55 6.96289 118.563 7.14195C118.563 7.14195 118.713 7.00182 118.941 6.88744C119.212 6.75331 119.483 6.68624 119.859 6.75751C119.936 6.77187 120.008 6.83055 119.948 6.94075C119.896 7.03595 119.865 7.07667 119.798 7.19523C119.735 7.30482 119.657 7.3641 119.513 7.39225C119.244 7.44553 119.025 7.60423 118.844 7.87249C118.436 8.47728 118.219 9.16711 118.007 9.8348L117.821 10.427C117.807 10.4701 117.785 10.5007 117.756 10.5144C117.722 10.5306 117.681 10.5246 117.651 10.5168C117.604 10.5007 117.563 10.4737 117.59 10.3438C117.689 9.81862 117.804 9.218 117.877 8.72758Z" fill="#111111"></path>
<path d="M117.375 12.8891C116.836 12.9185 116.249 12.5041 116.151 11.9334C116.097 11.6191 116.197 11.3328 116.383 11.0759C116.561 10.8298 116.818 10.7394 117.106 10.7286C117.281 10.7214 117.498 10.734 117.603 10.749C117.691 10.7622 117.723 10.8346 117.69 10.9053C117.624 11.0466 117.542 11.0825 117.398 11.0622C117.146 11.0275 116.906 11.0772 116.738 11.2993C116.451 11.6808 116.49 12.2209 116.998 12.4305C117.155 12.4951 117.33 12.5215 117.499 12.552C117.627 12.5748 117.686 12.6041 117.683 12.7346C117.679 12.8886 117.469 12.8838 117.375 12.8891Z" fill="#111111"></path>
<path d="M124.292 10.7046C124.255 10.7046 124.207 10.6842 124.136 10.6375C124.103 10.616 124.064 10.6052 124.027 10.5956C123.945 10.574 123.916 10.5603 123.915 10.4986C123.912 10.3998 123.922 10.2956 123.944 10.1986C123.982 10.0327 124.024 9.86684 124.066 9.70157C124.103 9.55487 124.227 9.03988 124.227 9.03988C124.348 8.52431 124.469 8.00814 124.579 7.49017C124.63 7.25184 124.746 7.04225 124.916 6.88358C125.086 6.72549 125.3 6.62728 125.533 6.60093C125.576 6.59614 125.619 6.59375 125.661 6.59375C126.164 6.59375 126.595 6.94286 126.71 7.44286C126.803 7.85186 126.688 8.20336 126.349 8.54946C125.952 8.95427 125.564 9.36624 125.178 9.78061C125.024 9.94648 124.874 10.1171 124.724 10.2878L124.445 10.6028C124.38 10.6752 124.336 10.7046 124.292 10.7046Z" fill="#111111"></path>
<path d="M123.166 10.497C123.093 10.497 123.046 10.473 123.018 10.3503C122.937 9.99877 122.848 9.64906 122.76 9.29934C122.705 9.08199 122.65 8.86462 122.597 8.64664C122.499 8.24366 122.406 7.84006 122.312 7.43585L122.271 7.25682C122.263 7.22389 122.263 7.18915 122.261 7.15083L122.258 7.08615C122.282 6.57477 122.502 6.23883 122.948 6.03345C123.03 5.99632 123.139 5.97656 123.264 5.97656C123.401 5.97656 123.554 6.0011 123.673 6.04123C123.837 6.09631 123.944 6.18615 124.086 6.33165C124.195 6.44303 124.281 6.58255 124.339 6.74543C124.411 6.94124 124.361 7.15322 124.314 7.35802C124.147 8.07358 123.979 8.78798 123.81 9.50175C123.742 9.78558 123.675 10.0694 123.594 10.3491C123.584 10.3862 123.529 10.4365 123.48 10.4551C123.433 10.473 123.378 10.4772 123.32 10.482C123.288 10.4844 123.184 10.497 123.166 10.497Z" fill="#111111"></path>
<path d="M124.728 11.2824C124.646 11.1782 124.563 11.074 124.5 10.9591C124.499 10.9441 124.536 10.8693 124.581 10.8207C124.843 10.5453 125.105 10.2716 125.368 9.99798L126.035 9.30097C126.114 9.21773 126.19 9.13031 126.266 9.04348C126.368 8.92611 126.47 8.80875 126.581 8.70037C126.802 8.4866 127.029 8.26562 127.38 8.26562C127.759 8.27761 128.095 8.53091 128.272 8.77701C128.387 8.93689 128.464 9.15666 128.482 9.38061C128.506 9.69079 128.415 9.96087 128.232 10.1207C128.098 10.2393 127.926 10.3357 127.749 10.3932C127.311 10.5345 126.871 10.6669 126.431 10.7992C126.164 10.8794 125.898 10.9597 125.631 11.0417C125.444 11.1004 125.258 11.1645 125.072 11.2291L124.769 11.3345L124.728 11.2824Z" fill="#111111"></path>
<path d="M128.008 12.957C127.883 12.957 127.745 12.9157 127.611 12.8762L127.528 12.8522C126.922 12.6804 126.318 12.5007 125.715 12.3205L125.304 12.1977C125.166 12.1564 125.03 12.1097 124.894 12.0636L124.84 12.045C124.751 12.0145 124.725 11.9654 124.731 11.8354C124.74 11.648 124.826 11.5905 125.003 11.5396C125.387 11.4283 125.768 11.3103 126.15 11.1929C126.654 11.0372 127.157 10.8821 127.666 10.7414C127.775 10.7109 127.884 10.6953 127.989 10.6953C128.474 10.6953 128.846 11.0235 128.959 11.5516C128.974 11.6199 128.982 11.6887 128.989 11.7588L129.001 11.8588C128.997 12.3959 128.666 12.8043 128.114 12.9444C128.082 12.9528 128.046 12.957 128.008 12.957Z" fill="#111111"></path>
<path d="M125.59 16.8468C125.503 16.8426 125.457 16.8372 125.411 16.8294C125.278 16.8061 125.149 16.7582 125.018 16.7073C124.742 16.5995 124.666 16.3306 124.593 16.0713C124.424 15.4761 124.282 14.915 124.139 14.3546L124.102 14.2084C124.012 13.8581 123.929 13.506 123.851 13.1533C123.828 13.0474 123.861 12.9773 123.954 12.9402C124.007 12.9192 124.057 12.8947 124.108 12.8689C124.14 12.8521 124.169 12.8438 124.195 12.8438C124.233 12.8438 124.269 12.8617 124.307 12.9006C124.466 13.0623 124.626 13.2222 124.786 13.3821C124.953 13.5485 125.12 13.715 125.285 13.8833L125.472 14.0719C125.755 14.3582 126.037 14.6438 126.309 14.9408C126.517 15.1677 126.726 15.4522 126.707 15.8348C126.692 16.1432 126.608 16.369 126.449 16.5258C126.242 16.73 125.939 16.8474 125.62 16.8474L125.59 16.8468Z" fill="#111111"></path>
<path d="M118.826 12.8751C118.588 12.8547 118.367 12.7506 118.151 12.5566C117.884 12.3164 117.764 12.0416 117.783 11.717C117.801 11.4158 117.922 11.1805 118.151 10.9985C118.374 10.8212 118.614 10.7344 118.883 10.7344C119.034 10.7344 119.192 10.7613 119.366 10.8164C119.536 10.8703 119.709 10.9158 119.882 10.9619C120.025 11.0002 120.168 11.038 120.31 11.0805C120.442 11.1206 120.573 11.1655 120.703 11.211C120.825 11.2529 120.947 11.2955 121.07 11.3332C121.377 11.4272 121.686 11.5134 121.995 11.5978C122.141 11.638 122.188 11.7074 122.167 11.8535C122.135 12.0703 122.116 12.0895 121.882 12.141C121.585 12.2068 121.289 12.2841 120.995 12.3613L120.253 12.5572C119.898 12.6506 119.544 12.7446 119.189 12.835C119.115 12.8542 119.037 12.8596 118.953 12.8649L118.826 12.8751Z" fill="#111111"></path>
<path d="M123.278 17.4434C122.75 17.4152 122.418 17.2176 122.235 16.823C122.129 16.5973 122.176 16.3721 122.238 16.1422C122.413 15.5021 122.591 14.8631 122.769 14.2242C122.877 13.8356 122.978 13.4787 123.087 13.1236C123.092 13.1044 123.133 13.0715 123.168 13.0625L123.227 13.0691C123.248 13.0739 123.273 13.0793 123.297 13.0793C123.35 13.0721 123.371 13.0697 123.39 13.0697C123.514 13.0697 123.542 13.1595 123.578 13.3056C123.738 13.9272 123.881 14.5098 124.021 15.0931L124.071 15.302C124.152 15.6314 124.235 15.9715 124.293 16.3092C124.337 16.5625 124.258 16.8254 124.07 17.0494C123.868 17.2889 123.569 17.444 123.31 17.444L123.278 17.4434Z" fill="#111111"></path>
<path d="M122.561 10.8476C122.519 10.8476 122.466 10.8291 122.442 10.8063C122.338 10.7069 122.24 10.6015 122.142 10.4956C122.079 10.4279 122.016 10.3602 121.951 10.2944C121.868 10.2075 121.781 10.1225 121.695 10.0375C121.579 9.9231 121.463 9.80872 121.351 9.69016C120.947 9.26141 120.545 8.82966 120.147 8.39493C119.767 7.98055 119.899 7.45241 120.184 7.13743C120.405 6.89131 120.647 6.76497 120.922 6.75179L121.013 6.75C121.273 6.75 121.45 6.80689 121.664 6.95898C121.827 7.07515 121.896 7.25539 121.947 7.43384C122.027 7.72007 122.105 8.0069 122.181 8.29434L122.377 9.02249C122.456 9.32008 122.536 9.6177 122.621 9.9135C122.666 10.0716 122.728 10.2255 122.789 10.3782C122.837 10.4979 122.844 10.5973 122.809 10.674C122.775 10.7476 122.7 10.8045 122.587 10.8441L122.561 10.8476Z" fill="#111111"></path>
<path d="M127.409 15.3424C127.368 15.3424 127.328 15.3401 127.286 15.3353C127.146 15.3185 127.001 15.2544 126.915 15.173C126.712 14.9796 126.522 14.7718 126.332 14.564L125.966 14.1652C125.88 14.0712 125.794 13.9778 125.706 13.8856L125.324 13.4897C125.152 13.3131 124.98 13.1364 124.812 12.958C124.757 12.9005 124.709 12.8388 124.659 12.7766C124.615 12.7215 124.571 12.6664 124.523 12.6143C124.458 12.5424 124.487 12.4975 124.547 12.4173C124.564 12.3969 124.579 12.376 124.591 12.3556C124.603 12.3358 124.667 12.3047 124.72 12.3047C124.938 12.364 125.134 12.4298 125.33 12.4963L125.641 12.6005C125.824 12.6592 126.519 12.8772 126.519 12.8772C126.681 12.9281 126.846 12.9688 127.011 13.0089C127.334 13.0879 127.667 13.17 127.963 13.3346C128.298 13.5221 128.494 13.8083 128.512 14.1406C128.476 14.7287 128.053 15.3424 127.409 15.3424Z" fill="#111111"></path>
<path d="M119.301 15.091C118.801 15.0617 118.475 14.7964 118.359 14.3234C118.3 14.0796 118.262 13.818 118.392 13.5563C118.502 13.3335 118.686 13.1874 118.938 13.1216C119.318 13.0233 119.701 12.9347 120.083 12.8461C120.31 12.7934 120.535 12.7413 120.76 12.6868C121.015 12.6251 121.268 12.5611 121.523 12.4964L122.001 12.3754C122.109 12.3479 122.174 12.3311 122.238 12.3281C122.327 12.3281 122.383 12.3748 122.419 12.47C122.456 12.5659 122.461 12.618 122.402 12.673C122.129 12.9209 121.255 13.7263 121.255 13.7263L120.72 14.2234C120.546 14.3874 120.371 14.5509 120.201 14.721C119.972 14.9515 119.677 15.0725 119.301 15.091Z" fill="#111111"></path>
<path d="M120.916 16.7549C120.638 16.7549 120.404 16.6519 120.257 16.4651C120.195 16.3879 120.144 16.304 120.101 16.2154C119.986 15.9729 119.955 15.7124 120.012 15.4819C120.059 15.2933 120.164 15.1148 120.315 14.9657L120.656 14.625C120.812 14.4675 120.969 14.3094 121.128 14.1549C121.577 13.7214 122.028 13.292 122.482 12.8645C122.543 12.8076 122.592 12.7812 122.638 12.7812C122.661 12.7812 122.684 12.7878 122.707 12.801C122.9 12.9094 122.914 12.9609 122.867 13.1076L122.187 15.2448L122.1 15.5238C122.055 15.6717 122.009 15.8196 121.96 15.9663C121.898 16.1519 121.809 16.3088 121.697 16.4322C121.516 16.631 121.217 16.7549 120.916 16.7549Z" fill="#111111"></path>
<path d="M122.174 11.3324C122.14 11.3324 122.102 11.3222 122.065 11.3121C121.811 11.2438 121.568 11.1713 121.324 11.0989C121.165 11.051 120.374 10.821 120.374 10.821C119.943 10.6947 119.497 10.5635 119.053 10.4588C118.624 10.3576 118.402 10.0522 118.393 9.55216C118.375 9.34677 118.468 9.10844 118.647 8.89346C118.725 8.80006 118.811 8.7234 118.902 8.66593C119.01 8.59826 119.149 8.55274 119.326 8.52641C119.375 8.51921 119.423 8.51562 119.471 8.51562C119.587 8.51562 119.694 8.53838 119.789 8.58329C119.874 8.62341 119.962 8.68987 120.06 8.78568C120.347 9.07072 121.518 10.2414 121.819 10.5396C121.933 10.6528 122.057 10.7558 122.181 10.8588L122.259 10.9228C122.343 10.9941 122.368 11.0612 122.283 11.2522C122.251 11.3228 122.21 11.3324 122.174 11.3324Z" fill="#111111"></path>
<path d="M123.437 10.7227C124.111 10.7072 124.44 11.1395 124.462 11.7413C124.483 12.3012 124.025 12.7569 123.482 12.7671C122.861 12.7797 122.423 12.2503 122.442 11.6018C122.453 11.218 122.93 10.6665 123.437 10.7227Z" fill="#111111"></path>
</svg>
</div>
<p class="font-abc text-more-13">箱根</p>
</div>
<div class="grid grid-cols-1 gap-y-[3px] lg:gap-y-[5px]">
<a class="btn-black -bg-w w-full ml-auto" href="https://www.gorakadan.com/hakone/" rel="noopener noreferrer" target="_blank">
<span class="btn-black__bg"></span>
<span class="btn-black__text" data-name="btn-blue-text"><div style="position:relative;display:inline-block;">詳</div><div style="position:relative;display:inline-block;">細</div><div style="position:relative;display:inline-block;">を</div><div style="position:relative;display:inline-block;">見</div><div style="position:relative;display:inline-block;">る</div></span>
</a>
<a class="btn-black w-full ml-auto" href="https://go-gorakadan-group.reservation.jp/ja/hotels/gorakadan" rel="noopener noreferrer" target="_blank">
<span class="btn-black__bg"></span>
<span class="btn-black__text" data-name="btn-blue-text"><div style="position:relative;display:inline-block;">予</div><div style="position:relative;display:inline-block;">約</div><div style="position:relative;display:inline-block;">を</div><div style="position:relative;display:inline-block;">申</div><div style="position:relative;display:inline-block;">し</div><div style="position:relative;display:inline-block;">込</div><div style="position:relative;display:inline-block;">む</div></span>
</a>
</div>
</div>
<div class="grid grid-cols-[auto_44%] items-start mt-[16px] lg:grid-cols-[auto_33%]">
<div class="grid grid-cols-1 justify-start gap-y-[19px] lg:gap-y-[8px]">
<div class="w-[86%] lg:w-1/2">
<svg fill="none" height="28" viewbox="0 0 244 28" width="244" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_7202_14065)">
<path d="M32.846 8.39062C26.6628 8.39062 22 12.6408 22 18.2772C22 23.9285 26.6628 28.1902 32.846 28.1902C39.0292 28.1902 43.6926 23.9285 43.6926 18.2772C43.6926 12.6408 39.0292 8.39062 32.846 8.39062ZM40.8627 18.2509C40.8627 23.5513 37.5663 27.2525 32.846 27.2525C28.1257 27.2525 24.8288 23.5513 24.8288 18.2509C24.8288 12.9971 28.1257 9.32774 32.846 9.32774C37.5663 9.32774 40.8627 12.9971 40.8627 18.2509Z" fill="#352F2D"></path>
<path d="M60.8476 24.6447L56.9741 18.4966C60.2298 17.6743 62.0925 15.901 62.0925 13.6123C62.0925 8.9383 57.3723 8.54688 52.5381 8.54688H45.8672L46.3418 8.80874C47.3334 9.35498 47.4153 10.2586 47.4153 11.2149V25.3353C47.4153 26.4931 47.1175 27.2392 46.4492 27.7536L46.1248 28.0039H51.2275L50.9032 27.7536C50.2354 27.2392 49.9376 26.4931 49.9376 25.3353V19.0209H52.4459C53.2883 19.0209 54.0016 18.9891 54.6221 18.9237L60.3535 28.0039H63.8668L63.4074 27.7431C62.4332 27.1898 60.8639 24.6705 60.8476 24.6447ZM59.3239 13.586C59.3239 14.7191 58.96 15.66 58.2418 16.3825C57.1168 17.5139 55.1217 18.1101 52.4659 18.1101C52.4285 18.1101 52.3905 18.1101 52.3531 18.1096H49.9376V9.48399H52.2598C56.3964 9.51034 59.3239 9.82051 59.3239 13.586Z" fill="#352F2D"></path>
<path d="M84.3094 25.6504L77.2311 8.63252L77.1953 8.54688H74.5813L67.4993 25.6241C67.0366 26.7352 66.7713 27.2891 65.8107 27.7382L65.2422 28.0039H69.4987L69.0203 27.742C68.4187 27.4126 68.1507 26.9669 68.6764 25.758L70.0872 22.3664H80.336L81.7474 25.7844V25.7849C82.1949 26.8587 82.113 27.3709 81.4339 27.742L80.9555 28.0039H86.3934L85.9828 27.7459C85.0232 27.1447 84.5985 26.3493 84.3094 25.6504ZM79.9748 21.4287H70.4794L75.2274 9.91659L79.9748 21.4287Z" fill="#352F2D"></path>
<path d="M178.368 25.6504L171.29 8.63252L171.254 8.54688H168.64L161.557 25.6241C161.095 26.7352 160.829 27.2897 159.869 27.7382L159.3 28.0039H163.556L163.078 27.742C162.477 27.4126 162.21 26.9663 162.735 25.758L164.145 22.3664H174.395L175.806 25.7844V25.7849C176.253 26.8582 176.171 27.3704 175.493 27.742L175.015 28.0039H180.452L180.041 27.7459C179.081 27.1442 178.657 26.3487 178.368 25.6504ZM174.033 21.4287H164.538L169.286 9.91659L174.033 21.4287Z" fill="#352F2D"></path>
<path d="M136.617 25.6504L129.54 8.63252L129.504 8.54688H126.89L119.808 25.6241C119.345 26.7352 119.079 27.2897 118.119 27.7382L117.551 28.0039H121.806L121.328 27.742C120.727 27.4126 120.46 26.9663 120.985 25.758L122.395 22.3664H132.645L134.055 25.7844V25.7849C134.504 26.8582 134.422 27.3704 133.742 27.742L133.265 28.0039H138.701L138.291 27.7459C137.331 27.1442 136.907 26.3487 136.617 25.6504ZM132.283 21.4287H122.788L127.535 9.91659L132.283 21.4287Z" fill="#352F2D"></path>
<path d="M112.9 24.5371L106.185 16.3633L114.28 8.54688H110.801L111.145 8.79886C111.398 8.98441 111.555 9.21993 111.566 9.42909C111.574 9.58665 111.502 9.73597 111.349 9.87596L101.181 19.689V11.2149C101.181 10.073 101.488 9.32753 102.177 8.79721L102.502 8.54688H97.3369L97.6624 8.79721C98.3301 9.31106 98.6279 10.0571 98.6279 11.2149V25.3353C98.6279 26.4931 98.3301 27.2392 97.6618 27.7536L97.3375 28.0039H102.501L102.177 27.7536C101.488 27.2227 101.181 26.4766 101.181 25.3353V21.2064L104.509 17.9789L112.692 27.9534L112.733 28.0039H116.648L116.145 27.7409C114.84 27.0591 112.923 24.5656 112.9 24.5371Z" fill="#352F2D"></path>
<path d="M148.402 8.54688H140.419L140.744 8.79721C141.211 9.15679 141.833 9.79855 141.833 11.2149V25.3353C141.833 26.7511 141.211 27.3934 140.744 27.7536L140.419 28.0039H148.402C154.74 28.0039 159.34 23.9458 159.34 18.3544C159.34 12.5802 154.842 8.54688 148.402 8.54688ZM156.511 18.3281C156.511 23.4737 153.164 27.0668 148.372 27.0668H144.385V9.48399H148.372C153.316 9.48399 156.511 12.9552 156.511 18.3281Z" fill="#352F2D"></path>
<path d="M197.432 8.54688L197.757 8.79721C198.446 9.32808 198.753 10.0736 198.753 11.2149V24.34L184.777 8.59354L184.736 8.54688H182.292L182.617 8.79721C183.285 9.31106 183.582 10.0566 183.582 11.2149V25.3353C183.582 26.4931 183.285 27.2392 182.617 27.7536L182.293 28.0039H186.134L185.809 27.7536C185.141 27.2392 184.843 26.4931 184.843 25.3353V12.0016L198.912 27.9572L198.952 28.0039H199.983V11.2149C199.983 10.0566 200.281 9.31106 200.949 8.79721L201.273 8.54688H197.432Z" fill="#352F2D"></path>
<path d="M15.198 18.6538C16.0219 19.2495 16.0149 20.7268 16.004 22.9628C16.0029 23.2334 16.0013 23.5156 16.0013 23.8093C16.0013 25.8812 13.8723 27.3849 10.9388 27.3849C6.23964 27.3849 2.82935 23.4992 2.82935 18.146C2.85972 12.7929 6.30419 9.19653 11.3998 9.19653C13.5392 9.19653 15.4746 10.2072 16.5774 11.9014L16.7097 12.1039L17.7339 10.0057L17.627 9.9393C15.9574 8.89734 13.8804 8.39063 11.2772 8.39063C4.74306 8.39062 0 12.5486 0 18.2772C0 23.9285 4.64976 28.1902 10.8151 28.1902C13.7703 28.1902 15.8062 27.3986 17.0357 26.6936L18.023 28.1847H18.4651V25.2394L18.467 25.0053L18.4922 22.1761C18.4922 20.7482 18.6956 19.0727 19.2662 18.6533L19.6096 18.4013H14.8481L15.198 18.6538Z" fill="#352F2D"></path>
<path d="M37.3937 5.44644L33.6385 0H32.0546L28.2993 5.44644L28.2744 5.51397V5.69732H28.9194L32.8465 1.38013L36.7737 5.69732H37.4186V5.51397L37.3937 5.44644Z" fill="#352F2D"></path>
<path d="M212.57 12.7208C212.862 12.9489 212.961 13.2962 212.961 14.0977V22.4663C212.961 23.2677 212.862 23.615 212.569 23.8437L212.217 24.1192H215.358L215.006 23.8437C214.704 23.6076 214.597 23.247 214.597 22.4663V18.2639H217.524C218.363 18.2639 218.811 18.3453 219.07 18.5452L219.314 18.733V17.0976L219.07 17.2854C218.811 17.4848 218.363 17.5662 217.524 17.5662H214.597V13.1431H218.027C218.906 13.1431 220.052 13.4324 220.291 13.8477L220.335 13.9238H220.628V12.4453H212.217L212.57 12.7208Z" fill="#352F2D"></path>
<path d="M231.441 12.7208C231.77 12.9771 231.904 13.3765 231.904 14.0977V19.7344C231.904 22.236 230.74 23.4523 228.347 23.4523C225.875 23.4523 224.772 22.2573 224.772 19.5785L224.79 14.0977C224.79 13.376 224.924 12.9771 225.252 12.7208L225.606 12.4453H222.32L222.673 12.7208C223.002 12.9771 223.136 13.376 223.136 14.0977V19.5626C223.136 22.7019 224.799 24.2282 228.221 24.2282C231.109 24.2282 232.765 22.5961 232.765 19.7498V14.0977C232.765 13.3765 232.899 12.9771 233.228 12.7208L233.581 12.4453H231.088L231.441 12.7208Z" fill="#352F2D"></path>
<path d="M235.932 12.713C236.234 12.9486 236.341 13.3086 236.341 14.0898V21.6937C236.341 22.8866 236.004 23.8205 235.502 24.0167L235.336 24.0821L235.548 24.4501L235.648 24.4379C237.106 24.2544 237.977 23.1647 237.977 21.522V14.0898C237.977 13.2884 238.076 12.9406 238.367 12.713L238.721 12.4375H235.579L235.932 12.713Z" fill="#352F2D"></path>
<path d="M243.635 12.713L243.988 12.4375H240.847L241.2 12.713C241.492 12.9411 241.591 13.2884 241.591 14.0898V22.4581C241.591 23.2595 241.492 23.6067 241.199 23.8354L240.847 24.1109H243.987L243.635 23.8354C243.333 23.5998 243.226 23.2393 243.226 22.4581V14.0898C243.226 13.3086 243.333 12.948 243.635 12.713Z" fill="#352F2D"></path>
</g>
<defs>
<clippath id="clip0_7202_14065">
<rect fill="white" height="28" width="244"></rect>
</clippath>
</defs>
</svg>
</div>
<p class="font-abc text-more-13">富士</p>
</div>
<div class="grid grid-cols-1 gap-y-[3px]">
<a class="btn-black -bg-w w-full ml-auto" href="https://www.gorakadan.com/fuji/" rel="noopener noreferrer" target="_blank">
<span class="btn-black__bg"></span>
<span class="btn-black__text" data-name="btn-blue-text"><div style="position:relative;display:inline-block;">詳</div><div style="position:relative;display:inline-block;">細</div><div style="position:relative;display:inline-block;">を</div><div style="position:relative;display:inline-block;">見</div><div style="position:relative;display:inline-block;">る</div></span>
</a>
<a class="btn-black w-full ml-auto" href="https://go-gorakadan-group.reservation.jp/ja/hotels/gorakadan-fuji" rel="noopener noreferrer" target="_blank">
<span class="btn-black__bg"></span>
<span class="btn-black__text" data-name="btn-blue-text"><div style="position:relative;display:inline-block;">予</div><div style="position:relative;display:inline-block;">約</div><div style="position:relative;display:inline-block;">を</div><div style="position:relative;display:inline-block;">申</div><div style="position:relative;display:inline-block;">し</div><div style="position:relative;display:inline-block;">込</div><div style="position:relative;display:inline-block;">む</div></span>
</a>
</div>
</div>
</div>
</div>
<div class="hidden absolute bottom-[24px] left-more-40 grid-cols-1 gap-y-[10px] lg:grid">
<div class="flex items-center gap-x-[16px]">
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.facebook.com/gorakadan" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="22" viewbox="0 0 23 22" width="23" xmlns="http://www.w3.org/2000/svg">
<path d="M23 11.0444C23 4.94472 17.8513 0 11.5 0C5.14872 0 0 4.94472 0 11.0444C0 16.5568 4.20536 21.126 9.70312 21.9545V14.2369H6.7832V11.0444H9.70312V8.61115C9.70312 5.84316 11.42 4.3142 14.0468 4.3142C15.3051 4.3142 16.6211 4.52991 16.6211 4.52991V7.24786H15.171C13.7425 7.24786 13.2969 8.09927 13.2969 8.97268V11.0444H16.4863L15.9765 14.2369H13.2969V21.9545C18.7946 21.126 23 16.557 23 11.0444Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.instagram.com/gorakadan/" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="21" viewbox="0 0 21 21" width="21" xmlns="http://www.w3.org/2000/svg">
<path d="M10.4844 2.23245C13.1722 2.23245 13.491 2.24252 14.553 2.29117C17.281 2.41532 18.5552 3.7097 18.6794 6.41758C18.728 7.47876 18.7373 7.79753 18.7373 10.4853C18.7373 13.1739 18.7272 13.4918 18.6794 14.553C18.5544 17.2583 17.2835 18.5552 14.553 18.6794C13.491 18.728 13.1739 18.7381 10.4844 18.7381C7.79669 18.7381 7.47792 18.728 6.41675 18.6794C3.68202 18.5544 2.41449 17.2541 2.29033 14.5521C2.24168 13.491 2.23161 13.173 2.23161 10.4844C2.23161 7.79669 2.24252 7.47876 2.29033 6.41675C2.41532 3.7097 3.68622 2.41449 6.41675 2.29033C7.47876 2.24252 7.79669 2.23245 10.4844 2.23245ZM10.4844 0.417969C7.75055 0.417969 7.40829 0.429713 6.33454 0.478368C2.67873 0.646142 0.646981 2.67454 0.479206 6.3337C0.429713 7.40829 0.417969 7.75055 0.417969 10.4844C0.417969 13.2183 0.429713 13.5614 0.478368 14.6352C0.646142 18.291 2.67454 20.3227 6.3337 20.4905C7.40829 20.5392 7.75055 20.5509 10.4844 20.5509C13.2183 20.5509 13.5614 20.5392 14.6352 20.4905C18.2876 20.3227 20.3244 18.2943 20.4897 14.6352C20.5392 13.5614 20.5509 13.2183 20.5509 10.4844C20.5509 7.75055 20.5392 7.40829 20.4905 6.33454C20.3261 2.68209 18.2952 0.646981 14.636 0.479206C13.5614 0.429713 13.2183 0.417969 10.4844 0.417969ZM10.4844 5.31531C7.62975 5.31531 5.31531 7.62975 5.31531 10.4844C5.31531 13.3391 7.62975 15.6544 10.4844 15.6544C13.3391 15.6544 15.6536 13.34 15.6536 10.4844C15.6536 7.62975 13.3391 5.31531 10.4844 5.31531ZM10.4844 13.8399C8.63137 13.8399 7.12895 12.3383 7.12895 10.4844C7.12895 8.63137 8.63137 7.12895 10.4844 7.12895C12.3375 7.12895 13.8399 8.63137 13.8399 10.4844C13.8399 12.3383 12.3375 13.8399 10.4844 13.8399ZM15.8583 3.90348C15.1905 3.90348 14.6494 4.44456 14.6494 5.11146C14.6494 5.77836 15.1905 6.31944 15.8583 6.31944C16.5252 6.31944 17.0654 5.77836 17.0654 5.11146C17.0654 4.44456 16.5252 3.90348 15.8583 3.90348Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[24px] lg:w-more-24 -rc" href="https://www.relaischateaux.com/us/japan/gora-hakone-hakone" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="28" viewbox="0 0 27 28" width="27" xmlns="http://www.w3.org/2000/svg">
<path d="M11.6072 11.5435C10.3255 11.9304 8.78743 12.5753 8.33884 13.9941C8.14658 14.7679 8.40292 15.5096 9.01172 15.9932C9.68461 16.5414 10.71 16.7026 11.4469 16.3479C11.9276 16.1222 12.3121 15.7353 12.4723 15.1549C11.9917 15.5096 11.2226 15.5096 10.742 15.1549C10.3575 14.8324 10.1332 14.381 10.1332 13.8651C10.2293 12.8978 11.2547 12.1561 12.0878 11.8982C12.3762 11.8015 12.6966 11.737 13.017 11.737C12.8248 12.7043 11.9917 13.7039 11.1265 14.1553C11.1265 14.1553 11.8314 14.4455 12.9209 14.0908C12.9209 14.0908 13.0811 15.2194 13.6258 16.2512C14.2026 15.2194 14.3307 14.0908 14.3307 14.0908C15.4202 14.4455 16.1251 14.1553 16.1251 14.1553C15.26 13.6716 14.4269 12.6721 14.2346 11.737C14.555 11.737 14.8755 11.8337 15.1638 11.8982C16.029 12.1561 17.0543 12.8655 17.1184 13.8651C17.1505 14.381 16.9262 14.8324 16.5096 15.1549C16.029 15.5096 15.26 15.5096 14.7793 15.1549C14.9395 15.7353 15.3241 16.1222 15.8047 16.3479C16.5417 16.7026 17.5991 16.5091 18.2399 15.9932C18.8487 15.5096 19.105 14.7679 18.9128 13.9941C18.4642 12.5753 16.9262 11.9304 15.6445 11.5435C17.6311 11.35 19.5216 10.8341 20.9635 9.38312C22.0529 8.22232 22.7258 6.6101 22.117 5.03013C21.7325 3.93382 20.6431 3.22444 19.5536 3.15995C16.9262 2.99873 15.7086 5.64277 15.5804 6.48112C15.5804 6.48112 16.9903 4.73993 18.9448 5.19135C19.5536 5.32033 20.1304 5.83624 20.3227 6.48112C20.611 7.64192 20.1304 8.73823 19.2653 9.54434C18.0156 10.5762 16.4776 10.9953 14.8755 11.1243C14.9075 10.2537 15.1318 9.47985 15.6765 8.83497C16.061 8.38355 16.6057 8.09335 17.1184 8.19008C17.5991 8.28681 17.7593 8.73823 17.7272 9.06068C17.5991 9.57659 17.1505 9.89903 17.1505 9.89903C18.0477 9.8023 18.9448 9.06068 19.1691 8.22232C19.4255 7.15826 18.5603 5.99746 17.1184 6.3199C15.0357 6.80357 14.3948 9.2219 14.2987 11.1888C14.2346 11.1888 14.1705 11.1888 14.0744 11.1888C13.5938 8.9317 14.7473 7.06152 15.1959 5.06237C15.6124 3.32117 14.6832 1.41875 13.5938 0C12.4723 1.38651 11.5751 3.28893 11.9596 5.06237C12.4082 7.06152 13.5617 8.9317 13.0811 11.1888C13.017 11.1888 12.9529 11.1888 12.8568 11.1888C12.7927 9.2219 12.1519 6.80357 10.0691 6.3199C8.62722 5.99746 7.73003 7.15826 8.01841 8.22232C8.24271 9.06068 9.13989 9.8023 10.0371 9.89903C10.0371 9.89903 9.62053 9.57659 9.55644 9.12517C9.5244 8.80272 9.71665 8.3513 10.1652 8.25457C10.6779 8.15783 11.2226 8.44803 11.6072 8.89946C12.1519 9.54434 12.3441 10.3182 12.4082 11.1888C10.8061 11.0598 9.26806 10.6407 8.01841 9.60883C7.15327 8.80272 6.70468 7.70641 6.96102 6.54561C7.12123 5.90073 7.73003 5.38482 8.33884 5.25584C10.2934 4.80442 11.7033 6.54561 11.7033 6.54561C11.5751 5.70726 10.3575 3.09546 7.73003 3.22444C6.64059 3.28893 5.55116 3.99831 5.16665 5.09462C4.5258 6.67459 5.23073 8.31906 6.32017 9.44761C7.73003 10.8341 9.62053 11.35 11.6072 11.5435Z" fill="black"></path>
<path d="M23.5576 21.8953L23.9742 22.508H23.077L22.9488 22.3145C22.6605 22.4757 22.34 22.5725 22.0196 22.5725C21.3467 22.5725 20.834 22.1533 20.834 21.5084C20.834 20.9603 21.1224 20.6701 21.6031 20.3799C21.3788 20.1219 21.2826 19.9284 21.2826 19.606C21.2826 19.0256 21.6992 18.7354 22.2439 18.7354C22.7566 18.7354 23.1411 19.0901 23.1411 19.606C23.1411 19.9607 22.8848 20.3154 22.5964 20.5088L23.1411 21.3149C23.3333 21.1537 23.4936 20.9603 23.6217 20.8313L24.0383 21.3149C23.9421 21.5407 23.7499 21.7341 23.5576 21.8953ZM22.5964 21.8309L21.9876 20.928C21.6992 21.0892 21.6031 21.2182 21.6031 21.5084C21.6031 21.7986 21.8274 21.9598 22.1798 21.9598C22.308 21.9598 22.4682 21.8953 22.5964 21.8309ZM22.0196 19.6382C22.0196 19.7672 22.1157 19.9284 22.2759 20.0897C22.4682 19.9607 22.5964 19.7995 22.5964 19.6382C22.5964 19.4448 22.5002 19.348 22.308 19.348C22.1157 19.348 22.0196 19.4448 22.0196 19.6382Z" fill="black"></path>
<path d="M5.13287 22.5449L4.10751 20.8037V22.5449H3.27441V18.8046H4.58815C5.35716 18.8046 5.86984 19.2882 5.86984 19.9009C5.86984 20.449 5.51737 20.836 5.03674 20.9649L6.03005 22.5127H5.13287V22.5449ZM4.62019 19.5139H4.10751V20.4168H4.62019C4.90857 20.4168 5.06878 20.2233 5.06878 19.9331C5.06878 19.6752 4.87653 19.5139 4.62019 19.5139Z" fill="black"></path>
<path d="M9.26246 22.5449V18.8046H10.0635V21.8355H11.3772V22.5449H9.26246Z" fill="black"></path>
<path d="M14.2314 22.5353L13.943 21.6002H12.8215L12.5331 22.5353H11.668L12.9817 18.8271H13.7507L15.0965 22.5353H14.2314ZM13.4623 19.9879C13.4303 19.859 13.3662 19.6977 13.3662 19.6977C13.3662 19.6977 13.3342 19.859 13.3021 19.9879L13.0137 20.8908H13.7507L13.4623 19.9879Z" fill="black"></path>
<path d="M16.3763 18.8047H15.5752V22.5128H16.3763V18.8047Z" fill="black"></path>
<path d="M18.0777 22.5656C17.5651 22.5656 17.1805 22.3721 16.8281 22.1142L17.2446 21.5338C17.4689 21.695 17.7894 21.8562 18.0777 21.8562C18.4622 21.8562 18.5584 21.7272 18.5584 21.5015C18.5584 21.1146 17.8855 21.0179 17.5651 20.8244C17.1805 20.6309 16.8922 20.373 16.8922 19.7926C16.8922 19.0187 17.5651 18.7285 18.2059 18.7285C18.5584 18.7285 18.9749 18.8252 19.2633 19.051L18.9108 19.6636C18.7506 19.5346 18.4622 19.4379 18.2059 19.4379C17.8214 19.4379 17.6932 19.6636 17.6932 19.7926C17.6932 20.115 18.1739 20.244 18.5904 20.4052C18.9429 20.5664 19.3274 20.8244 19.3274 21.437C19.3915 22.1787 18.8788 22.5656 18.0777 22.5656Z" fill="black"></path>
<path d="M2.50378 27.2375C1.3823 27.2375 0.613281 26.4959 0.613281 25.3028C0.613281 24.1743 1.44638 23.4004 2.47173 23.4004C3.01645 23.4004 3.433 23.5616 3.78547 23.9485L3.33688 24.4967C3.08054 24.2387 2.79216 24.1098 2.47173 24.1098C1.83089 24.1098 1.44638 24.6257 1.44638 25.3028C1.44638 26.0767 1.83089 26.5281 2.50378 26.5281C2.95237 26.5281 3.20871 26.3346 3.433 26.1089L3.91364 26.6571C3.56117 27.0118 3.14462 27.2375 2.50378 27.2375Z" fill="black"></path>
<path d="M10.2274 27.17L9.93907 26.2349H8.81758L8.5292 27.17H7.66406L8.9778 23.4619H9.74681L11.0926 27.17H10.2274ZM9.45843 24.655C9.42639 24.526 9.3623 24.3648 9.3623 24.3648C9.3623 24.3648 9.33026 24.526 9.29822 24.655L9.00984 25.5578H9.71477L9.45843 24.655Z" fill="black"></path>
<path d="M10.9951 23.4766V24.1859H11.7321V27.1847H12.5331V24.1859H13.2381V23.4766H10.9951Z" fill="black"></path>
<path d="M18.8466 27.1925L18.5582 26.2574H17.4367L17.1483 27.1925H16.2832L17.5969 23.4844H18.366L19.7117 27.1925H18.8466ZM18.0776 24.6774C18.0455 24.5484 17.9814 24.3872 17.9814 24.3872C17.9814 24.3872 17.9494 24.5484 17.9174 24.6774L17.629 25.5803H18.3339L18.0776 24.6774Z" fill="black"></path>
<path d="M21.442 27.257C20.6089 27.257 19.872 26.7088 19.872 25.677V23.4844H20.673V25.7092C20.673 26.2252 20.9935 26.5798 21.442 26.5798C21.8906 26.5798 22.2431 26.2252 22.2431 25.7092V23.5166H23.0442V25.7092C23.0442 26.7088 22.3072 27.257 21.442 27.257Z" fill="black"></path>
<path d="M25.4465 23.4766L24.9017 24.4439L24.357 23.4766H23.4598L24.4531 25.1855L23.2676 27.1847H24.1648L24.9017 25.8949L25.6708 27.1847H26.5679L25.3503 25.1855L26.3436 23.4766H25.4465Z" fill="black"></path>
<path d="M13.6582 23.4766V27.1847H15.9012V26.5075H14.4913V25.7014H15.805V24.9921H14.4913V24.1859H15.805V23.4766H13.6582Z" fill="black"></path>
<path d="M7.24735 21.8357V21.0296H8.59312V20.3202H7.24735V19.5141H8.59312V18.8047H6.44629V22.545H8.65721V21.8357H7.24735Z" fill="black"></path>
<path d="M6.4478 23.4766V24.9921H5.16611V23.4766H4.33301V27.1847H5.16611V25.7014H6.4478V27.1847H7.24886V23.4766H6.4478Z" fill="black"></path>
</svg>
</a>
</div>
<a class="link-arrow text-more-12 lg:text-more-13 flex items-center gap-x-[.4em]" href="https://www.gorakadan.com/recruit">
<span>採用について</span>
<span class="link-arrow_icon block w-5 h-5 leading-[0] overflow-hidden">
<svg fill="none" height="14" viewbox="0 0 14 14" width="14" xmlns="http://www.w3.org/2000/svg">
<path d="M2.16602 11.5234L11.166 2.52344" stroke="black"></path>
<path d="M7.11523 2.25391L11.4828 2.2596L11.4885 6.62718" stroke="black"></path>
</svg>
</span>
</a> </div>
</div>
<div class="relative mt-[45px] pt-[45px] lg:col-start-1 lg:row-start-1 lg:mt-0 lg:pt-0">
<div class="absolute top-0 left-0 w-full h-[1px] bg-gray01 opacity-50 lg:left-auto lg:right-0 lg:w-[1px] lg:h-full"></div>
<div class="absolute top-[50px] right-[24px] lg:right-more-40 lg:top-more-40">
<h3 class="[writing-mode:vertical-lr] lg:text-more-20 text-more-16 tracking-[0.4em]">強羅花壇について</h3>
</div>
<div class="scrollable-area lg:h-screen lg:overflow-y-scroll lg:py-more-40">
<div class="reject-lenis" data-name="reject-lenis">
<div class="w-[70%] relative h-auto mb-more-50 lg:w-[52%]">
<img alt="image of residence" class="w-full h-auto" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2l1200004354s70017.webp" style="mix-blend-mode: multiply;"/>
</div>
<p class="w-[90%] text-more-13 tracking-[0.06em] leading-loose lg:text-more-13 lg:mt-[5em]">古くは閑院宮別邸としての起源をもつ強羅花壇。<br class="hidden lg:block"/>
        強羅花壇にはその当時から続く、<br class="hidden lg:block"/>
        心を込めてひとをもてなす場としての原点があります―<br/><br/>
        強羅花壇とは―<br class="hidden lg:block"/>
        創業の地「強羅」と<br class="hidden lg:block"/>
        西日本を拠点とした旧宮家が国賓や要人に向けて自邸を開放して<br class="hidden lg:block"/>
        自らもてなす迎賓の場として使われていた言葉「花壇」を<br class="hidden lg:block"/>
        紡いだ言葉でありその起源は一九四八年にまで遡ります。<br/><br/>
        私たちが今なお引き継いでいる強羅花壇という名称は<br class="hidden lg:block"/>
        閑院宮載仁親王が迎賓の場として別邸を開放し、<br class="hidden lg:block"/>
        ご自身の大切な来賓をもてなそうと<br class="hidden lg:block"/>
        決意された際に命名されたものです。<br/><br/>
        私たち花壇人は「強羅花壇」という言葉の起源そのものに<br class="hidden lg:block"/>
        強羅花壇の在り方が伝承されているように思います。</p>
<div class="flex justify-between items-center mt-[100px] mr-[5vw] lg:hidden">
<div class="flex items-center gap-x-[16px]">
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.facebook.com/gorakadan" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="22" viewbox="0 0 23 22" width="23" xmlns="http://www.w3.org/2000/svg">
<path d="M23 11.0444C23 4.94472 17.8513 0 11.5 0C5.14872 0 0 4.94472 0 11.0444C0 16.5568 4.20536 21.126 9.70312 21.9545V14.2369H6.7832V11.0444H9.70312V8.61115C9.70312 5.84316 11.42 4.3142 14.0468 4.3142C15.3051 4.3142 16.6211 4.52991 16.6211 4.52991V7.24786H15.171C13.7425 7.24786 13.2969 8.09927 13.2969 8.97268V11.0444H16.4863L15.9765 14.2369H13.2969V21.9545C18.7946 21.126 23 16.557 23 11.0444Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.instagram.com/gorakadan/" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="21" viewbox="0 0 21 21" width="21" xmlns="http://www.w3.org/2000/svg">
<path d="M10.4844 2.23245C13.1722 2.23245 13.491 2.24252 14.553 2.29117C17.281 2.41532 18.5552 3.7097 18.6794 6.41758C18.728 7.47876 18.7373 7.79753 18.7373 10.4853C18.7373 13.1739 18.7272 13.4918 18.6794 14.553C18.5544 17.2583 17.2835 18.5552 14.553 18.6794C13.491 18.728 13.1739 18.7381 10.4844 18.7381C7.79669 18.7381 7.47792 18.728 6.41675 18.6794C3.68202 18.5544 2.41449 17.2541 2.29033 14.5521C2.24168 13.491 2.23161 13.173 2.23161 10.4844C2.23161 7.79669 2.24252 7.47876 2.29033 6.41675C2.41532 3.7097 3.68622 2.41449 6.41675 2.29033C7.47876 2.24252 7.79669 2.23245 10.4844 2.23245ZM10.4844 0.417969C7.75055 0.417969 7.40829 0.429713 6.33454 0.478368C2.67873 0.646142 0.646981 2.67454 0.479206 6.3337C0.429713 7.40829 0.417969 7.75055 0.417969 10.4844C0.417969 13.2183 0.429713 13.5614 0.478368 14.6352C0.646142 18.291 2.67454 20.3227 6.3337 20.4905C7.40829 20.5392 7.75055 20.5509 10.4844 20.5509C13.2183 20.5509 13.5614 20.5392 14.6352 20.4905C18.2876 20.3227 20.3244 18.2943 20.4897 14.6352C20.5392 13.5614 20.5509 13.2183 20.5509 10.4844C20.5509 7.75055 20.5392 7.40829 20.4905 6.33454C20.3261 2.68209 18.2952 0.646981 14.636 0.479206C13.5614 0.429713 13.2183 0.417969 10.4844 0.417969ZM10.4844 5.31531C7.62975 5.31531 5.31531 7.62975 5.31531 10.4844C5.31531 13.3391 7.62975 15.6544 10.4844 15.6544C13.3391 15.6544 15.6536 13.34 15.6536 10.4844C15.6536 7.62975 13.3391 5.31531 10.4844 5.31531ZM10.4844 13.8399C8.63137 13.8399 7.12895 12.3383 7.12895 10.4844C7.12895 8.63137 8.63137 7.12895 10.4844 7.12895C12.3375 7.12895 13.8399 8.63137 13.8399 10.4844C13.8399 12.3383 12.3375 13.8399 10.4844 13.8399ZM15.8583 3.90348C15.1905 3.90348 14.6494 4.44456 14.6494 5.11146C14.6494 5.77836 15.1905 6.31944 15.8583 6.31944C16.5252 6.31944 17.0654 5.77836 17.0654 5.11146C17.0654 4.44456 16.5252 3.90348 15.8583 3.90348Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[24px] lg:w-more-24 -rc" href="https://www.relaischateaux.com/us/japan/gora-hakone-hakone" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="28" viewbox="0 0 27 28" width="27" xmlns="http://www.w3.org/2000/svg">
<path d="M11.6072 11.5435C10.3255 11.9304 8.78743 12.5753 8.33884 13.9941C8.14658 14.7679 8.40292 15.5096 9.01172 15.9932C9.68461 16.5414 10.71 16.7026 11.4469 16.3479C11.9276 16.1222 12.3121 15.7353 12.4723 15.1549C11.9917 15.5096 11.2226 15.5096 10.742 15.1549C10.3575 14.8324 10.1332 14.381 10.1332 13.8651C10.2293 12.8978 11.2547 12.1561 12.0878 11.8982C12.3762 11.8015 12.6966 11.737 13.017 11.737C12.8248 12.7043 11.9917 13.7039 11.1265 14.1553C11.1265 14.1553 11.8314 14.4455 12.9209 14.0908C12.9209 14.0908 13.0811 15.2194 13.6258 16.2512C14.2026 15.2194 14.3307 14.0908 14.3307 14.0908C15.4202 14.4455 16.1251 14.1553 16.1251 14.1553C15.26 13.6716 14.4269 12.6721 14.2346 11.737C14.555 11.737 14.8755 11.8337 15.1638 11.8982C16.029 12.1561 17.0543 12.8655 17.1184 13.8651C17.1505 14.381 16.9262 14.8324 16.5096 15.1549C16.029 15.5096 15.26 15.5096 14.7793 15.1549C14.9395 15.7353 15.3241 16.1222 15.8047 16.3479C16.5417 16.7026 17.5991 16.5091 18.2399 15.9932C18.8487 15.5096 19.105 14.7679 18.9128 13.9941C18.4642 12.5753 16.9262 11.9304 15.6445 11.5435C17.6311 11.35 19.5216 10.8341 20.9635 9.38312C22.0529 8.22232 22.7258 6.6101 22.117 5.03013C21.7325 3.93382 20.6431 3.22444 19.5536 3.15995C16.9262 2.99873 15.7086 5.64277 15.5804 6.48112C15.5804 6.48112 16.9903 4.73993 18.9448 5.19135C19.5536 5.32033 20.1304 5.83624 20.3227 6.48112C20.611 7.64192 20.1304 8.73823 19.2653 9.54434C18.0156 10.5762 16.4776 10.9953 14.8755 11.1243C14.9075 10.2537 15.1318 9.47985 15.6765 8.83497C16.061 8.38355 16.6057 8.09335 17.1184 8.19008C17.5991 8.28681 17.7593 8.73823 17.7272 9.06068C17.5991 9.57659 17.1505 9.89903 17.1505 9.89903C18.0477 9.8023 18.9448 9.06068 19.1691 8.22232C19.4255 7.15826 18.5603 5.99746 17.1184 6.3199C15.0357 6.80357 14.3948 9.2219 14.2987 11.1888C14.2346 11.1888 14.1705 11.1888 14.0744 11.1888C13.5938 8.9317 14.7473 7.06152 15.1959 5.06237C15.6124 3.32117 14.6832 1.41875 13.5938 0C12.4723 1.38651 11.5751 3.28893 11.9596 5.06237C12.4082 7.06152 13.5617 8.9317 13.0811 11.1888C13.017 11.1888 12.9529 11.1888 12.8568 11.1888C12.7927 9.2219 12.1519 6.80357 10.0691 6.3199C8.62722 5.99746 7.73003 7.15826 8.01841 8.22232C8.24271 9.06068 9.13989 9.8023 10.0371 9.89903C10.0371 9.89903 9.62053 9.57659 9.55644 9.12517C9.5244 8.80272 9.71665 8.3513 10.1652 8.25457C10.6779 8.15783 11.2226 8.44803 11.6072 8.89946C12.1519 9.54434 12.3441 10.3182 12.4082 11.1888C10.8061 11.0598 9.26806 10.6407 8.01841 9.60883C7.15327 8.80272 6.70468 7.70641 6.96102 6.54561C7.12123 5.90073 7.73003 5.38482 8.33884 5.25584C10.2934 4.80442 11.7033 6.54561 11.7033 6.54561C11.5751 5.70726 10.3575 3.09546 7.73003 3.22444C6.64059 3.28893 5.55116 3.99831 5.16665 5.09462C4.5258 6.67459 5.23073 8.31906 6.32017 9.44761C7.73003 10.8341 9.62053 11.35 11.6072 11.5435Z" fill="black"></path>
<path d="M23.5576 21.8953L23.9742 22.508H23.077L22.9488 22.3145C22.6605 22.4757 22.34 22.5725 22.0196 22.5725C21.3467 22.5725 20.834 22.1533 20.834 21.5084C20.834 20.9603 21.1224 20.6701 21.6031 20.3799C21.3788 20.1219 21.2826 19.9284 21.2826 19.606C21.2826 19.0256 21.6992 18.7354 22.2439 18.7354C22.7566 18.7354 23.1411 19.0901 23.1411 19.606C23.1411 19.9607 22.8848 20.3154 22.5964 20.5088L23.1411 21.3149C23.3333 21.1537 23.4936 20.9603 23.6217 20.8313L24.0383 21.3149C23.9421 21.5407 23.7499 21.7341 23.5576 21.8953ZM22.5964 21.8309L21.9876 20.928C21.6992 21.0892 21.6031 21.2182 21.6031 21.5084C21.6031 21.7986 21.8274 21.9598 22.1798 21.9598C22.308 21.9598 22.4682 21.8953 22.5964 21.8309ZM22.0196 19.6382C22.0196 19.7672 22.1157 19.9284 22.2759 20.0897C22.4682 19.9607 22.5964 19.7995 22.5964 19.6382C22.5964 19.4448 22.5002 19.348 22.308 19.348C22.1157 19.348 22.0196 19.4448 22.0196 19.6382Z" fill="black"></path>
<path d="M5.13287 22.5449L4.10751 20.8037V22.5449H3.27441V18.8046H4.58815C5.35716 18.8046 5.86984 19.2882 5.86984 19.9009C5.86984 20.449 5.51737 20.836 5.03674 20.9649L6.03005 22.5127H5.13287V22.5449ZM4.62019 19.5139H4.10751V20.4168H4.62019C4.90857 20.4168 5.06878 20.2233 5.06878 19.9331C5.06878 19.6752 4.87653 19.5139 4.62019 19.5139Z" fill="black"></path>
<path d="M9.26246 22.5449V18.8046H10.0635V21.8355H11.3772V22.5449H9.26246Z" fill="black"></path>
<path d="M14.2314 22.5353L13.943 21.6002H12.8215L12.5331 22.5353H11.668L12.9817 18.8271H13.7507L15.0965 22.5353H14.2314ZM13.4623 19.9879C13.4303 19.859 13.3662 19.6977 13.3662 19.6977C13.3662 19.6977 13.3342 19.859 13.3021 19.9879L13.0137 20.8908H13.7507L13.4623 19.9879Z" fill="black"></path>
<path d="M16.3763 18.8047H15.5752V22.5128H16.3763V18.8047Z" fill="black"></path>
<path d="M18.0777 22.5656C17.5651 22.5656 17.1805 22.3721 16.8281 22.1142L17.2446 21.5338C17.4689 21.695 17.7894 21.8562 18.0777 21.8562C18.4622 21.8562 18.5584 21.7272 18.5584 21.5015C18.5584 21.1146 17.8855 21.0179 17.5651 20.8244C17.1805 20.6309 16.8922 20.373 16.8922 19.7926C16.8922 19.0187 17.5651 18.7285 18.2059 18.7285C18.5584 18.7285 18.9749 18.8252 19.2633 19.051L18.9108 19.6636C18.7506 19.5346 18.4622 19.4379 18.2059 19.4379C17.8214 19.4379 17.6932 19.6636 17.6932 19.7926C17.6932 20.115 18.1739 20.244 18.5904 20.4052C18.9429 20.5664 19.3274 20.8244 19.3274 21.437C19.3915 22.1787 18.8788 22.5656 18.0777 22.5656Z" fill="black"></path>
<path d="M2.50378 27.2375C1.3823 27.2375 0.613281 26.4959 0.613281 25.3028C0.613281 24.1743 1.44638 23.4004 2.47173 23.4004C3.01645 23.4004 3.433 23.5616 3.78547 23.9485L3.33688 24.4967C3.08054 24.2387 2.79216 24.1098 2.47173 24.1098C1.83089 24.1098 1.44638 24.6257 1.44638 25.3028C1.44638 26.0767 1.83089 26.5281 2.50378 26.5281C2.95237 26.5281 3.20871 26.3346 3.433 26.1089L3.91364 26.6571C3.56117 27.0118 3.14462 27.2375 2.50378 27.2375Z" fill="black"></path>
<path d="M10.2274 27.17L9.93907 26.2349H8.81758L8.5292 27.17H7.66406L8.9778 23.4619H9.74681L11.0926 27.17H10.2274ZM9.45843 24.655C9.42639 24.526 9.3623 24.3648 9.3623 24.3648C9.3623 24.3648 9.33026 24.526 9.29822 24.655L9.00984 25.5578H9.71477L9.45843 24.655Z" fill="black"></path>
<path d="M10.9951 23.4766V24.1859H11.7321V27.1847H12.5331V24.1859H13.2381V23.4766H10.9951Z" fill="black"></path>
<path d="M18.8466 27.1925L18.5582 26.2574H17.4367L17.1483 27.1925H16.2832L17.5969 23.4844H18.366L19.7117 27.1925H18.8466ZM18.0776 24.6774C18.0455 24.5484 17.9814 24.3872 17.9814 24.3872C17.9814 24.3872 17.9494 24.5484 17.9174 24.6774L17.629 25.5803H18.3339L18.0776 24.6774Z" fill="black"></path>
<path d="M21.442 27.257C20.6089 27.257 19.872 26.7088 19.872 25.677V23.4844H20.673V25.7092C20.673 26.2252 20.9935 26.5798 21.442 26.5798C21.8906 26.5798 22.2431 26.2252 22.2431 25.7092V23.5166H23.0442V25.7092C23.0442 26.7088 22.3072 27.257 21.442 27.257Z" fill="black"></path>
<path d="M25.4465 23.4766L24.9017 24.4439L24.357 23.4766H23.4598L24.4531 25.1855L23.2676 27.1847H24.1648L24.9017 25.8949L25.6708 27.1847H26.5679L25.3503 25.1855L26.3436 23.4766H25.4465Z" fill="black"></path>
<path d="M13.6582 23.4766V27.1847H15.9012V26.5075H14.4913V25.7014H15.805V24.9921H14.4913V24.1859H15.805V23.4766H13.6582Z" fill="black"></path>
<path d="M7.24735 21.8357V21.0296H8.59312V20.3202H7.24735V19.5141H8.59312V18.8047H6.44629V22.545H8.65721V21.8357H7.24735Z" fill="black"></path>
<path d="M6.4478 23.4766V24.9921H5.16611V23.4766H4.33301V27.1847H5.16611V25.7014H6.4478V27.1847H7.24886V23.4766H6.4478Z" fill="black"></path>
</svg>
</a>
</div>
<a class="link-arrow text-more-12 lg:text-more-13 flex items-center gap-x-[.4em]" href="https://www.gorakadan.com/recruit">
<span>採用について</span>
<span class="link-arrow_icon block w-5 h-5 leading-[0] overflow-hidden">
<svg fill="none" height="14" viewbox="0 0 14 14" width="14" xmlns="http://www.w3.org/2000/svg">
<path d="M2.16602 11.5234L11.166 2.52344" stroke="black"></path>
<path d="M7.11523 2.25391L11.4828 2.2596L11.4885 6.62718" stroke="black"></path>
</svg>
</span>
</a> </div>
</div>
</div>
</div>
</div>
</div>
<nav class="main-nav -txt-white fixed top-0 right-[4px] grid grid-cols-1 content-between items-stretch gap-y-[5px] w-[35px] h-[100dvh] pt-[4px] pb-[8px] z-[100] lg:top-0 lg:right-more-12 lg:py-more-12 lg:h-full lg:w-[36px] lg:items-start -hide-bg" id="main-nav" style="opacity: 1; visibility: inherit;">
<div class="relative">
<div class="burger relative main-nav__item bg-nav-blur burger h-[37px] aspect-square cursor-pointer overflow-hidden" id="burger">
<div class="burger__lines grid grid-cols-1 gap-y-[3px] w-2/5">
<div class="line-t w-full h-[1px] bg-white01"></div>
<div class="line-c w-full h-[1px] bg-white01"></div>
<div class="line-b w-full h-[1px] bg-white01"></div>
</div>
<div class="burger__close absolute top-1/2 left-1/2 w-2/5 translate-x-[150%] -translate-y-1/2 opacity-0">
<div class="line-t absolute top-0 left-0 w-full h-[1px] bg-white01"></div>
<div class="line-b absolute top-0 left-0 w-full h-[1px] bg-white01"></div>
</div>
</div>
<p class="hidden txt-burger absolute top-2/4 right-full font-abc text-more-11 whitespace-nowrap -translate-y-1/2 lg:text-more-13">Close</p>
<p class="txt-burger absolute left-2/4 top-full text-more-10 tracking-[0.3em] whitespace-nowrap [writing-mode:vertical-lr] pointer-events-none -translate-x-1/2 lg:text-more-11">閉じる</p>
</div>
<div class="main-nav__rb grid grid-cols-1 gap-y-[6px]">
<a class="main-nav__item bg-nav-blur cursor-pointer" data-barba-prevent="" data-name="language-switcher" href="https://www.gorakadan.com/en/">
<ul class="language-switcher text-center grid grid-cols-1 py-[0.5em] leading-[2.5] pointer-events-none" id="language-switcher">
<li class="lang-item font-abc text-white01 pointer-events-none">JP</li>
<li class="-is-active lang-item font-abc text-white01 pointer-events-none">EN</li>
</ul>
</a>
<a class="js_mn-logo mn-logo main-nav__item -circle bg-nav-blur aspect-square" href="https://www.gorakadan.com">
<span class="block leading-none">
<svg fill="none" height="10" viewbox="0 0 15 10" width="15" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3045_10866)">
<path d="M3.69474 5.26019C3.72486 5.36104 3.70227 5.41327 3.61019 5.45356C3.00013 5.72042 2.39177 5.99112 1.78516 6.2657C1.50542 6.39229 1.27688 6.57175 1.19922 6.88969C1.16203 7.04202 1.27507 7.28025 1.4054 7.37207C1.59195 7.50353 1.76554 7.47026 1.92851 7.35022C2.21456 7.13951 2.49319 6.91879 2.77276 6.6995C3.01187 6.51193 3.24887 6.32163 3.48488 6.13023C3.59974 6.03709 3.71237 5.94092 3.82197 5.8418C3.89326 5.77729 3.96479 5.73834 4.04379 5.81943C4.11596 5.89356 4.07351 5.95402 4.01658 6.02207C3.65194 6.45773 3.28297 6.89038 2.93404 7.33827C2.77895 7.53733 2.65201 7.76217 2.5395 7.98864C2.39317 8.28321 2.47305 8.54446 2.72827 8.70421C2.86537 8.79006 3.00895 8.73157 3.10915 8.66891C3.25419 8.57825 3.40728 8.4516 3.47934 8.30315C3.73269 7.78124 3.95177 7.24304 4.18638 6.71208C4.25919 6.54723 4.34467 6.38789 4.41555 6.22228C4.45882 6.12131 4.54238 6.14983 4.60555 6.16397C4.63317 6.17011 4.66605 6.257 4.65811 6.30036C4.56783 6.79468 4.4677 7.28721 4.37567 7.78118C4.34017 7.97183 4.30012 8.16456 4.29521 8.35718C4.2878 8.64688 4.36464 8.91428 4.63475 9.07884C4.84127 9.20468 5.1188 9.11559 5.22921 8.9028C5.37011 8.63118 5.35172 8.34535 5.31493 8.05698C5.29257 7.88152 5.26991 7.70606 5.24644 7.53072C5.19949 7.18015 5.15138 6.82969 5.1049 6.47907C5.09638 6.41472 5.09544 6.3494 5.08674 6.28512C5.07372 6.18953 5.11407 6.15365 5.20989 6.14699C5.30553 6.14032 5.35843 6.18008 5.377 6.26436C5.44175 6.55784 5.50563 6.85155 5.56384 7.1463C5.63975 7.53066 5.80353 7.87167 6.06436 8.16491C6.19358 8.31017 6.32408 8.4556 6.46713 8.58683C6.54006 8.65372 6.64493 8.68508 6.72994 8.74073C6.78944 8.77968 6.85233 8.82397 6.89168 8.88089C6.98411 9.0145 7.06148 9.15831 7.14866 9.29562C7.21014 9.39254 7.18124 9.45781 7.06154 9.47508C6.8612 9.50395 6.66823 9.4734 6.48115 9.40002C6.3806 9.3606 6.27515 9.32994 6.18091 9.27899C6.03791 9.20166 5.93894 9.27824 5.85311 9.36576C5.60571 9.61797 5.32795 9.80032 4.96634 9.85585C4.72146 9.89347 4.50355 9.83707 4.31057 9.71129C4.12594 9.59095 3.96263 9.43642 3.79884 9.28728C3.71167 9.20792 3.64656 9.19448 3.53556 9.25482C3.10973 9.48644 2.66562 9.54053 2.24603 9.2587C1.99717 9.09159 1.82819 8.84866 1.79164 8.53125C1.77664 8.40111 1.73401 8.26977 1.67825 8.15077C1.657 8.10544 1.5641 8.07066 1.50337 8.06996C1.1827 8.06608 0.933082 7.92882 0.729944 7.69308C0.566979 7.50394 0.477584 7.28866 0.465381 7.03605C0.454287 6.80535 0.50602 6.59053 0.58035 6.37647C0.637572 6.21162 0.611063 6.16009 0.488153 6.05645C0.227093 5.83624 0.0469024 5.57632 0.0100585 5.21752C-0.0191947 4.93292 0.00906589 4.67353 0.179155 4.44572C0.280519 4.31003 0.399576 4.17775 0.537142 4.08101C0.668752 3.98844 0.638272 3.89297 0.579532 3.80736C0.276782 3.36654 0.448214 2.62077 0.865408 2.33778C1.0752 2.19548 1.2819 2.09827 1.53572 2.09526C1.70248 2.09329 1.7569 2.01817 1.77109 1.85024C1.80601 1.43678 1.91496 1.04708 2.30442 0.825079C2.45939 0.736741 2.65429 0.704397 2.83565 0.677617C3.10926 0.637274 3.36121 0.73906 3.6054 0.849077C3.77841 0.927039 3.78063 0.933473 3.8824 0.775346C4.16781 0.331916 4.68783 0.195583 5.18466 0.268619C5.54282 0.321251 5.76733 0.527605 5.96013 0.795749C6.08217 0.96547 6.09758 0.978454 6.24835 0.849482C6.5219 0.615479 6.83522 0.613045 7.16133 0.678139C7.23869 0.693558 7.2645 0.742074 7.21037 0.821543C7.11888 0.956021 7.02405 1.09119 6.95632 1.23779C6.89764 1.36473 6.80842 1.41429 6.68078 1.43945C6.45032 1.48489 6.28484 1.62459 6.15562 1.81535C5.80301 2.33593 5.62223 2.92769 5.43428 3.51667C5.3965 3.63503 5.35971 3.75368 5.32194 3.87205C5.28556 3.98618 5.19582 4.02252 5.09451 3.98925C4.99682 3.9572 4.9765 3.87982 4.99764 3.76997C5.07979 3.34335 5.15407 2.91494 5.2183 2.48524C5.26489 2.17345 5.30862 1.85934 5.32065 1.545C5.32988 1.30306 5.21929 1.09508 5.00184 0.962687C4.75678 0.813486 4.41701 1.04053 4.31594 1.25796C4.21195 1.48153 4.26117 1.70481 4.30006 1.92525C4.38665 2.41598 4.48726 2.90421 4.58302 3.39332C4.60608 3.51122 4.63417 3.62813 4.65723 3.74603C4.67422 3.83298 4.67072 3.91471 4.56614 3.94763C4.47044 3.97772 4.40937 3.93546 4.37521 3.84846C4.2499 3.52936 4.13575 3.20551 3.99824 2.89169C3.80538 2.45145 3.61048 2.01086 3.38714 1.58558C3.24525 1.31534 2.90969 1.24729 2.64839 1.39702C2.48876 1.48848 2.46044 1.65461 2.43358 1.81541C2.39942 2.0195 2.50925 2.18539 2.60127 2.34967C2.85223 2.79773 3.17653 3.19282 3.52873 3.56536C3.67371 3.71873 3.8278 3.8637 3.97763 4.01261C3.99947 4.03435 4.03608 4.05446 4.03947 4.07892C4.04752 4.13683 4.0526 4.20227 4.03129 4.25328C4.02394 4.2709 3.92117 4.27461 3.88544 4.2505C3.74904 4.15851 3.62204 4.05284 3.49189 3.95181C3.04334 3.60356 2.59905 3.24968 2.14437 2.9096C1.98088 2.7873 1.78861 2.69983 1.57385 2.77837C1.4103 2.83813 1.29136 2.95569 1.26754 3.13132C1.23653 3.35993 1.29802 3.55997 1.49853 3.70407C1.81079 3.92845 2.18215 4.02455 2.52694 4.17816C2.75852 4.28134 2.99108 4.38289 3.22686 4.47616C3.35456 4.52664 3.49177 4.55319 3.62046 4.60159C3.6569 4.61527 3.70005 4.66732 3.70087 4.70268C3.70174 4.73868 3.65754 4.80389 3.62735 4.80737C3.52225 4.81942 3.41283 4.82261 3.30831 4.80713C2.81113 4.73329 2.31487 4.65301 1.81862 4.57307C1.69051 4.55244 1.56281 4.52815 1.43622 4.49986C1.05839 4.41541 0.583211 4.75839 0.603881 5.1162C0.620755 5.40782 0.734732 5.52416 1.0839 5.64977C1.17873 5.68391 1.29358 5.69185 1.39401 5.67701C1.75375 5.6238 2.11279 5.5634 2.46908 5.491C2.8325 5.41715 3.192 5.32441 3.55419 5.24448C3.5967 5.23497 3.64563 5.25398 3.69474 5.26019Z" fill="white"></path>
<path d="M11.9329 0.537813C12.4419 0.480196 12.8908 0.80758 13.004 1.29877C13.0953 1.6949 12.9566 2.01388 12.6788 2.29513C12.3338 2.64448 11.9956 3.00061 11.6598 3.35878C11.4422 3.59093 11.2352 3.83281 11.0217 4.06867C10.9079 4.19434 10.8435 4.20118 10.6973 4.10641C10.6234 4.05847 10.4889 4.08374 10.4849 3.95187C10.4821 3.86191 10.4913 3.76917 10.5116 3.68141C10.5745 3.40996 10.6486 3.14106 10.7126 2.86985C10.8327 2.36063 10.9552 1.85181 11.0643 1.34022C11.109 1.13062 11.2125 0.939855 11.3709 0.793437C11.5296 0.64667 11.7277 0.561057 11.9329 0.537813Z" fill="white"></path>
<path d="M9.03906 1.00489C9.06055 0.560246 9.2509 0.242021 9.66541 0.0532877C9.8515 -0.0314566 10.1441 -0.00392342 10.3291 0.0580987C10.4877 0.11131 10.5879 0.199996 10.7054 0.319751C10.8091 0.425421 10.8852 0.554682 10.9358 0.692927C11.0023 0.874357 10.9577 1.06448 10.9147 1.24782C10.7694 1.86573 10.6234 2.48345 10.4757 3.10078C10.4169 3.34666 10.3578 3.59272 10.2873 3.83531C10.2731 3.88417 10.213 3.93559 10.1622 3.9546C10.091 3.98126 10.0083 3.9772 9.93096 3.98891C9.80519 4.00792 9.73472 3.97355 9.70219 3.83386C9.58763 3.34127 9.45655 2.85246 9.33603 2.36121C9.23794 1.96143 9.14527 1.56031 9.0522 1.15931C9.0414 1.11311 9.04368 1.06396 9.03906 1.00489Z" fill="white"></path>
<path d="M14.5445 2.98757C14.5646 3.24198 14.4993 3.50108 14.3133 3.66309C14.1899 3.77055 14.033 3.85669 13.8764 3.90706C13.2646 4.10362 12.6462 4.27977 12.0318 4.46839C11.7888 4.54293 11.5498 4.63017 11.3089 4.71138C11.2889 4.7181 11.2547 4.73346 11.2499 4.72732C11.1609 4.61301 11.065 4.50166 10.9959 4.376C10.9776 4.34267 11.0312 4.25149 11.0734 4.20714C11.493 3.76685 11.9186 3.33228 12.3391 2.89291C12.502 2.72273 12.6477 2.5355 12.8169 2.37222C13.0253 2.17131 13.2335 1.97423 13.5673 1.98478C13.8739 1.99446 14.1801 2.20325 14.3546 2.44438C14.4596 2.58929 14.5286 2.78695 14.5445 2.98757Z" fill="white"></path>
<path d="M15.0006 5.1213C14.9973 5.67202 14.6355 5.99587 14.1963 6.10675C14.0355 6.14739 13.837 6.07545 13.6637 6.0267C13.0158 5.84446 12.3711 5.65132 11.7257 5.4605C11.5894 5.42022 11.4549 5.37362 11.3202 5.32806C11.2111 5.29113 11.1906 5.21786 11.1961 5.10437C11.205 4.92114 11.297 4.85761 11.4637 4.80956C12.2388 4.58611 13.0055 4.33327 13.7831 4.11938C14.3562 3.96177 14.8374 4.26638 14.9639 4.85205C14.983 4.94033 14.9887 5.03151 15.0006 5.1213Z" fill="white"></path>
<path d="M11.9846 9.48395C11.9722 9.48366 11.9597 9.48314 11.9473 9.48244C11.9054 9.48007 11.8637 9.47543 11.8224 9.46818C11.7019 9.44714 11.5859 9.40425 11.4723 9.36014C11.1987 9.25424 11.1355 8.99038 11.0666 8.74913C10.9172 8.2256 10.783 7.69771 10.6473 7.17047C10.5693 6.86731 10.4973 6.56253 10.429 6.25712C10.4065 6.15637 10.4351 6.06728 10.5448 6.02346C10.5891 6.00578 10.6324 5.98538 10.6749 5.96376C10.7589 5.92092 10.829 5.92892 10.8988 5.99941C11.1811 6.28424 11.4685 6.56392 11.7509 6.84865C12.051 7.15122 12.3547 7.45072 12.643 7.76402C12.8498 7.98864 13.0177 8.23858 13.0017 8.56846C12.9906 8.79678 12.9324 9.02916 12.7637 9.19424C12.5604 9.393 12.2676 9.49062 11.9846 9.48395Z" fill="white"></path>
<path d="M6.09415 6.04586C5.86246 6.02708 5.66119 5.92339 5.47738 5.75935C5.24575 5.55259 5.12372 5.30919 5.14252 4.99879C5.15863 4.73244 5.26338 4.51699 5.47902 4.34704C5.8168 4.08075 6.18092 4.05768 6.57715 4.18201C6.84808 4.26705 7.12619 4.32918 7.3984 4.41039C7.62116 4.47688 7.83854 4.56116 8.06095 4.62898C8.32744 4.71024 8.59609 4.78473 8.86509 4.85747C9.01065 4.89683 9.06828 4.97566 9.04598 5.12568C9.01468 5.33661 8.98029 5.3624 8.76466 5.40993C8.49099 5.47028 8.21959 5.54128 7.94825 5.61159C7.43904 5.74358 6.93117 5.88078 6.42125 6.00981C6.32099 6.0352 6.21373 6.03346 6.09415 6.04586Z" fill="white"></path>
<path d="M9.97244 9.99908C9.55834 9.97711 9.21519 9.83922 9.02688 9.43804C8.92931 9.23012 8.97205 9.02458 9.02811 8.82002C9.18232 8.25771 9.33997 7.69626 9.49774 7.13488C9.58474 6.8254 9.67238 6.5161 9.76662 6.20883C9.77742 6.17353 9.8291 6.13748 9.86845 6.12739C9.91143 6.11638 9.9645 6.14745 10.0091 6.13899C10.2307 6.0969 10.251 6.26703 10.2865 6.40383C10.4171 6.90615 10.5418 7.40998 10.6637 7.91444C10.7483 8.26455 10.8394 8.61413 10.9014 8.96841C10.9995 9.52887 10.4243 10.0218 9.97244 9.99908Z" fill="white"></path>
<path d="M7.91964 0.666057C8.21521 0.65232 8.39014 0.709009 8.59299 0.852587C8.74287 0.958721 8.80821 1.1205 8.85568 1.28639C8.96183 1.65702 9.05929 2.03014 9.16065 2.40216C9.25413 2.74519 9.34493 3.08898 9.44337 3.43056C9.48232 3.5658 9.53522 3.69743 9.588 3.8282C9.67787 4.05073 9.61638 4.20775 9.38668 4.28711C9.33915 4.30351 9.25407 4.28079 9.2163 4.24491C9.06723 4.10348 8.93211 3.94767 8.78801 3.8009C8.61518 3.62498 8.43417 3.45682 8.26431 3.27823C7.91181 2.90755 7.56229 2.53397 7.21534 2.15807C6.8883 1.80373 6.96211 1.32691 7.24693 1.01303C7.42765 0.813925 7.6448 0.678809 7.91964 0.666057Z" fill="white"></path>
<path d="M14.5692 7.09642C14.5312 7.70116 14.0693 8.24841 13.4526 8.1741C13.3308 8.15943 13.1919 8.10454 13.1048 8.02235C12.8604 7.79165 12.6388 7.53713 12.4086 7.29165C12.289 7.16412 12.1721 7.03399 12.0508 6.90815C11.7919 6.63983 11.529 6.37522 11.2716 6.10546C11.1821 6.01161 11.1074 5.90409 11.0201 5.80798C10.9148 5.69211 11.0278 5.6116 11.0736 5.53381C11.0986 5.49138 11.1998 5.45753 11.2515 5.47225C11.5166 5.54784 11.7763 5.64215 12.0389 5.72666C12.2929 5.80839 12.5482 5.88571 12.8028 5.9657C13.225 6.09833 13.6755 6.14725 14.0683 6.36485C14.3695 6.53173 14.5516 6.78735 14.5692 7.09642Z" fill="white"></path>
<path d="M6.50428 7.96651C6.08236 7.94292 5.7562 7.73094 5.64245 7.27012C5.58494 7.03716 5.55971 6.80693 5.67375 6.57802C5.78043 6.36384 5.95968 6.23609 6.17759 6.17992C6.70409 6.04417 7.23626 5.93015 7.76521 5.8035C8.12606 5.71707 8.48509 5.62358 8.8453 5.53443C8.91636 5.51681 8.98841 5.49553 9.06076 5.4924C9.163 5.488 9.22594 5.55135 9.2608 5.64265C9.29058 5.72067 9.31055 5.79492 9.23394 5.8643C8.89995 6.16665 8.56824 6.47148 8.23571 6.77539C8.12103 6.88019 8.00542 6.98395 7.89209 7.09014C7.70004 7.27018 7.50578 7.44807 7.3194 7.63373C7.09484 7.85736 6.82017 7.95144 6.50428 7.96651Z" fill="white"></path>
<path d="M7.16917 8.90545C7.20741 8.98533 7.25477 9.0608 7.3093 9.12943C7.62391 9.52532 8.31092 9.45038 8.62967 9.10317C8.73932 8.9837 8.81599 8.83769 8.86755 8.68513C8.93738 8.47843 8.99881 8.26888 9.06531 8.06102C9.26238 7.44486 9.46003 6.82887 9.65756 6.21282C9.7062 6.06113 9.67659 5.99731 9.49874 5.89761C9.401 5.84277 9.31797 5.89732 9.25204 5.95882C8.85645 6.3284 8.46285 6.70007 8.0724 7.07499C7.83265 7.30522 7.60102 7.54369 7.36349 7.77624C7.23182 7.90521 7.13285 8.06334 7.08812 8.24239C7.03265 8.46486 7.07066 8.69973 7.16917 8.90545Z" fill="white"></path>
<path d="M5.6661 3.12682C5.64724 2.91066 5.76209 2.69109 5.89761 2.52978C5.9643 2.45043 6.04201 2.37907 6.1303 2.324C6.24527 2.25224 6.38307 2.21683 6.51631 2.19735C6.66135 2.17614 6.80948 2.18721 6.94331 2.24998C7.03487 2.29294 7.11837 2.36203 7.19118 2.43356C7.70372 2.93727 8.21101 3.4462 8.72361 3.9498C8.8429 4.067 8.97469 4.17192 9.10355 4.27915C9.20539 4.3639 9.20959 4.45131 9.1366 4.6139C9.07903 4.74217 8.97364 4.71018 8.89008 4.68786C8.53607 4.59309 8.18614 4.48325 7.83352 4.38308C7.31467 4.23574 6.79886 4.07477 6.27405 3.95177C5.81534 3.84424 5.67287 3.49889 5.6661 3.12682Z" fill="white"></path>
<path d="M10.1088 4.14405C10.6968 4.13077 10.9827 4.50459 11.0023 5.0247C11.0205 5.50911 10.6216 5.90264 10.1484 5.91191C9.60728 5.92246 9.22635 5.4646 9.24269 4.90391C9.25239 4.57241 9.66754 4.09559 10.1088 4.14405Z" fill="white"></path>
<path d="M4.92197 5.99439C4.45234 6.01995 3.94119 5.66127 3.85635 5.1681C3.80958 4.89636 3.89653 4.64851 4.05821 4.42662C4.21329 4.21377 4.43768 4.13587 4.68759 4.12613C4.84051 4.12016 4.99472 4.13923 5.14805 4.15071C5.22623 4.15656 5.24539 4.1932 5.20352 4.26519C5.14431 4.36709 5.07389 4.43201 4.94205 4.41485C4.71772 4.3857 4.51376 4.42766 4.36767 4.61982C4.11706 4.94958 4.15093 5.41654 4.5937 5.59762C4.73062 5.65361 4.88267 5.67663 5.02999 5.70265C5.14227 5.72253 5.20229 5.77969 5.19961 5.89266C5.19663 6.01804 5.10233 5.98969 5.02806 5.99404C4.99285 5.99607 4.95735 5.99439 4.92197 5.99439Z" fill="white"></path>
</g>
<defs>
<clippath id="clip0_3045_10866">
<rect fill="white" height="10" width="15"></rect>
</clippath>
</defs>
</svg>
</span>
</a>
</div>
</nav>
<div class="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[34px] h-[21px] overflow-hidden z-50" id="loading-count" style="filter: blur(10px); opacity: 0; visibility: hidden;">
<div class="grid grid-cols-1 font-abc text-[1.8rem]" id="inner_loading" style="translate: none; rotate: none; scale: none; transform: translate(0px, -105px);">
<div class="flex justify-center items-center h-[15px] w-auto my-[3px]">
<svg class="h-full w-auto leading-[0]" fill="none" height="14" viewbox="0 0 20 14" width="20" xmlns="http://www.w3.org/2000/svg">
<path d="M4.92332 7.00431C4.96347 7.13871 4.93335 7.2083 4.81065 7.26198C3.99774 7.61759 3.18709 7.9783 2.37877 8.34418C2.006 8.51287 1.70147 8.752 1.59799 9.17566C1.54843 9.37864 1.69906 9.69609 1.87272 9.81844C2.12131 9.99362 2.35262 9.94928 2.56978 9.78932C2.95095 9.50856 3.32224 9.21443 3.69477 8.92224C4.01338 8.67229 4.3292 8.41871 4.64368 8.16367C4.79673 8.03955 4.94681 7.91141 5.09286 7.77933C5.18786 7.69336 5.28317 7.64146 5.38844 7.74951C5.48461 7.8483 5.42804 7.92886 5.35218 8.01954C4.86629 8.60007 4.37463 9.17658 3.90967 9.77341C3.70302 10.0386 3.53387 10.3383 3.38394 10.64C3.18896 11.0326 3.29539 11.3807 3.63548 11.5936C3.81817 11.7079 4.00949 11.63 4.14301 11.5465C4.33628 11.4257 4.54028 11.2569 4.63629 11.0591C4.97389 10.3637 5.26582 9.64651 5.57844 8.939C5.67546 8.71933 5.78937 8.507 5.88383 8.28633C5.94148 8.15177 6.05282 8.18978 6.137 8.20862C6.17381 8.21681 6.21761 8.33259 6.20703 8.39037C6.08674 9.04906 5.95331 9.70536 5.83068 10.3636C5.78338 10.6176 5.73 10.8745 5.72347 11.1311C5.71359 11.5172 5.81598 11.8735 6.17591 12.0927C6.45111 12.2604 6.82092 12.1417 6.96805 11.8582C7.15579 11.4962 7.13128 11.1154 7.08226 10.7311C7.05246 10.4973 7.02228 10.2635 6.991 10.0298C6.92844 9.5627 6.86433 9.09571 6.8024 8.62849C6.79104 8.54276 6.78979 8.45571 6.7782 8.37005C6.76085 8.24268 6.81461 8.19487 6.94229 8.18599C7.06974 8.17711 7.14023 8.2301 7.16497 8.3424C7.25126 8.73346 7.33638 9.12483 7.41395 9.51759C7.5151 10.0298 7.73334 10.4842 8.0809 10.8749C8.25308 11.0685 8.42698 11.2623 8.6176 11.4371C8.71478 11.5263 8.85452 11.5681 8.9678 11.6422C9.04709 11.6941 9.13088 11.7531 9.18332 11.829C9.30649 12.007 9.40958 12.1986 9.52575 12.3816C9.60767 12.5108 9.56916 12.5977 9.40966 12.6208C9.14271 12.6592 8.88556 12.6185 8.63627 12.5207C8.50229 12.4682 8.36178 12.4273 8.2362 12.3595C8.04565 12.2564 7.91377 12.3585 7.7994 12.4751C7.46973 12.8111 7.09961 13.0541 6.61776 13.1281C6.29145 13.1783 6.00108 13.1031 5.74393 12.9355C5.49791 12.7752 5.28029 12.5692 5.06204 12.3705C4.94588 12.2648 4.85913 12.2468 4.71122 12.3272C4.14378 12.6359 3.552 12.708 2.99289 12.3324C2.66128 12.1097 2.43611 11.786 2.3874 11.3631C2.36741 11.1897 2.31061 11.0146 2.23631 10.8561C2.20798 10.7957 2.0842 10.7493 2.00328 10.7484C1.57597 10.7432 1.24335 10.5603 0.972667 10.2462C0.755512 9.99416 0.636392 9.70729 0.62013 9.37069C0.605347 9.06327 0.674283 8.77703 0.773329 8.49178C0.849579 8.27211 0.814255 8.20345 0.650474 8.06534C0.302606 7.77191 0.0624984 7.42557 0.0134032 6.94746C-0.0255774 6.56822 0.0120805 6.22257 0.238728 5.91902C0.373798 5.7382 0.532444 5.56194 0.715753 5.43303C0.891127 5.30968 0.850512 5.18247 0.77224 5.06839C0.368819 4.48098 0.597256 3.48722 1.15318 3.11014C1.43273 2.92052 1.70816 2.79099 2.04638 2.78697C2.26859 2.78435 2.34111 2.68425 2.36002 2.46048C2.40654 1.90954 2.55173 1.39026 3.07069 1.09443C3.27719 0.976719 3.5369 0.933619 3.77857 0.897935C4.14316 0.844176 4.47889 0.979808 4.80427 1.12641C5.03481 1.23029 5.03777 1.23887 5.17338 1.02816C5.5537 0.43728 6.24663 0.255614 6.90868 0.352935C7.38594 0.423068 7.6851 0.69804 7.94201 1.05535C8.10463 1.2815 8.12517 1.29881 8.32606 1.12695C8.69058 0.815134 9.10808 0.81189 9.54263 0.89863C9.64572 0.919175 9.68011 0.983825 9.60799 1.08972C9.48606 1.26891 9.35971 1.44904 9.26945 1.64437C9.19126 1.81353 9.07237 1.87957 8.90229 1.91309C8.59519 1.97364 8.37469 2.15979 8.20251 2.41399C7.73264 3.10767 7.49175 3.8962 7.2413 4.68103C7.19096 4.83875 7.14194 4.99686 7.0916 5.15459C7.04313 5.30667 6.92354 5.3551 6.78855 5.31076C6.65838 5.26805 6.6313 5.16494 6.65947 5.01857C6.76894 4.45009 6.86791 3.87921 6.9535 3.30664C7.01558 2.89117 7.07386 2.47261 7.08989 2.05374C7.10218 1.73135 6.95482 1.45421 6.66507 1.2778C6.33852 1.07898 5.88577 1.38153 5.75109 1.67125C5.61252 1.96916 5.67811 2.26669 5.72993 2.56043C5.84531 3.21434 5.97937 3.86492 6.10697 4.51667C6.13771 4.67377 6.17513 4.82956 6.20586 4.98667C6.2285 5.10253 6.22384 5.21143 6.08449 5.25531C5.95696 5.29539 5.87558 5.23909 5.83006 5.12315C5.66309 4.69795 5.51098 4.26641 5.32775 3.84824C5.07076 3.26161 4.81104 2.67451 4.51344 2.10781C4.32437 1.74772 3.87722 1.65704 3.52904 1.85655C3.31632 1.97843 3.27859 2.1998 3.2428 2.41406C3.19728 2.68602 3.34363 2.90708 3.46625 3.12598C3.80066 3.72303 4.23279 4.2495 4.70212 4.74591C4.89531 4.95029 5.10064 5.14346 5.30028 5.34189C5.32938 5.37086 5.37817 5.39766 5.38268 5.43025C5.39342 5.50741 5.40019 5.59462 5.37179 5.66259C5.36198 5.68607 5.22505 5.69101 5.17743 5.65888C4.99568 5.5363 4.82645 5.39549 4.65302 5.26087C4.05532 4.79681 3.4633 4.32527 2.85743 3.87211C2.63957 3.70913 2.38336 3.59258 2.09719 3.69724C1.87926 3.77687 1.72077 3.93351 1.68902 4.16755C1.64771 4.47218 1.72964 4.73873 1.99682 4.93075C2.41292 5.22974 2.90777 5.3578 3.36721 5.56249C3.67578 5.69997 3.98568 5.83529 4.29986 5.95957C4.47002 6.02685 4.65287 6.06222 4.82435 6.12672C4.8729 6.14495 4.9304 6.21431 4.93149 6.26142C4.93265 6.30939 4.87375 6.39628 4.83353 6.40092C4.69348 6.41698 4.54767 6.42123 4.4084 6.40061C3.74589 6.30221 3.08462 6.19523 2.42335 6.08872C2.25264 6.06122 2.08248 6.02886 1.9138 5.99116C1.41032 5.87863 0.777142 6.33565 0.804685 6.81245C0.827171 7.20104 0.979047 7.35606 1.44432 7.52343C1.57068 7.56893 1.72372 7.57951 1.85755 7.55974C2.33691 7.48883 2.81533 7.40835 3.2901 7.31188C3.77436 7.21347 4.25341 7.08989 4.73604 6.98338C4.79268 6.97071 4.85788 6.99604 4.92332 7.00431Z" fill="#111111"></path>
<path d="M15.9028 0.71187C16.5811 0.635094 17.1793 1.07134 17.3301 1.72587C17.4517 2.25372 17.2669 2.67877 16.8968 3.05353C16.4371 3.51905 15.9864 3.99361 15.539 4.47087C15.249 4.78021 14.9732 5.10253 14.6886 5.41682C14.537 5.58427 14.4512 5.59339 14.2563 5.4671C14.1578 5.40322 13.9787 5.4369 13.9733 5.26118C13.9696 5.1413 13.9818 5.01772 14.0089 4.90078C14.0928 4.53907 14.1915 4.18076 14.2767 3.81936C14.4368 3.14081 14.6001 2.4628 14.7454 1.78109C14.805 1.5018 14.9429 1.2476 15.1539 1.05249C15.3655 0.856925 15.6294 0.742843 15.9028 0.71187Z" fill="#111111"></path>
<path d="M12.0469 1.33904C12.0755 0.746541 12.3292 0.322498 12.8815 0.071007C13.1295 -0.0419167 13.5194 -0.00522805 13.7659 0.0774179C13.9772 0.148323 14.1107 0.266499 14.2674 0.426076C14.4055 0.566883 14.5069 0.739126 14.5744 0.923341C14.6629 1.1651 14.6036 1.41844 14.5461 1.66275C14.3526 2.48612 14.158 3.30926 13.9613 4.13186C13.8829 4.45951 13.8042 4.78739 13.7102 5.11063C13.6912 5.17574 13.6112 5.24426 13.5435 5.26959C13.4485 5.30512 13.3384 5.29971 13.2354 5.31532C13.0678 5.34065 12.9738 5.29485 12.9305 5.1087C12.7779 4.45232 12.6032 3.80096 12.4426 3.14636C12.3119 2.61364 12.1884 2.07915 12.0644 1.54481C12.05 1.48325 12.053 1.41775 12.0469 1.33904Z" fill="#111111"></path>
<path d="M19.38 3.9774C19.4068 4.31641 19.3197 4.66166 19.0719 4.87755C18.9075 5.02075 18.6985 5.13553 18.4897 5.20265C17.6745 5.46457 16.8505 5.6993 16.0318 5.95063C15.708 6.04996 15.3896 6.16621 15.0685 6.27442C15.0418 6.28338 14.9963 6.30385 14.9899 6.29566C14.8713 6.14334 14.7435 5.99497 14.6514 5.82751C14.627 5.7831 14.6984 5.6616 14.7547 5.60251C15.3138 5.01581 15.8809 4.43674 16.4413 3.85127C16.6584 3.6245 16.8525 3.37501 17.078 3.15743C17.3556 2.88972 17.6331 2.62711 18.0779 2.64117C18.4864 2.65406 18.8945 2.93228 19.127 3.25359C19.2668 3.44669 19.3588 3.71008 19.38 3.9774Z" fill="#111111"></path>
<path d="M19.9867 6.82755C19.9823 7.5614 19.5001 7.99293 18.9149 8.14069C18.7006 8.19484 18.4361 8.09898 18.2052 8.03402C17.3419 7.79118 16.4828 7.53382 15.6228 7.27955C15.4411 7.22587 15.2619 7.16377 15.0824 7.10306C14.937 7.05386 14.9098 6.95623 14.9171 6.80499C14.9289 6.56084 15.0515 6.47619 15.2736 6.41216C16.3065 6.1144 17.3281 5.77748 18.3643 5.49247C19.1279 5.28246 19.7691 5.68835 19.9377 6.46877C19.9632 6.58641 19.9708 6.7079 19.9867 6.82755Z" fill="#111111"></path>
<path d="M15.9711 12.6398C15.9544 12.6394 15.9379 12.6387 15.9213 12.6378C15.8655 12.6346 15.8099 12.6284 15.7549 12.6188C15.5944 12.5907 15.4398 12.5336 15.2884 12.4748C14.9238 12.3337 14.8396 11.9821 14.7478 11.6606C14.5487 10.963 14.3699 10.2596 14.1891 9.55702C14.0851 9.15306 13.9892 8.74693 13.8982 8.33996C13.8682 8.20572 13.9063 8.087 14.0525 8.02861C14.1115 8.00505 14.1692 7.97786 14.2258 7.94905C14.3378 7.89197 14.4312 7.90263 14.5242 7.99655C14.9003 8.37611 15.2834 8.74879 15.6596 9.12819C16.0595 9.53137 16.4642 9.93047 16.8484 10.3479C17.1239 10.6472 17.3477 10.9803 17.3263 11.4199C17.3115 11.7241 17.234 12.0338 17.0092 12.2537C16.7383 12.5186 16.3481 12.6487 15.9711 12.6398Z" fill="#111111"></path>
<path d="M8.1202 8.05698C7.81147 8.03195 7.54328 7.89377 7.29834 7.67519C6.98969 7.39967 6.82708 7.07535 6.85213 6.66173C6.8736 6.30682 7.01319 6.01972 7.30052 5.79325C7.75063 5.43842 8.23582 5.40768 8.76381 5.57335C9.12483 5.68666 9.49542 5.76946 9.85814 5.87768C10.155 5.96627 10.4446 6.07857 10.741 6.16894C11.0961 6.27723 11.4541 6.37649 11.8125 6.47342C12.0065 6.52587 12.0833 6.63091 12.0536 6.83081C12.0119 7.11188 11.9661 7.14625 11.6787 7.20959C11.314 7.28999 10.9524 7.38461 10.5908 7.4783C9.9123 7.65418 9.23555 7.837 8.55607 8.00894C8.42248 8.04277 8.27955 8.04045 8.1202 8.05698Z" fill="#111111"></path>
<path d="M13.2886 13.3186C12.7368 13.2893 12.2796 13.1055 12.0286 12.571C11.8986 12.2939 11.9556 12.02 12.0303 11.7474C12.2358 10.9981 12.4458 10.25 12.6561 9.50193C12.772 9.08955 12.8888 8.6774 13.0144 8.26796C13.0287 8.22092 13.0976 8.17288 13.15 8.15944C13.2073 8.14476 13.278 8.18616 13.3374 8.17489C13.6328 8.11881 13.6597 8.34551 13.7071 8.52779C13.8812 9.19715 14.0473 9.86851 14.2097 10.5407C14.3224 11.0072 14.4439 11.4731 14.5265 11.9452C14.6572 12.692 13.8908 13.3488 13.2886 13.3186Z" fill="#111111"></path>
<path d="M10.5536 0.885471C10.9474 0.867165 11.1805 0.942705 11.4508 1.13403C11.6505 1.27545 11.7376 1.49103 11.8009 1.71208C11.9423 2.20595 12.0722 2.70314 12.2072 3.19886C12.3318 3.65596 12.4528 4.11407 12.584 4.56924C12.6359 4.74944 12.7064 4.92485 12.7767 5.0991C12.8964 5.39562 12.8145 5.60486 12.5084 5.7106C12.4451 5.73246 12.3317 5.70218 12.2814 5.65437C12.0828 5.46591 11.9027 5.25829 11.7107 5.06272C11.4804 4.8283 11.2392 4.60423 11.0128 4.36625C10.5431 3.87231 10.0774 3.3745 9.61507 2.87361C9.17928 2.40144 9.27763 1.76607 9.65717 1.34782C9.89797 1.08251 10.1873 0.902463 10.5536 0.885471Z" fill="#111111"></path>
<path d="M19.4123 9.45797C19.3616 10.2638 18.7461 10.993 17.9243 10.894C17.7621 10.8745 17.5769 10.8013 17.4609 10.6918C17.1353 10.3844 16.84 10.0452 16.5332 9.71811C16.3738 9.54818 16.2181 9.37478 16.0565 9.2071C15.7115 8.84956 15.3611 8.49696 15.0181 8.13749C14.8989 8.01244 14.7993 7.86916 14.683 7.7411C14.5428 7.5867 14.6932 7.47941 14.7543 7.37576C14.7876 7.31922 14.9224 7.27411 14.9913 7.29373C15.3446 7.39445 15.6906 7.52012 16.0406 7.63273C16.379 7.74164 16.7193 7.84467 17.0585 7.95126C17.6211 8.12799 18.2214 8.19318 18.7448 8.48313C19.1462 8.70551 19.3889 9.04613 19.4123 9.45797Z" fill="#111111"></path>
<path d="M8.66643 10.6174C8.10421 10.586 7.66959 10.3035 7.51802 9.68945C7.44138 9.37902 7.40777 9.07223 7.55973 8.76721C7.70188 8.48181 7.94074 8.31158 8.23111 8.23673C8.93268 8.05584 9.6418 7.90391 10.3466 7.73514C10.8275 7.61998 11.3059 7.49539 11.7859 7.3766C11.8806 7.35312 11.9766 7.32477 12.073 7.3206C12.2092 7.31473 12.2931 7.39915 12.3395 7.5208C12.3792 7.62477 12.4058 7.72371 12.3038 7.81617C11.8587 8.21905 11.4167 8.62525 10.9736 9.03021C10.8208 9.16986 10.6667 9.30812 10.5157 9.44962C10.2598 9.68953 10.001 9.92657 9.7526 10.174C9.45336 10.472 9.08736 10.5973 8.66643 10.6174Z" fill="#111111"></path>
<path d="M9.55425 11.8585C9.60521 11.9649 9.66831 12.0655 9.74098 12.1569C10.1602 12.6845 11.0757 12.5846 11.5004 12.1219C11.6465 11.9627 11.7487 11.7682 11.8174 11.5649C11.9104 11.2894 11.9923 11.0102 12.0809 10.7332C12.3435 9.91218 12.6069 9.09136 12.8701 8.27046C12.9349 8.06833 12.8954 7.98329 12.6585 7.85044C12.5282 7.77737 12.4176 7.85005 12.3297 7.932C11.8026 8.42448 11.2781 8.91974 10.7578 9.41932C10.4384 9.72611 10.1297 10.0439 9.81318 10.3538C9.63773 10.5256 9.50585 10.7363 9.44625 10.9749C9.37234 11.2714 9.42299 11.5843 9.55425 11.8585Z" fill="#111111"></path>
<path d="M7.54959 4.17353C7.52446 3.88551 7.6775 3.59293 7.85809 3.37797C7.94694 3.27223 8.0505 3.17715 8.16814 3.10377C8.32134 3.00815 8.50496 2.96096 8.68251 2.935C8.87578 2.90673 9.07318 2.92149 9.25151 3.00514C9.37351 3.06237 9.48477 3.15444 9.58179 3.24975C10.2648 3.92096 10.9407 4.59912 11.6238 5.27018C11.7828 5.42635 11.9584 5.56616 12.1301 5.70905C12.2658 5.82197 12.2714 5.93845 12.1741 6.1551C12.0974 6.32604 11.957 6.2834 11.8456 6.25366C11.3739 6.12738 10.9076 5.98101 10.4377 5.84754C9.74635 5.6512 9.05902 5.4367 8.3597 5.2728C7.74846 5.12952 7.55861 4.66933 7.54959 4.17353Z" fill="#111111"></path>
<path d="M13.4692 5.33268C14.2528 5.315 14.6337 5.81311 14.6599 6.50618C14.6841 7.15167 14.1526 7.67604 13.522 7.6884C12.8009 7.70246 12.2933 7.09235 12.3151 6.34521C12.328 5.90348 12.8812 5.26811 13.4692 5.33268Z" fill="#111111"></path>
<path d="M6.55589 7.991C5.9301 8.02507 5.24899 7.54711 5.13594 6.88996C5.07361 6.52786 5.18947 6.19759 5.40491 5.90191C5.61156 5.61829 5.91057 5.51448 6.24358 5.50151C6.44735 5.49355 6.65283 5.51896 6.85715 5.53426C6.96133 5.54206 6.98685 5.59087 6.93106 5.6868C6.85217 5.82259 6.75834 5.9091 6.58265 5.88623C6.28372 5.84738 6.01195 5.90331 5.81728 6.15935C5.48334 6.59877 5.52847 7.221 6.11846 7.4623C6.30092 7.53691 6.50352 7.56758 6.69983 7.60226C6.84945 7.62875 6.92943 7.70491 6.92585 7.85545C6.92188 8.02252 6.79623 7.98475 6.69726 7.99054C6.65034 7.99324 6.60304 7.991 6.55589 7.991Z" fill="#111111"></path>
</svg>
</div>
<div class="flex justify-center items-center h-[21px]"><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">1</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">6</span></div>
<div class="flex justify-center items-center h-[21px]"><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">3</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">1</span></div>
<div class="flex justify-center items-center h-[21px]"><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">6</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">5</span></div>
<div class="flex justify-center items-center h-[21px]"><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">8</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">9</span></div>
<div class="flex justify-center items-center h-[21px]"><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">1</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">0</span><span class="" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px);">0</span></div>
</div>
</div><main class="home barba-enter barba-enter-active" data-barba="container" data-barba-namespace="home">
<div class="relative" id="home-main-contents">
<div class="pin-spacer" style="order: 0; place-self: auto; grid-area: auto; z-index: auto; float: none; flex-shrink: 1; display: block; margin: 0px; inset: 0px; position: relative; flex-basis: auto; overflow: visible; box-sizing: border-box; width: 1920px; height: 11880px; padding: 0px 0px 10800px;"><div class="relative" id="wrap-k-f" style="translate: none; rotate: none; scale: none; left: 0px; top: 0.001px; margin: 0px; max-width: 1920px; width: 1920px; max-height: 1080px; height: 1080px; padding: 0px; box-sizing: border-box; position: fixed; transform: translate(0px, 0px);">
<!-- KV -->
<section class="relative w-full h-[100dvh] pt-[calc((100svh-(63vw*1.5))/2)] lg:h-screen lg:pt-0" id="kv-wrap">
<div class="absolute top-[11px] left-1/2 -translate-x-1/2 w-[22.5vw] h-auto mx-auto z-20 lg:w-[116px] lg:top-[20px]" id="kv-logo" style="opacity: 1; visibility: inherit;">
<svg class="fill-white lg:fill-[#111111]" fill="none" height="28" viewbox="0 0 115 28" width="115" xmlns="http://www.w3.org/2000/svg">
<path d="M37.5547 26.8144C37.3942 26.833 37.2195 26.7549 37.0698 26.5937C36.9462 26.4602 36.8125 26.3371 36.6789 26.2154C35.9448 25.5521 35.7292 24.7518 36.0321 23.8056C36.1718 23.3706 36.0482 23.181 35.5956 23.1658C35.2497 23.1541 34.9024 23.1534 34.5566 23.1651C34.304 23.1741 34.0696 23.1188 33.838 23.0282C33.439 22.8726 33.261 22.5495 33.3389 22.1069C33.488 21.2631 33.8023 20.5423 34.5854 20.0983C34.8803 19.9309 35.1201 19.648 35.3565 19.3893C35.5861 19.1389 35.5318 19.0048 35.208 18.9252C34.6875 18.7986 34.165 18.679 33.6465 18.5427C33.3692 18.4701 33.0259 18.4252 32.9796 18.0641C32.9219 17.6131 32.8493 17.1082 33.2583 16.779C34.1308 16.0769 34.8514 15.1985 35.7427 14.5249C36.1812 14.1929 36.5996 13.8318 37.0792 13.5524C37.6836 13.1996 38.8576 13.3988 39.3452 14.0642C39.7851 14.666 40.3076 15.2061 40.7167 15.8411C40.8234 16.0078 41.0007 16.1302 41.1559 16.2595C41.3439 16.4159 41.5018 16.3066 41.5474 16.1261C41.6676 15.6509 42.0014 15.3915 42.3614 15.1266C42.708 14.8714 42.9424 14.4917 43.2332 14.1742C43.5972 13.7772 44.0096 13.4542 44.477 13.1948C44.745 13.0461 45.0062 12.9645 45.2574 13.1713C45.5247 13.3898 45.7316 13.67 45.7202 14.0455C45.7135 14.2613 45.6423 14.4751 45.6295 14.6915C45.6121 14.9793 45.4891 15.1971 45.2769 15.3673C44.933 15.6426 44.8229 16.0341 44.7551 16.4484C44.7316 16.5909 44.7618 16.7444 44.8498 16.8371C44.9626 16.956 45.0694 16.8101 45.1715 16.7548C45.4871 16.5846 45.8269 16.595 46.1534 16.6649C46.4643 16.7313 46.6101 16.6413 46.6786 16.3239C46.8505 15.5202 47.2642 14.8693 47.9043 14.3727C48.2408 14.1126 48.3737 14.1479 48.553 14.5421C48.5866 14.6148 48.6074 14.7068 48.6632 14.7538C49.0568 15.0858 49.177 15.5423 49.2388 16.0313C49.2703 16.2831 49.3791 16.3495 49.6511 16.3135C50.1535 16.2471 50.6209 16.0507 51.1159 15.9677C51.5142 15.9013 51.7123 16.0444 51.7506 16.4615C51.7842 16.8253 51.6982 17.1732 51.5646 17.5032C51.4625 17.757 51.3335 18.0019 51.1938 18.2363C51.0239 18.5213 50.7499 18.6513 50.4336 18.6617C50.1663 18.6707 50.1314 18.8007 50.2173 19.0207C50.271 19.159 50.3221 19.2987 50.3792 19.4357C50.5041 19.7379 50.4107 19.9164 50.087 19.9046C49.7573 19.8929 49.4725 19.9869 49.1992 20.1564C48.9218 20.3279 48.8909 20.5036 49.0809 20.7796C49.2314 20.9975 49.4759 21.1641 49.4349 21.4892C49.414 21.6566 49.3704 21.7963 49.2374 21.907C48.7378 22.3248 48.2421 22.746 47.8183 23.2509C47.5738 23.5421 47.1292 23.504 46.9815 23.1382C46.7504 22.5675 46.3394 22.1463 45.9707 21.6926C45.7215 21.3862 45.7551 21.1164 45.9156 20.8114C45.9579 20.7305 46.0056 20.6523 46.0486 20.5721C46.3199 20.0637 46.2743 19.9344 45.7544 19.7338C45.4105 19.601 45.3629 19.4122 45.6 19.1279C45.6772 19.0352 45.8055 18.9619 45.735 18.8076C45.6711 18.6693 45.5422 18.6624 45.4173 18.6679C45.2212 18.6762 45.0419 18.6347 44.8746 18.5268C44.7013 18.4141 44.6006 18.4936 44.5569 18.6755C44.4602 19.0767 44.3494 19.4758 44.2809 19.8818C44.2191 20.247 44.3991 20.5818 44.4992 20.9172C44.6476 21.4152 44.6483 21.8855 44.4562 22.3635C44.2816 22.7972 44.2628 23.291 43.9928 23.6866C43.7107 24.0996 43.4542 24.5215 43.2789 24.9994C43.1969 25.2235 42.9377 25.2194 42.7543 25.016C42.5025 24.7373 42.3883 24.3852 42.3084 24.0249C42.217 23.6106 42.1378 23.1949 42.1385 22.764C42.1391 22.34 41.9101 22.2058 41.5824 22.4465C41.0981 22.8027 40.6004 22.9749 39.9873 22.8843C39.4076 22.7986 38.8388 22.9957 38.2793 23.1575C38.0067 23.2364 37.957 23.4861 37.9691 23.7081C37.9993 24.2337 38.0449 24.7656 38.3915 25.1993C38.7004 25.5846 38.5675 25.9788 38.4097 26.3648C38.2625 26.7237 37.955 26.8109 37.5547 26.8144ZM37.4579 21.8219C38.0658 21.9001 38.5661 21.6428 39.0571 21.3696C39.2532 21.261 39.3029 21.1213 39.1451 20.8951C38.8845 20.5223 38.5298 20.2615 38.2028 19.9689C38.0476 19.8306 37.8502 19.8271 37.6393 19.9309C37.0933 20.2 36.648 20.5845 36.2699 21.0652C36.0301 21.3696 36.0765 21.5757 36.4546 21.6822C36.787 21.7769 37.1268 21.8523 37.4579 21.8219ZM42.2661 17.5571C42.1109 18.1464 41.6636 18.4335 41.2371 18.7468C40.9947 18.9245 40.7502 18.9902 40.4527 18.9377C40.225 18.8969 39.9826 18.8934 39.7596 18.9902C39.5393 19.085 39.5218 19.251 39.7112 19.4025C39.7717 19.4509 39.8482 19.4792 39.9107 19.5263C40.5722 20.0236 41.4286 20.1965 41.9927 20.8584C42.1096 20.9961 42.1922 20.9103 42.2244 20.774C42.3097 20.4061 42.389 20.0395 42.3379 19.6529C42.2842 19.2455 42.2916 18.8346 42.3352 18.4245C42.3661 18.136 42.3991 17.8435 42.2661 17.5571ZM38.3378 17.2272C38.5084 17.2493 38.5957 17.2016 38.6635 17.1193C38.7488 17.0155 38.6776 16.9208 38.6245 16.8385C38.4258 16.5307 38.1887 16.2595 37.8717 16.0749C37.7327 15.9946 37.5842 15.9372 37.4257 16.0057C37.098 16.1475 36.7595 16.2755 36.4989 16.5376C36.3619 16.6752 36.1181 16.8004 36.2128 17.0314C36.3061 17.2597 36.5909 17.2542 36.7769 17.2251C37.3216 17.1407 37.8582 17.0909 38.3378 17.2272Z" fill="inherit"></path>
<path d="M33.9064 10.078C33.9057 9.03289 33.9017 7.9871 33.9084 6.94198C33.9104 6.5422 33.8674 6.1452 33.8762 5.74196C33.8896 5.14158 34.2342 4.88844 34.6559 4.65258C34.8319 4.55366 35.0173 4.47413 35.1979 4.38421C35.654 4.15667 36.0865 4.39806 36.5271 4.45754C36.6674 4.4762 36.797 4.56958 36.9374 4.60139C37.2088 4.66226 37.1899 4.82482 37.0798 5.00879C36.9515 5.2239 36.8286 5.45008 36.6614 5.63059C36.2161 6.10991 36.2282 6.67708 36.2907 7.27538C36.3639 7.98157 36.4841 8.6829 36.4854 9.39601C36.4861 10.0102 36.9831 10.4563 37.5728 10.3941C38.065 10.3422 38.5023 10.0607 38.9972 10.0324C39.5896 9.99845 40.1585 9.75084 40.7461 9.76882C41.3566 9.7875 41.9436 9.73009 42.5306 9.57655C43.0787 9.43268 43.6442 9.37942 44.1983 9.25493C44.9491 9.08615 45.7248 9.12074 46.4938 9.16154C46.8061 9.17813 47.1198 9.1726 47.4327 9.16914C47.7477 9.16501 47.9634 9.01698 48.0493 8.69329C48.2111 8.08392 48.3435 7.46696 48.4415 6.84377C48.4999 6.47096 48.3797 6.34094 48.0043 6.35962C47.371 6.39073 46.7383 6.35892 46.1043 6.4855C45.6718 6.57196 45.4145 6.77599 45.2836 7.17854C45.1358 7.63297 44.8256 7.93245 44.4367 8.16139C44.125 8.34469 43.8745 8.30665 43.6193 8.05004C43.4185 7.84739 43.2305 7.63297 43.1203 7.36461C43.0169 7.11353 42.861 6.98764 42.5837 6.9911C42.2741 6.99526 42.0048 7.06164 41.8328 7.34523C41.769 7.45037 41.722 7.57486 41.7012 7.69659C41.6125 8.20635 41.2794 8.54804 40.9396 8.87866C40.6629 9.14772 40.3768 9.13457 40.1209 8.83093C39.9033 8.57225 39.6474 8.33846 39.5043 8.01961C39.4318 7.85984 39.3142 7.81832 39.1538 7.81695C38.7763 7.81279 38.4237 7.93868 38.0805 8.0611C37.4311 8.2935 37.0657 7.90064 36.7325 7.4559C36.4256 7.04575 36.5277 6.59754 36.7608 6.19569C36.9341 5.89689 37.1906 5.67209 37.5546 5.62782C37.8965 5.58633 38.237 5.52062 38.5802 5.50057C39.0624 5.47289 39.2632 5.33941 39.3854 4.8497C39.4647 4.52808 39.5325 4.19954 39.5594 3.86962C39.5923 3.46361 39.7414 3.11431 39.9563 2.78647C40.2565 2.32929 40.7038 2.24283 41.1444 2.56306C41.7757 3.02095 42.1451 3.59363 41.9363 4.43886C41.859 4.75288 41.896 5.07934 41.9685 5.3975C41.9933 5.5061 42.031 5.59395 42.1471 5.62023C42.4682 5.69216 43.0679 5.22321 43.0861 4.88153C43.1156 4.30537 43.0793 3.7306 43.0048 3.16134C42.9154 2.48766 43.0767 1.85411 43.3016 1.24614C43.4581 0.824211 43.8591 0.636773 44.2816 0.546853C44.395 0.522663 44.5059 0.555845 44.5972 0.631939C44.9505 0.928653 45.3266 1.20325 45.6483 1.53318C45.9955 1.88801 46.1647 2.32374 46.0103 2.85148C45.8981 3.23537 45.886 3.64414 45.7517 4.02662C45.7087 4.14905 45.7289 4.30191 45.7437 4.43746C45.7886 4.86146 45.923 4.98735 46.3346 4.9327C46.7087 4.8836 47.0916 4.86631 47.4489 4.72451C47.7585 4.60139 48.0862 4.63252 48.4052 4.59862C48.8566 4.55022 49.2555 4.32682 49.6867 4.21269C50.3395 4.04047 51.04 4.32127 51.2348 4.92993C51.3073 5.1575 51.4134 5.37398 51.4906 5.60085C51.6424 6.04766 51.5303 6.43085 51.224 6.77462C50.8627 7.17924 50.6525 7.64473 50.7042 8.21466C50.7794 9.03912 50.4859 9.7453 50.0225 10.3934C49.9366 10.5137 49.8506 10.6355 49.7787 10.7648C49.3845 11.4793 48.733 11.7546 47.9996 11.4344C47.4986 11.2158 46.9949 11.139 46.4723 11.2103C45.9767 11.2787 45.4844 11.1715 44.98 11.2822C44.4017 11.4095 43.8396 11.5927 43.2647 11.7221C42.2136 11.9586 41.1686 12.2194 40.0705 12.1807C39.2935 12.1537 38.5123 12.2574 37.7326 12.2948C37.4512 12.3086 37.2403 12.5196 36.981 12.5694C36.2033 12.7181 35.4847 12.5071 34.8017 12.1309C34.7829 12.1205 34.7694 12.0963 34.75 12.088C33.9648 11.7532 33.7996 11.1058 33.9057 10.3194C33.9158 10.2405 33.9071 10.1589 33.9064 10.078Z" fill="inherit"></path>
<path d="M6.72619 10.4578C6.77317 9.66452 6.88331 8.84211 6.85109 8.01213C6.84502 7.86273 6.83228 7.71402 6.81817 7.56599C6.7987 7.37302 6.74697 7.19181 6.5334 7.14962C6.30844 7.10466 6.11299 7.13303 5.92561 7.3253C5.45412 7.80808 5.06326 8.40569 4.49035 8.7439C3.91343 9.0842 3.29756 9.4577 2.54736 9.15061C2.11551 8.97353 1.64742 8.89539 1.22229 8.67958C0.755511 8.44303 0.424376 8.05916 0.10808 7.66077C-0.0128472 7.5086 -0.0920852 7.27757 0.207445 7.21119C0.775638 7.0853 1.13899 6.63779 1.57085 6.30785C1.98589 5.99109 2.3929 5.67637 2.88518 5.50069C3.16593 5.4004 3.39697 5.20811 3.61455 5.0186C4.1868 4.5206 4.8228 4.11666 5.42862 3.66917C5.6301 3.52045 5.86113 3.44437 6.11434 3.41879C6.82018 3.34546 7.43605 3.46166 8.05528 3.90847C8.57781 4.28614 9.12788 4.64026 9.66988 4.99716C10.457 5.5173 10.6968 6.20896 10.5121 7.12127C10.3126 8.10758 10.1561 9.10288 9.95932 10.0906C9.70882 11.3494 9.35354 12.5826 9.14198 13.8498C9.06139 14.3333 8.93645 14.8229 8.60134 15.2054C8.10029 15.7774 7.48508 16.0562 6.7275 15.9365C6.61735 15.9185 6.50655 15.8978 6.40042 15.8653C6.30508 15.8355 6.22715 15.8501 6.15058 15.9116C6.0055 16.0271 5.98337 16.5431 6.10155 16.8232C6.20702 17.075 6.4078 17.0695 6.60862 17.0245C6.89073 16.9616 7.17345 16.8917 7.44478 16.7921C8.30849 16.4753 8.82831 17.1179 8.88812 17.8538C8.92841 18.358 8.79338 18.8311 8.66647 19.3063C8.48045 20.0028 8.23663 20.6903 8.10431 21.3924C7.92767 22.3323 7.90353 23.3014 7.80141 24.2566C7.74029 24.8306 7.67581 25.4213 7.13316 25.7602C6.69057 26.0362 6.1714 26.0175 5.67912 26.057C5.46355 26.0742 5.30232 25.8474 5.14721 25.6786C4.67035 25.1606 4.56492 24.4454 4.22238 23.8554C4.09277 23.632 3.9618 23.4003 3.91412 23.1333C3.85768 22.8179 3.93626 22.6982 4.24386 22.6872C4.34327 22.683 4.44469 22.6982 4.54274 22.7169C5.00751 22.8034 5.08609 22.7674 5.14049 22.2971C5.20025 21.7832 5.33389 21.2852 5.40911 20.7761C5.45211 20.4808 5.40374 20.1833 5.40509 19.888C5.4071 19.4972 4.9773 19.2925 4.64616 19.4889C4.52191 19.5629 4.41312 19.6563 4.27276 19.7102C4.04841 19.7974 3.86977 19.7614 3.68104 19.6183C3.05845 19.1452 2.98528 18.9902 3.19147 18.2239C3.32175 17.7383 3.48694 17.2611 3.66829 16.7928C3.95708 16.0465 4.13171 15.2884 4.08875 14.4764C4.06186 13.9819 4.31105 13.7612 4.79595 13.8152C5.07199 13.8456 5.34332 13.9231 5.61735 13.9763C5.99075 14.0497 6.06462 13.9985 6.14926 13.6257C6.34672 12.7556 6.5764 11.8924 6.70266 11.0057C6.7275 10.8327 6.73021 10.6633 6.72619 10.4578Z" fill="inherit"></path>
<path d="M100.893 9.33389C100.891 8.92372 100.996 8.75357 101.601 8.61802C101.826 8.5682 102.062 8.55644 102.298 8.60349C102.889 8.72107 103.456 8.51357 104.009 8.37869C105.15 8.09994 106.303 7.91666 107.457 7.72023C108.059 7.61716 108.674 7.61163 109.28 7.54592C110.05 7.46223 110.837 7.43802 111.58 7.1835C111.862 7.08666 112.14 6.94072 112.38 6.7602C112.641 6.56308 112.897 6.57899 113.196 6.59421C113.946 6.63362 114.365 7.14477 114.747 7.69533C115.028 8.10134 114.885 8.60003 114.787 9.00189C114.361 10.7456 113.724 12.4145 112.784 13.9362C112.547 14.3201 112.322 14.7904 111.808 14.9232C111.662 14.9613 111.514 14.9896 111.382 14.9211C111.051 14.7482 110.713 14.7386 110.347 14.7558C109.749 14.7835 109.148 14.7413 108.551 14.7399C107.886 14.7386 107.22 14.8956 106.578 15.0802C105.741 15.3216 104.896 15.5305 104.051 15.738C103.42 15.8929 102.991 15.4897 102.631 15.0277C101.969 14.1769 101.632 13.1927 101.567 12.1102C101.528 11.4697 101.359 10.8722 101.086 10.3001C100.956 10.0283 100.888 9.73921 100.893 9.33389ZM106.139 13.4043C106.534 13.4977 106.823 13.3103 107.136 13.304C108.095 13.2847 109.055 13.3386 110.016 13.2722C110.234 13.257 110.361 13.1726 110.498 13.0301C110.719 12.7991 110.741 12.4941 110.802 12.2064C110.964 11.4442 111.304 10.7345 111.404 9.95362C111.47 9.44386 111.249 9.21906 110.772 9.3733C110.541 9.44733 110.311 9.48467 110.079 9.45146C108.983 9.29377 107.886 9.26333 106.784 9.35878C106.544 9.37953 106.297 9.43694 106.052 9.37676C105.54 9.25087 105.058 9.36363 104.583 9.56352C104.32 9.6735 104.199 9.85264 104.205 10.1445C104.223 11.0215 104.047 11.8792 103.926 12.7396C103.804 13.6049 104.277 13.7149 104.894 13.6305C104.393 13.2279 104.167 12.6968 104.4 12.2513C104.785 11.5154 105.126 10.7511 105.62 10.0802C105.8 9.83603 106.02 9.70738 106.336 9.7219C106.726 9.73988 107.117 9.70461 107.508 9.70324C107.709 9.70185 107.901 9.7219 108.048 9.9128C108.134 10.0249 108.272 10.0989 108.396 10.1749C108.749 10.3914 109.129 10.5692 109.457 10.8189C109.839 11.1094 110 11.5216 109.917 12.0286C109.842 12.4934 109.499 12.5474 109.148 12.6587C108.535 12.8531 107.901 12.8925 107.279 13.0135C106.905 13.0855 106.515 13.1456 106.139 13.4043ZM107.571 11.6267C107.564 11.4262 107.094 10.8431 106.907 10.7995C106.811 10.7767 106.725 10.7988 106.662 10.8722C106.373 11.2111 106.153 11.5963 106.017 12.0231C105.956 12.2154 106.05 12.3689 106.266 12.2617C106.63 12.0826 107.023 11.9968 107.395 11.8481C107.492 11.8086 107.576 11.7547 107.571 11.6267Z" fill="inherit"></path>
<path d="M104.177 22.2254C104.167 21.7302 104.241 21.2425 104.293 20.7521C104.341 20.2977 104.131 19.8758 104.097 19.4304C104.04 18.6778 103.69 18.0277 103.423 17.3491C103.207 16.7993 103.454 16.4009 104.024 16.3276C104.528 16.2632 105.02 16.3283 105.517 16.3801C105.811 16.4106 106.036 16.5945 106.258 16.7599C106.783 17.1486 107.323 17.0573 107.877 16.874C108.332 16.7239 108.697 16.4196 109.085 16.1498C109.701 15.7224 110.429 15.5557 111.071 15.9153C111.521 16.1671 111.947 16.5703 112.268 17.0303C112.338 17.1306 112.314 17.2406 112.288 17.3595C112.17 17.9122 111.97 18.4358 111.787 18.9669C111.55 19.6531 111.47 20.3745 111.384 21.0924C111.345 21.4224 111.342 21.7571 111.309 22.0877C111.217 23.0104 110.178 23.7007 109.333 23.6219C108.818 23.5734 108.575 23.1757 108.707 22.6611C108.733 22.5615 108.756 22.4557 108.806 22.3685C109.253 21.596 109.159 20.8075 108.792 20.0625C108.519 19.5092 108.588 18.9794 108.687 18.4254C108.732 18.1798 108.844 17.8886 108.65 17.7067C108.456 17.5262 108.235 17.7586 108.034 17.834C107.48 18.0429 106.946 18.3036 106.356 18.4102C106.07 18.4613 105.887 18.7567 105.969 19.106C106.139 19.8426 106.186 20.6055 106.422 21.3269C106.454 21.4231 106.459 21.5635 106.562 21.5808C106.699 21.6036 106.736 21.4466 106.77 21.3518C106.905 20.9811 106.925 20.6062 106.697 20.2624C106.454 19.8966 106.434 19.4809 106.46 19.0693C106.488 18.6246 106.898 18.5679 107.365 18.7823C108.182 19.1578 108.246 19.956 108.498 20.6608C108.695 21.2135 108.656 21.7537 108.487 22.3125C108.339 22.7995 108.348 23.3286 108.103 23.7975C107.987 24.0196 108.13 24.1434 108.355 24.1309C108.666 24.1129 108.977 24.0687 109.27 23.9504C110.315 23.5278 111.344 23.6053 112.366 24.0403C112.562 24.124 112.757 24.2167 112.961 24.2706C113.222 24.3405 113.215 24.4823 113.108 24.6787C112.977 24.9194 112.845 25.1594 112.721 25.4036C112.661 25.5226 112.583 25.618 112.462 25.6602C111.566 25.97 110.683 26.1976 109.728 25.8255C109.02 25.5495 108.264 25.618 107.512 25.7148C106.873 25.7965 106.422 26.0842 106.09 26.6506C105.875 27.0165 105.487 27.0504 105.103 27.0103C104.345 26.9315 103.711 26.5746 103.123 26.0973C102.916 25.9299 102.89 25.7598 102.979 25.5288C103.185 24.9969 103.6 24.8032 104.114 24.7735C104.526 24.75 104.922 24.6151 105.341 24.6165C105.586 24.6172 105.806 24.4643 105.983 24.2775C106.282 23.9615 106.495 23.5824 106.696 23.1979C106.756 23.0844 106.808 22.9184 106.646 22.8327C106.507 22.7587 106.423 22.8832 106.339 22.9765C106.332 22.9848 106.322 22.9931 106.319 23.0035C106.071 23.8017 105.48 23.635 104.923 23.5167C104.391 23.4033 104.181 23.1086 104.177 22.5359V22.2254Z" fill="inherit"></path>
<path d="M78.6093 26.6563C77.4151 26.8333 76.2868 26.206 75.2384 25.4493C74.6138 24.9977 74.1242 24.3669 73.8327 23.604C73.3787 22.415 73.1591 21.1721 73.0705 19.9105C73.0214 19.216 73.0846 18.5099 73.1329 17.8113C73.1625 17.3873 73.1631 16.964 73.1739 16.5407C73.196 15.6443 73.3498 14.7624 73.4587 13.8764C73.5036 13.5077 73.8273 13.2581 74.0134 12.9475C74.5352 12.0781 75.3755 11.5801 76.1391 10.9915C76.3446 10.8331 76.5541 10.6809 76.7657 10.5322C77.1418 10.268 77.5844 10.3434 77.8618 10.7093C78.2976 11.284 78.5354 11.9508 78.6677 12.6535C78.7892 13.2968 78.2097 13.8356 77.5347 13.7215C77.3567 13.6917 77.1814 13.6737 77.0034 13.6737C76.4171 13.6737 76.1202 13.9054 75.9739 14.4913C75.8328 15.0564 75.8711 15.6332 75.8973 16.2066C75.9443 17.2151 76.1169 18.2124 76.1337 19.2243C76.1451 19.9119 76.1968 20.5987 76.2277 21.2862C76.2338 21.419 76.2875 21.5338 76.3486 21.641C76.6864 22.2421 76.9517 22.8867 77.6012 23.254C77.9833 23.4705 78.3789 23.4421 78.7678 23.4788C79.0619 23.5064 79.5247 22.886 79.6134 22.5077C79.7853 21.7731 79.8296 21.0206 79.9391 20.2771C79.9807 19.9914 80.1406 19.7708 80.3326 19.5723C80.5294 19.3682 80.7316 19.3952 80.8605 19.6241C81.0593 19.9782 81.3172 20.3103 81.3716 20.7363C81.4529 21.372 81.7531 21.9606 81.741 22.6163C81.7269 23.384 81.7276 24.1524 81.7068 24.9202C81.7007 25.1464 81.561 25.3345 81.4173 25.4922C80.6234 26.3672 80.0915 26.7344 78.6093 26.6563Z" fill="inherit"></path>
<path d="M18.0182 3.87276C18.0061 4.54161 17.7455 5.10462 17.3734 5.54384C16.6319 6.41811 16.2148 7.46943 15.6702 8.45437C14.9186 9.81418 14.5157 11.313 13.9649 12.7531C13.8192 13.1335 13.7017 13.536 13.701 13.9565C13.7003 14.339 13.799 14.4753 14.1637 14.4815C14.877 14.494 15.5869 14.4227 16.2934 14.3266C16.5634 14.2899 16.6668 14.0562 16.7542 13.8279C16.9456 13.3265 17.1309 12.8229 17.3176 12.3201C17.4439 11.9812 17.6844 11.7169 17.8818 11.4258C18.0336 11.2003 18.2512 11.2127 18.4009 11.4389C18.4634 11.5323 18.5111 11.6395 18.5501 11.7467C18.8637 12.6092 19.2902 13.4171 19.7019 14.2311C19.8389 14.5023 19.8617 14.7755 19.722 15.021C19.5031 15.4056 19.2425 15.7666 18.9879 16.1284C18.8556 16.3179 18.6494 16.3421 18.4346 16.3926C17.8999 16.5185 17.4137 16.3974 16.9046 16.2549C16.5191 16.147 16.1014 16.284 15.7064 16.2639C14.7762 16.2176 13.9246 16.4521 13.075 16.8048C12.4988 17.0441 11.9642 16.9086 11.5222 16.4189C11.3423 16.2197 11.1153 16.062 10.8983 15.9015C10.5256 15.6249 10.3798 15.2472 10.5276 14.7976C10.631 14.4829 10.7479 14.1661 10.9077 13.8791C11.3241 13.1328 11.3705 12.2779 11.6075 11.4804C11.9239 10.4152 12.1805 9.33417 12.3705 8.23995C12.6016 6.90918 13.1207 5.71815 13.7957 4.56166C14.472 3.40313 15.2786 2.35941 16.1134 1.33021C16.3545 1.0321 16.6689 1.00375 16.9986 1.11373C17.3183 1.22093 17.5762 1.38625 17.63 1.78741C17.7052 2.34282 17.7636 2.90307 17.9631 3.43494C18.0175 3.57951 18.0047 3.75103 18.0182 3.87276Z" fill="inherit"></path>
<path d="M71.9513 6.31868C72.408 6.22878 72.8761 6.20941 73.3382 6.14855C73.8815 6.07662 74.0346 5.88502 74.0917 5.31648C74.1347 4.88972 74.1958 4.46019 74.0951 4.02997C74.0682 3.91584 74.04 3.80656 73.9735 3.71318C73.7479 3.39709 73.8069 3.08446 73.989 2.78013C74.1347 2.5353 74.2972 2.30151 74.4477 2.06011C74.5948 1.82495 74.7942 1.71428 75.0696 1.74818C76.1455 1.88028 77.0999 2.31949 77.8487 3.11076C78.3854 3.67724 78.3941 4.12958 77.707 4.47955C77.3799 4.64556 77.112 4.91253 76.748 5.02873C76.5163 5.10274 76.4269 5.33723 76.4988 5.59797C76.5753 5.87256 76.7863 5.79233 76.9696 5.78127C77.9273 5.72247 78.8152 5.41469 79.6715 4.98654C80.0275 4.80949 80.4929 5.0246 80.6931 5.4458C81.2048 6.5179 80.8267 7.36034 79.7152 7.70963C79.3169 7.83482 78.9482 7.87355 78.5439 7.75735C78.1805 7.65222 77.8004 7.60795 77.4263 7.54571C77.1301 7.49659 76.9602 7.6702 76.8057 7.90952C76.4793 8.41375 76.21 8.95947 75.7687 9.38207C75.306 9.82543 75.2241 9.80953 74.7613 9.37378C74.4121 9.04523 74.2334 8.70077 74.2758 8.2028C74.31 7.80092 74.2079 7.73245 73.8123 7.7449C73.4235 7.75735 73.0333 7.71862 72.6451 7.80785C72.3469 7.87702 72.144 8.00221 72.0883 8.34804C72.0198 8.77203 71.9667 9.20293 71.7518 9.59233C71.4476 10.1443 70.9284 10.248 70.4529 9.82336C70.1379 9.54116 69.8726 9.21055 69.7067 8.8163C69.5979 8.55829 69.4522 8.52648 69.2332 8.63369C68.7295 8.87992 68.2198 9.1151 67.7234 9.37654C67.1143 9.69747 66.5354 9.63385 65.9799 9.26104C65.753 9.10818 65.5145 8.99681 65.26 8.90413C65.0028 8.81077 64.7408 8.67449 64.7354 8.34804C64.7301 8.0437 64.9584 7.87632 65.2096 7.7864C65.6931 7.61348 66.0565 7.23791 66.5025 7.00137C66.753 6.86856 66.9954 6.80769 67.2674 6.86787C67.5079 6.92112 67.7503 6.94603 67.9975 6.9405C68.5469 6.92944 68.8639 6.55593 68.7087 6.00951C68.5187 5.3386 68.2863 4.67737 67.7772 4.16968C67.6026 3.9947 67.6032 3.76023 67.6973 3.55825C67.9699 2.97242 68.2816 2.45574 69.0116 2.3437C69.6678 2.24339 70.2937 2.2282 70.9143 2.47719C71.3804 2.66393 71.4637 2.85346 71.32 3.36182C71.1964 3.79896 71.0164 4.23125 71.1185 4.70228C71.2078 5.11589 71.3092 5.52604 71.408 5.93758C71.4771 6.22809 71.6464 6.37196 71.9513 6.31868Z" fill="inherit"></path>
<path d="M16.1351 22.8678C16.1251 23.1023 16.1069 23.2628 16.1143 23.4232C16.1231 23.6314 16.2164 23.7345 16.4145 23.5961C16.706 23.3935 16.9713 23.1625 17.0284 22.7758C17.062 22.551 17.1875 22.4431 17.3904 22.3816C17.974 22.2045 18.5698 22.199 19.1648 22.2502C19.4697 22.2764 19.7444 22.4134 19.917 22.7053C20.4126 23.5436 20.6914 24.4282 20.4993 25.4235C20.4214 25.8275 20.3086 25.8883 19.9251 25.8088C19.1057 25.64 18.2823 25.4934 17.4804 25.2458C17.4038 25.2223 17.3266 25.234 17.254 25.2776C16.6852 25.6151 16.0841 25.8994 15.5844 26.3531C15.1814 26.719 14.8685 26.6706 14.4883 26.2653C13.9651 25.7071 13.7724 25.0549 13.804 24.2913C13.8234 23.8119 13.7307 23.3319 13.7368 22.8478C13.7415 22.5338 13.5434 22.3131 13.2767 22.1872C13.0148 22.0641 12.7932 22.1935 12.6172 22.3878C12.3264 22.7087 11.9866 22.8242 11.5627 22.7502C11.0873 22.6672 10.8401 22.3456 10.7427 21.9106C10.5896 21.2251 10.7494 20.5411 10.826 19.8591C10.8361 19.7678 10.8683 19.6772 10.8656 19.5873C10.8394 18.8423 11.2626 18.3519 11.7629 17.9093C11.9254 17.7654 12.1343 17.7433 12.3345 17.8076C12.636 17.9045 12.9456 17.9266 13.2552 17.9681C13.5481 18.0068 13.7025 17.8858 13.7684 17.6139C13.8147 17.4251 13.8335 17.2301 13.8792 17.0412C13.9866 16.5979 14.2089 16.472 14.624 16.6242C15.207 16.8372 15.757 17.1063 16.1378 17.6402C16.2715 17.8277 16.4609 17.8291 16.6536 17.7931C17.1513 17.6997 17.6543 17.7301 18.1554 17.7211C18.4945 17.7149 18.7706 17.8408 19.0177 18.1016C19.2548 18.3526 19.4301 18.6418 19.6262 18.9191C19.7975 19.1605 19.8263 19.4579 19.7283 19.7118C19.4119 20.53 18.9431 21.2535 18.2897 21.8296C18.0257 22.0627 17.6967 22.0738 17.3756 21.9541C17.1452 21.8677 16.9243 21.7529 16.6792 21.7162C16.2735 21.6553 16.0418 21.9085 16.0989 22.3221C16.1271 22.5261 16.1271 22.7343 16.1351 22.8678ZM16.2574 19.7304C16.254 20.1952 16.3098 20.2575 16.7711 20.3426C16.9565 20.3764 17.1338 20.3758 17.2446 20.189C17.3615 19.9926 17.2883 19.801 17.1668 19.6488C16.9841 19.4213 16.7121 19.3556 16.4448 19.3791C16.2459 19.3964 16.2533 19.5983 16.2574 19.7304ZM13.5427 19.9082C13.5494 19.6481 13.3956 19.5852 13.2163 19.579C12.8966 19.5679 12.5776 19.9144 12.587 20.254C12.5924 20.4588 12.6233 20.6545 12.8892 20.6566C13.2385 20.6587 13.5434 20.2962 13.5427 19.9082Z" fill="inherit"></path>
<path d="M97.4578 21.5705C96.5377 21.6044 95.8453 21.1216 95.1132 20.773C94.9137 20.6776 94.7317 20.4853 94.7908 20.2204C94.8506 19.9576 95.0481 19.7888 95.3012 19.7293C95.6256 19.6539 95.903 19.4935 96.1683 19.2984C96.302 19.2002 96.3597 19.0716 96.3107 18.9097C96.2375 18.6683 96.1609 18.4276 96.0696 18.1925C96.0004 18.014 95.8956 17.9566 95.7546 18.142C95.6377 18.2955 95.5188 18.4491 95.3845 18.5853C95.1582 18.8129 94.9842 18.781 94.7613 18.5569C93.9795 17.7726 93.3838 16.8555 92.8828 15.8636C92.7807 15.6623 92.6282 15.4887 92.4966 15.304C92.328 15.0682 92.416 14.9361 92.6517 14.8344C93.2615 14.5702 93.911 14.546 94.5544 14.4989C95.0769 14.4609 95.1179 14.4346 95.1233 13.8903C95.1253 13.669 95.0843 13.4594 95.0319 13.2457C94.8331 12.4288 95.226 11.8361 96.0306 11.7205C96.2066 11.6956 96.3832 11.6714 96.5605 11.6583C96.8762 11.6334 97.124 11.8374 97.206 12.2331C97.3477 12.9116 97.6606 13.5299 97.8399 14.1925C98.0031 14.7971 98.4391 14.9319 98.9595 14.9575C99.2705 14.9728 99.5868 14.9133 99.8884 15.0364C100.011 15.0862 100.161 15.1028 100.177 15.2805C100.191 15.4424 100.084 15.5385 99.9723 15.6139C99.6097 15.8574 99.2604 16.1251 98.8642 16.3125C98.5841 16.4446 98.4525 16.6576 98.5129 16.9751C98.5975 17.4226 98.8527 17.5388 99.1845 17.249C99.8252 16.6881 100.61 16.3471 101.251 15.7875C101.399 15.6582 101.6 15.6693 101.761 15.7529C101.929 15.8415 101.873 16.0351 101.864 16.1922C101.849 16.4363 101.704 16.6175 101.563 16.7953C101.046 17.4413 100.437 18.0313 100.032 18.7444C99.609 19.4886 99.0495 20.1754 98.8507 21.0442C98.8185 21.1853 98.7433 21.2614 98.6284 21.3222C98.2402 21.5256 97.8306 21.6072 97.4578 21.5705Z" fill="inherit"></path>
<path d="M66.7329 23.5037C66.7295 22.7132 66.9807 22.099 67.0935 21.4578C67.2265 20.7052 67.2131 19.9811 66.9934 19.2534C66.929 19.039 66.7846 18.9941 66.6059 19.0183C66.16 19.0791 65.7247 19.0778 65.2915 18.918C64.7872 18.7312 64.6139 18.5217 64.6367 18.061C64.675 17.2946 64.8147 17.0871 65.3957 16.9578C66.5307 16.7053 67.2581 15.9805 67.6657 14.8849C67.9491 14.1234 68.2353 13.3639 68.512 12.5996C68.751 11.9377 69.1695 11.7779 69.7598 12.1487C70.1104 12.3693 70.3415 12.7013 70.5617 13.0554C70.8492 13.5182 71.1997 13.9415 71.4429 14.4325C71.8398 15.2349 71.6135 15.8594 70.8398 16.2931C70.3911 16.5435 69.9459 16.816 69.5913 17.2061C69.2984 17.5284 69.2507 17.8418 69.4798 18.2076C69.97 18.9892 70.056 19.8434 69.9976 20.744C69.9237 21.8866 69.737 23.0189 69.7182 24.1677C69.7094 24.719 69.4092 25.145 69.0042 25.4798C68.6899 25.7406 68.3683 25.6956 68.0835 25.4113C67.8108 25.1395 67.5375 24.8677 67.2473 24.6166C66.8679 24.2888 66.6583 23.8994 66.7329 23.5037Z" fill="inherit"></path>
<path d="M107.49 6.71291C107.219 6.77518 106.946 6.65413 106.663 6.53309C105.79 6.15682 104.929 6.35948 104.093 6.66797C103.552 6.86786 103.011 7.06913 102.524 7.41566C102.172 7.66534 101.769 7.54708 101.464 7.22613C101.122 6.86577 100.744 6.54485 100.524 6.07313C100.368 5.74252 100.325 5.44857 100.567 5.14907C100.802 4.85856 101.026 4.55769 101.264 4.26927C101.507 3.97602 101.831 3.98845 102.125 4.12402C103.013 4.53417 103.946 4.3834 104.863 4.37371C105.297 4.36957 105.593 3.97946 105.577 3.52642C105.567 3.25322 105.521 2.98002 105.477 2.70889C105.394 2.2005 105.526 1.89547 106.017 1.81317C106.698 1.69904 107.322 1.45142 107.947 1.17062C108.288 1.01776 108.524 1.15747 108.649 1.51783C108.806 1.97363 108.815 2.41354 108.567 2.84514C108.489 2.98139 108.436 3.13839 108.4 3.29334C108.288 3.76437 108.479 3.92897 108.934 3.8439C109.788 3.68551 110.648 3.55825 111.508 3.43719C111.689 3.41231 111.857 3.36942 112.02 3.29678C112.504 3.08237 112.98 3.13356 113.449 3.3459C113.715 3.46626 113.736 3.59766 113.525 3.79618C112.803 4.47608 111.923 4.81223 110.995 5.06537C110.263 5.26457 109.525 5.43196 108.782 5.5772C108.498 5.63254 108.389 5.86011 108.31 6.10633C108.122 6.69771 108.125 6.69909 107.49 6.71291Z" fill="inherit"></path>
<path d="M47.5528 26.279C47.3097 26.2452 47.0417 26.1864 46.8167 25.9886C46.1223 25.3764 45.3465 24.877 44.6071 24.3279C44.3713 24.1529 44.27 24.0325 44.3888 23.7199C44.6951 22.912 45.2042 22.6852 45.9483 23.0836C46.4695 23.363 46.9859 23.6514 47.5017 23.9412C47.6932 24.0491 47.8967 24.0311 48.0888 23.989C48.3373 23.9343 48.6019 23.8769 48.8154 23.7448C49.4085 23.3761 50.0714 23.1645 50.6738 22.818C51.2406 22.4922 51.8297 22.5607 52.3979 22.8477C52.508 22.903 52.6121 22.9605 52.735 22.9867C53.054 23.0566 53.1494 23.3215 53.1809 23.603C53.2112 23.8734 53.0547 24.0519 52.8223 24.1667C51.7074 24.72 50.5509 25.1669 49.391 25.6074C49.1217 25.7098 48.8645 25.8551 48.616 26.0058C48.2956 26.2002 47.9484 26.2569 47.5528 26.279Z" fill="inherit"></path>
<path d="M77.95 17.9673C77.4228 17.9798 76.9775 17.847 76.6517 17.4258C76.2951 16.9651 76.3072 16.3654 76.6672 15.9062C77.1125 15.3376 77.7256 15.0084 78.3214 14.6501C78.5128 14.5346 78.6928 14.3963 78.8701 14.2572C79.2697 13.9432 79.445 13.5531 79.2939 13.0254C79.2099 12.7349 79.3489 12.5391 79.6136 12.5647C80.1441 12.6159 80.6781 12.6374 81.1999 12.7785C81.5955 12.885 81.8319 13.1291 81.8823 13.5289C81.9542 14.1051 82.1879 14.7048 81.7399 15.2387C81.6593 15.3348 81.5814 15.4414 81.4592 15.4773C80.7755 15.6807 80.2959 16.142 79.8667 16.7092C79.4893 17.2079 79.0017 17.5973 78.4483 17.8919C78.2757 17.9839 78.1004 17.95 77.95 17.9673Z" fill="inherit"></path>
<path d="M38.9208 23.5469C39.5144 23.6949 40.1081 23.8422 40.7012 23.9923C40.7657 24.0082 40.8248 24.0463 40.8886 24.0643C41.4406 24.2213 41.5434 24.4834 41.2392 24.9814C41.0672 25.2629 40.8973 25.5479 40.7039 25.8142C40.3405 26.3122 39.8059 26.2886 39.4822 25.7747C39.2445 25.3957 38.9724 25.0582 38.5675 24.8451C38.2827 24.6957 38.1323 24.0843 38.2834 23.8C38.4271 23.5282 38.6837 23.605 38.9208 23.5469Z" fill="inherit"></path>
<path d="M34.8712 25.7179C34.3594 25.7449 34.0585 25.4053 33.6986 25.2165C33.3453 25.0311 33.0981 24.7088 32.8859 24.3706C32.7012 24.0759 32.7684 23.8505 33.0847 23.7585C33.7557 23.5627 34.4299 23.6264 35.0868 23.8352C35.3568 23.921 35.6073 24.6445 35.5475 25.0491C35.4683 25.5893 35.3259 25.7186 34.8712 25.7179Z" fill="inherit"></path>
</svg>
</div>
<div class="absolute top-0 left-0 w-full h-screen overflow-hidden z-10 lg:[clip-path:inset(70px_60px_0);]" id="kv-bg">
<img alt="kv-img-pc" class="w-full h-full object-cover scale-[1.4] z-10 lg:absolute lg:top-0 lg:left-0 lg:h-auto lg:scale-100" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb1q1200004354sl654B.webp" style="opacity: 1; visibility: inherit;"/>
<div class="mask absolute top-0 left-0 w-full h-full z-20 bg-white01" id="mask-hero-bg" style="mask-position: 60% -40%; mask-size: 100% 800%;"></div>
</div>
<div class="kv-contents relative grid grid-cols-1 z-20 lg:grid-cols-[22%_32%_22%] lg:justify-center lg:gap-x-more-60 xl:gap-x-more-96 lg:items-center lg:h-full lg:mx-[60px]" id="kv-contents" style="grid-template-columns: 22% 32% 22%;">
<div class="relative w-sp-con h-auto mx-auto aspect-[2/3] overflow-hidden pointer-events-none z-20 lg:col-start-2 lg:w-full lg:max-h-[calc(100vh-var(--s-more-20))] lg:translate-y-[14vh]" id="movie-kv" style="clip-path: inset(0%);">
<div class="w-full h-full">
<video autoplay="" class="w-full h-full object-cover" loop="" muted="" playsinline="" poster="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/poster-kv-video.webp" src="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/video/kv-video.mp4" style="translate: none; rotate: none; scale: none; filter: none; transform: translate(0px, 0px);"></video>
</div>
</div>
<div class="kv-contents__info relative w-full pt-[3vh] pb-[40px] mx-auto opacity-0 z-30 lg:col-start-3 lg:row-start-1 lg:flex lg:items-center lg:h-full lg:py-0" id="kv-contents-info">
<div class="relative mx-auto text-white01 lg:flex lg:w-full lg:m-0 lg:justify-between lg:items-center">
<div class="relative w-sp-con h-auto mx-auto lg:w-full lg:m-0">
<div class="kv-first-desc absolute top-0 left-0 w-full h-auto lg:top-1/2 lg:-translate-y-1/2" id="kv-first-desc">
<p class="text-more-13 leading-loose tracking-widest lg:tracking-[0.07em] desc font-abc inline-block lg:leading-21" style=""><div class="line-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 75%); transform: translate(0%, 100%);">閑院宮別邸に端を発する強羅花壇は、</div><div class="line-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 75%); transform: translate(0%, 100%);">閑院宮載仁親王が自ら創業し、命名された場所。</div><div class="line-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 75%); transform: translate(0%, 100%);">「強羅花壇」という言葉の由来とともに、</div><div class="line-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 75%); transform: translate(0%, 100%);">和の心でもてなすという精神は今なおこの場所の</div><div class="line-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 75%); transform: translate(0%, 100%);">在り方として受け継がれています。</div></p>
</div>
<div class="kv-second-desc" id="kv-second-desc">
<h2 class="js-kv-second-ttl font-abc text-more-20 text-white01 flex items-center gap-x-[0.5em] lg:text-more-24">
<span class="tracking-wider whitespace-nowrap"><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">P</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">A</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">S</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">S</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">A</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">G</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">E</div></span><span class="tracking-wider whitespace-nowrap"><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">O</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">F</div></span><span class="tracking-wider whitespace-nowrap"><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">T</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">I</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">M</div><div class="char-i" style="position: relative; display: inline-block; opacity: 0; filter: blur(10px);">E</div></span>
</h2>
<p class="whitespace-nowrap text-more-13 leading-loose tracking-[0.06em] mt-[1.6em] lg:tracking-[0.07em] desc font-abc js-kv-second-desc lg:leading-21 lg:whitespace-normal" style=""><div class="char-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); transform: translate(0%, 50%);">閑院宮家別邸として建築された建物と</div><div class="char-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); transform: translate(0%, 50%);">来賓を一心に迎え入れもてなす心、</div><div class="char-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); transform: translate(0%, 50%);">和心を引き継ぐ強羅花壇が持つ</div><div class="char-i" style="display: block; text-align: start; position: relative; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); transform: translate(0%, 50%);">空気と時間と空間と－</div></p>
<a class="js-kv-link link-underLine -white mt-[1em] lg:mt-[3em] lg:w-more-240" href="https://www.gorakadan.com/passage-of-time">
<span class="link-underLine__txt font-abc lg:text-more-15 lg:text-right" style="translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); transform: translate(0%, 50%);">Discover More</span>
<span class="link-underLine__line origin-left" style="translate: none; rotate: none; scale: none; transform: scale(0, 1);"></span>
</a>
</div>
</div>
</div>
</div>
<p class="[writing-mode:vertical-lr] js-kv-second-ttl-left absolute top-[36%] left-[8px] text-white01 text-more-12 tracking-[0.4em] opacity-0 z-20 lg:col-start-1 lg:row-start-1 lg:m-0 lg:text-more-14"><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">強</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">羅</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">花</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">壇</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">で</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">の</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">時</div><div class="char-i" style="position: relative; display: inline-block; translate: none; rotate: none; scale: none; opacity: 0; clip-path: inset(0px 0px 100%); filter: blur(0px); transform: translate(0%, 50%);">間</div></p>
</div>
<div class="logos-for-loading opacity-0 lg:absolute lg:top-0 lg:left-0 lg:w-full lg:h-full grid grid-cols-2 lg:gap-x-[20vw] lg:items-center lg:justify-center lg:justify-items-center pointer-events-none z-20" style="opacity: 1;">
<div class="hidden w-[116px] h-auto lg:block" id="gora-logo-left" style="translate: none; rotate: none; scale: none; transform: translate(0px, 0px); opacity: 0; visibility: hidden;">
<svg class="fill-white lg:fill-[#111111]" fill="none" height="28" viewbox="0 0 115 28" width="115" xmlns="http://www.w3.org/2000/svg">
<path d="M37.5547 26.8144C37.3942 26.833 37.2195 26.7549 37.0698 26.5937C36.9462 26.4602 36.8125 26.3371 36.6789 26.2154C35.9448 25.5521 35.7292 24.7518 36.0321 23.8056C36.1718 23.3706 36.0482 23.181 35.5956 23.1658C35.2497 23.1541 34.9024 23.1534 34.5566 23.1651C34.304 23.1741 34.0696 23.1188 33.838 23.0282C33.439 22.8726 33.261 22.5495 33.3389 22.1069C33.488 21.2631 33.8023 20.5423 34.5854 20.0983C34.8803 19.9309 35.1201 19.648 35.3565 19.3893C35.5861 19.1389 35.5318 19.0048 35.208 18.9252C34.6875 18.7986 34.165 18.679 33.6465 18.5427C33.3692 18.4701 33.0259 18.4252 32.9796 18.0641C32.9219 17.6131 32.8493 17.1082 33.2583 16.779C34.1308 16.0769 34.8514 15.1985 35.7427 14.5249C36.1812 14.1929 36.5996 13.8318 37.0792 13.5524C37.6836 13.1996 38.8576 13.3988 39.3452 14.0642C39.7851 14.666 40.3076 15.2061 40.7167 15.8411C40.8234 16.0078 41.0007 16.1302 41.1559 16.2595C41.3439 16.4159 41.5018 16.3066 41.5474 16.1261C41.6676 15.6509 42.0014 15.3915 42.3614 15.1266C42.708 14.8714 42.9424 14.4917 43.2332 14.1742C43.5972 13.7772 44.0096 13.4542 44.477 13.1948C44.745 13.0461 45.0062 12.9645 45.2574 13.1713C45.5247 13.3898 45.7316 13.67 45.7202 14.0455C45.7135 14.2613 45.6423 14.4751 45.6295 14.6915C45.6121 14.9793 45.4891 15.1971 45.2769 15.3673C44.933 15.6426 44.8229 16.0341 44.7551 16.4484C44.7316 16.5909 44.7618 16.7444 44.8498 16.8371C44.9626 16.956 45.0694 16.8101 45.1715 16.7548C45.4871 16.5846 45.8269 16.595 46.1534 16.6649C46.4643 16.7313 46.6101 16.6413 46.6786 16.3239C46.8505 15.5202 47.2642 14.8693 47.9043 14.3727C48.2408 14.1126 48.3737 14.1479 48.553 14.5421C48.5866 14.6148 48.6074 14.7068 48.6632 14.7538C49.0568 15.0858 49.177 15.5423 49.2388 16.0313C49.2703 16.2831 49.3791 16.3495 49.6511 16.3135C50.1535 16.2471 50.6209 16.0507 51.1159 15.9677C51.5142 15.9013 51.7123 16.0444 51.7506 16.4615C51.7842 16.8253 51.6982 17.1732 51.5646 17.5032C51.4625 17.757 51.3335 18.0019 51.1938 18.2363C51.0239 18.5213 50.7499 18.6513 50.4336 18.6617C50.1663 18.6707 50.1314 18.8007 50.2173 19.0207C50.271 19.159 50.3221 19.2987 50.3792 19.4357C50.5041 19.7379 50.4107 19.9164 50.087 19.9046C49.7573 19.8929 49.4725 19.9869 49.1992 20.1564C48.9218 20.3279 48.8909 20.5036 49.0809 20.7796C49.2314 20.9975 49.4759 21.1641 49.4349 21.4892C49.414 21.6566 49.3704 21.7963 49.2374 21.907C48.7378 22.3248 48.2421 22.746 47.8183 23.2509C47.5738 23.5421 47.1292 23.504 46.9815 23.1382C46.7504 22.5675 46.3394 22.1463 45.9707 21.6926C45.7215 21.3862 45.7551 21.1164 45.9156 20.8114C45.9579 20.7305 46.0056 20.6523 46.0486 20.5721C46.3199 20.0637 46.2743 19.9344 45.7544 19.7338C45.4105 19.601 45.3629 19.4122 45.6 19.1279C45.6772 19.0352 45.8055 18.9619 45.735 18.8076C45.6711 18.6693 45.5422 18.6624 45.4173 18.6679C45.2212 18.6762 45.0419 18.6347 44.8746 18.5268C44.7013 18.4141 44.6006 18.4936 44.5569 18.6755C44.4602 19.0767 44.3494 19.4758 44.2809 19.8818C44.2191 20.247 44.3991 20.5818 44.4992 20.9172C44.6476 21.4152 44.6483 21.8855 44.4562 22.3635C44.2816 22.7972 44.2628 23.291 43.9928 23.6866C43.7107 24.0996 43.4542 24.5215 43.2789 24.9994C43.1969 25.2235 42.9377 25.2194 42.7543 25.016C42.5025 24.7373 42.3883 24.3852 42.3084 24.0249C42.217 23.6106 42.1378 23.1949 42.1385 22.764C42.1391 22.34 41.9101 22.2058 41.5824 22.4465C41.0981 22.8027 40.6004 22.9749 39.9873 22.8843C39.4076 22.7986 38.8388 22.9957 38.2793 23.1575C38.0067 23.2364 37.957 23.4861 37.9691 23.7081C37.9993 24.2337 38.0449 24.7656 38.3915 25.1993C38.7004 25.5846 38.5675 25.9788 38.4097 26.3648C38.2625 26.7237 37.955 26.8109 37.5547 26.8144ZM37.4579 21.8219C38.0658 21.9001 38.5661 21.6428 39.0571 21.3696C39.2532 21.261 39.3029 21.1213 39.1451 20.8951C38.8845 20.5223 38.5298 20.2615 38.2028 19.9689C38.0476 19.8306 37.8502 19.8271 37.6393 19.9309C37.0933 20.2 36.648 20.5845 36.2699 21.0652C36.0301 21.3696 36.0765 21.5757 36.4546 21.6822C36.787 21.7769 37.1268 21.8523 37.4579 21.8219ZM42.2661 17.5571C42.1109 18.1464 41.6636 18.4335 41.2371 18.7468C40.9947 18.9245 40.7502 18.9902 40.4527 18.9377C40.225 18.8969 39.9826 18.8934 39.7596 18.9902C39.5393 19.085 39.5218 19.251 39.7112 19.4025C39.7717 19.4509 39.8482 19.4792 39.9107 19.5263C40.5722 20.0236 41.4286 20.1965 41.9927 20.8584C42.1096 20.9961 42.1922 20.9103 42.2244 20.774C42.3097 20.4061 42.389 20.0395 42.3379 19.6529C42.2842 19.2455 42.2916 18.8346 42.3352 18.4245C42.3661 18.136 42.3991 17.8435 42.2661 17.5571ZM38.3378 17.2272C38.5084 17.2493 38.5957 17.2016 38.6635 17.1193C38.7488 17.0155 38.6776 16.9208 38.6245 16.8385C38.4258 16.5307 38.1887 16.2595 37.8717 16.0749C37.7327 15.9946 37.5842 15.9372 37.4257 16.0057C37.098 16.1475 36.7595 16.2755 36.4989 16.5376C36.3619 16.6752 36.1181 16.8004 36.2128 17.0314C36.3061 17.2597 36.5909 17.2542 36.7769 17.2251C37.3216 17.1407 37.8582 17.0909 38.3378 17.2272Z" fill="inherit"></path>
<path d="M33.9064 10.078C33.9057 9.03289 33.9017 7.9871 33.9084 6.94198C33.9104 6.5422 33.8674 6.1452 33.8762 5.74196C33.8896 5.14158 34.2342 4.88844 34.6559 4.65258C34.8319 4.55366 35.0173 4.47413 35.1979 4.38421C35.654 4.15667 36.0865 4.39806 36.5271 4.45754C36.6674 4.4762 36.797 4.56958 36.9374 4.60139C37.2088 4.66226 37.1899 4.82482 37.0798 5.00879C36.9515 5.2239 36.8286 5.45008 36.6614 5.63059C36.2161 6.10991 36.2282 6.67708 36.2907 7.27538C36.3639 7.98157 36.4841 8.6829 36.4854 9.39601C36.4861 10.0102 36.9831 10.4563 37.5728 10.3941C38.065 10.3422 38.5023 10.0607 38.9972 10.0324C39.5896 9.99845 40.1585 9.75084 40.7461 9.76882C41.3566 9.7875 41.9436 9.73009 42.5306 9.57655C43.0787 9.43268 43.6442 9.37942 44.1983 9.25493C44.9491 9.08615 45.7248 9.12074 46.4938 9.16154C46.8061 9.17813 47.1198 9.1726 47.4327 9.16914C47.7477 9.16501 47.9634 9.01698 48.0493 8.69329C48.2111 8.08392 48.3435 7.46696 48.4415 6.84377C48.4999 6.47096 48.3797 6.34094 48.0043 6.35962C47.371 6.39073 46.7383 6.35892 46.1043 6.4855C45.6718 6.57196 45.4145 6.77599 45.2836 7.17854C45.1358 7.63297 44.8256 7.93245 44.4367 8.16139C44.125 8.34469 43.8745 8.30665 43.6193 8.05004C43.4185 7.84739 43.2305 7.63297 43.1203 7.36461C43.0169 7.11353 42.861 6.98764 42.5837 6.9911C42.2741 6.99526 42.0048 7.06164 41.8328 7.34523C41.769 7.45037 41.722 7.57486 41.7012 7.69659C41.6125 8.20635 41.2794 8.54804 40.9396 8.87866C40.6629 9.14772 40.3768 9.13457 40.1209 8.83093C39.9033 8.57225 39.6474 8.33846 39.5043 8.01961C39.4318 7.85984 39.3142 7.81832 39.1538 7.81695C38.7763 7.81279 38.4237 7.93868 38.0805 8.0611C37.4311 8.2935 37.0657 7.90064 36.7325 7.4559C36.4256 7.04575 36.5277 6.59754 36.7608 6.19569C36.9341 5.89689 37.1906 5.67209 37.5546 5.62782C37.8965 5.58633 38.237 5.52062 38.5802 5.50057C39.0624 5.47289 39.2632 5.33941 39.3854 4.8497C39.4647 4.52808 39.5325 4.19954 39.5594 3.86962C39.5923 3.46361 39.7414 3.11431 39.9563 2.78647C40.2565 2.32929 40.7038 2.24283 41.1444 2.56306C41.7757 3.02095 42.1451 3.59363 41.9363 4.43886C41.859 4.75288 41.896 5.07934 41.9685 5.3975C41.9933 5.5061 42.031 5.59395 42.1471 5.62023C42.4682 5.69216 43.0679 5.22321 43.0861 4.88153C43.1156 4.30537 43.0793 3.7306 43.0048 3.16134C42.9154 2.48766 43.0767 1.85411 43.3016 1.24614C43.4581 0.824211 43.8591 0.636773 44.2816 0.546853C44.395 0.522663 44.5059 0.555845 44.5972 0.631939C44.9505 0.928653 45.3266 1.20325 45.6483 1.53318C45.9955 1.88801 46.1647 2.32374 46.0103 2.85148C45.8981 3.23537 45.886 3.64414 45.7517 4.02662C45.7087 4.14905 45.7289 4.30191 45.7437 4.43746C45.7886 4.86146 45.923 4.98735 46.3346 4.9327C46.7087 4.8836 47.0916 4.86631 47.4489 4.72451C47.7585 4.60139 48.0862 4.63252 48.4052 4.59862C48.8566 4.55022 49.2555 4.32682 49.6867 4.21269C50.3395 4.04047 51.04 4.32127 51.2348 4.92993C51.3073 5.1575 51.4134 5.37398 51.4906 5.60085C51.6424 6.04766 51.5303 6.43085 51.224 6.77462C50.8627 7.17924 50.6525 7.64473 50.7042 8.21466C50.7794 9.03912 50.4859 9.7453 50.0225 10.3934C49.9366 10.5137 49.8506 10.6355 49.7787 10.7648C49.3845 11.4793 48.733 11.7546 47.9996 11.4344C47.4986 11.2158 46.9949 11.139 46.4723 11.2103C45.9767 11.2787 45.4844 11.1715 44.98 11.2822C44.4017 11.4095 43.8396 11.5927 43.2647 11.7221C42.2136 11.9586 41.1686 12.2194 40.0705 12.1807C39.2935 12.1537 38.5123 12.2574 37.7326 12.2948C37.4512 12.3086 37.2403 12.5196 36.981 12.5694C36.2033 12.7181 35.4847 12.5071 34.8017 12.1309C34.7829 12.1205 34.7694 12.0963 34.75 12.088C33.9648 11.7532 33.7996 11.1058 33.9057 10.3194C33.9158 10.2405 33.9071 10.1589 33.9064 10.078Z" fill="inherit"></path>
<path d="M6.72619 10.4578C6.77317 9.66452 6.88331 8.84211 6.85109 8.01213C6.84502 7.86273 6.83228 7.71402 6.81817 7.56599C6.7987 7.37302 6.74697 7.19181 6.5334 7.14962C6.30844 7.10466 6.11299 7.13303 5.92561 7.3253C5.45412 7.80808 5.06326 8.40569 4.49035 8.7439C3.91343 9.0842 3.29756 9.4577 2.54736 9.15061C2.11551 8.97353 1.64742 8.89539 1.22229 8.67958C0.755511 8.44303 0.424376 8.05916 0.10808 7.66077C-0.0128472 7.5086 -0.0920852 7.27757 0.207445 7.21119C0.775638 7.0853 1.13899 6.63779 1.57085 6.30785C1.98589 5.99109 2.3929 5.67637 2.88518 5.50069C3.16593 5.4004 3.39697 5.20811 3.61455 5.0186C4.1868 4.5206 4.8228 4.11666 5.42862 3.66917C5.6301 3.52045 5.86113 3.44437 6.11434 3.41879C6.82018 3.34546 7.43605 3.46166 8.05528 3.90847C8.57781 4.28614 9.12788 4.64026 9.66988 4.99716C10.457 5.5173 10.6968 6.20896 10.5121 7.12127C10.3126 8.10758 10.1561 9.10288 9.95932 10.0906C9.70882 11.3494 9.35354 12.5826 9.14198 13.8498C9.06139 14.3333 8.93645 14.8229 8.60134 15.2054C8.10029 15.7774 7.48508 16.0562 6.7275 15.9365C6.61735 15.9185 6.50655 15.8978 6.40042 15.8653C6.30508 15.8355 6.22715 15.8501 6.15058 15.9116C6.0055 16.0271 5.98337 16.5431 6.10155 16.8232C6.20702 17.075 6.4078 17.0695 6.60862 17.0245C6.89073 16.9616 7.17345 16.8917 7.44478 16.7921C8.30849 16.4753 8.82831 17.1179 8.88812 17.8538C8.92841 18.358 8.79338 18.8311 8.66647 19.3063C8.48045 20.0028 8.23663 20.6903 8.10431 21.3924C7.92767 22.3323 7.90353 23.3014 7.80141 24.2566C7.74029 24.8306 7.67581 25.4213 7.13316 25.7602C6.69057 26.0362 6.1714 26.0175 5.67912 26.057C5.46355 26.0742 5.30232 25.8474 5.14721 25.6786C4.67035 25.1606 4.56492 24.4454 4.22238 23.8554C4.09277 23.632 3.9618 23.4003 3.91412 23.1333C3.85768 22.8179 3.93626 22.6982 4.24386 22.6872C4.34327 22.683 4.44469 22.6982 4.54274 22.7169C5.00751 22.8034 5.08609 22.7674 5.14049 22.2971C5.20025 21.7832 5.33389 21.2852 5.40911 20.7761C5.45211 20.4808 5.40374 20.1833 5.40509 19.888C5.4071 19.4972 4.9773 19.2925 4.64616 19.4889C4.52191 19.5629 4.41312 19.6563 4.27276 19.7102C4.04841 19.7974 3.86977 19.7614 3.68104 19.6183C3.05845 19.1452 2.98528 18.9902 3.19147 18.2239C3.32175 17.7383 3.48694 17.2611 3.66829 16.7928C3.95708 16.0465 4.13171 15.2884 4.08875 14.4764C4.06186 13.9819 4.31105 13.7612 4.79595 13.8152C5.07199 13.8456 5.34332 13.9231 5.61735 13.9763C5.99075 14.0497 6.06462 13.9985 6.14926 13.6257C6.34672 12.7556 6.5764 11.8924 6.70266 11.0057C6.7275 10.8327 6.73021 10.6633 6.72619 10.4578Z" fill="inherit"></path>
<path d="M100.893 9.33389C100.891 8.92372 100.996 8.75357 101.601 8.61802C101.826 8.5682 102.062 8.55644 102.298 8.60349C102.889 8.72107 103.456 8.51357 104.009 8.37869C105.15 8.09994 106.303 7.91666 107.457 7.72023C108.059 7.61716 108.674 7.61163 109.28 7.54592C110.05 7.46223 110.837 7.43802 111.58 7.1835C111.862 7.08666 112.14 6.94072 112.38 6.7602C112.641 6.56308 112.897 6.57899 113.196 6.59421C113.946 6.63362 114.365 7.14477 114.747 7.69533C115.028 8.10134 114.885 8.60003 114.787 9.00189C114.361 10.7456 113.724 12.4145 112.784 13.9362C112.547 14.3201 112.322 14.7904 111.808 14.9232C111.662 14.9613 111.514 14.9896 111.382 14.9211C111.051 14.7482 110.713 14.7386 110.347 14.7558C109.749 14.7835 109.148 14.7413 108.551 14.7399C107.886 14.7386 107.22 14.8956 106.578 15.0802C105.741 15.3216 104.896 15.5305 104.051 15.738C103.42 15.8929 102.991 15.4897 102.631 15.0277C101.969 14.1769 101.632 13.1927 101.567 12.1102C101.528 11.4697 101.359 10.8722 101.086 10.3001C100.956 10.0283 100.888 9.73921 100.893 9.33389ZM106.139 13.4043C106.534 13.4977 106.823 13.3103 107.136 13.304C108.095 13.2847 109.055 13.3386 110.016 13.2722C110.234 13.257 110.361 13.1726 110.498 13.0301C110.719 12.7991 110.741 12.4941 110.802 12.2064C110.964 11.4442 111.304 10.7345 111.404 9.95362C111.47 9.44386 111.249 9.21906 110.772 9.3733C110.541 9.44733 110.311 9.48467 110.079 9.45146C108.983 9.29377 107.886 9.26333 106.784 9.35878C106.544 9.37953 106.297 9.43694 106.052 9.37676C105.54 9.25087 105.058 9.36363 104.583 9.56352C104.32 9.6735 104.199 9.85264 104.205 10.1445C104.223 11.0215 104.047 11.8792 103.926 12.7396C103.804 13.6049 104.277 13.7149 104.894 13.6305C104.393 13.2279 104.167 12.6968 104.4 12.2513C104.785 11.5154 105.126 10.7511 105.62 10.0802C105.8 9.83603 106.02 9.70738 106.336 9.7219C106.726 9.73988 107.117 9.70461 107.508 9.70324C107.709 9.70185 107.901 9.7219 108.048 9.9128C108.134 10.0249 108.272 10.0989 108.396 10.1749C108.749 10.3914 109.129 10.5692 109.457 10.8189C109.839 11.1094 110 11.5216 109.917 12.0286C109.842 12.4934 109.499 12.5474 109.148 12.6587C108.535 12.8531 107.901 12.8925 107.279 13.0135C106.905 13.0855 106.515 13.1456 106.139 13.4043ZM107.571 11.6267C107.564 11.4262 107.094 10.8431 106.907 10.7995C106.811 10.7767 106.725 10.7988 106.662 10.8722C106.373 11.2111 106.153 11.5963 106.017 12.0231C105.956 12.2154 106.05 12.3689 106.266 12.2617C106.63 12.0826 107.023 11.9968 107.395 11.8481C107.492 11.8086 107.576 11.7547 107.571 11.6267Z" fill="inherit"></path>
<path d="M104.177 22.2254C104.167 21.7302 104.241 21.2425 104.293 20.7521C104.341 20.2977 104.131 19.8758 104.097 19.4304C104.04 18.6778 103.69 18.0277 103.423 17.3491C103.207 16.7993 103.454 16.4009 104.024 16.3276C104.528 16.2632 105.02 16.3283 105.517 16.3801C105.811 16.4106 106.036 16.5945 106.258 16.7599C106.783 17.1486 107.323 17.0573 107.877 16.874C108.332 16.7239 108.697 16.4196 109.085 16.1498C109.701 15.7224 110.429 15.5557 111.071 15.9153C111.521 16.1671 111.947 16.5703 112.268 17.0303C112.338 17.1306 112.314 17.2406 112.288 17.3595C112.17 17.9122 111.97 18.4358 111.787 18.9669C111.55 19.6531 111.47 20.3745 111.384 21.0924C111.345 21.4224 111.342 21.7571 111.309 22.0877C111.217 23.0104 110.178 23.7007 109.333 23.6219C108.818 23.5734 108.575 23.1757 108.707 22.6611C108.733 22.5615 108.756 22.4557 108.806 22.3685C109.253 21.596 109.159 20.8075 108.792 20.0625C108.519 19.5092 108.588 18.9794 108.687 18.4254C108.732 18.1798 108.844 17.8886 108.65 17.7067C108.456 17.5262 108.235 17.7586 108.034 17.834C107.48 18.0429 106.946 18.3036 106.356 18.4102C106.07 18.4613 105.887 18.7567 105.969 19.106C106.139 19.8426 106.186 20.6055 106.422 21.3269C106.454 21.4231 106.459 21.5635 106.562 21.5808C106.699 21.6036 106.736 21.4466 106.77 21.3518C106.905 20.9811 106.925 20.6062 106.697 20.2624C106.454 19.8966 106.434 19.4809 106.46 19.0693C106.488 18.6246 106.898 18.5679 107.365 18.7823C108.182 19.1578 108.246 19.956 108.498 20.6608C108.695 21.2135 108.656 21.7537 108.487 22.3125C108.339 22.7995 108.348 23.3286 108.103 23.7975C107.987 24.0196 108.13 24.1434 108.355 24.1309C108.666 24.1129 108.977 24.0687 109.27 23.9504C110.315 23.5278 111.344 23.6053 112.366 24.0403C112.562 24.124 112.757 24.2167 112.961 24.2706C113.222 24.3405 113.215 24.4823 113.108 24.6787C112.977 24.9194 112.845 25.1594 112.721 25.4036C112.661 25.5226 112.583 25.618 112.462 25.6602C111.566 25.97 110.683 26.1976 109.728 25.8255C109.02 25.5495 108.264 25.618 107.512 25.7148C106.873 25.7965 106.422 26.0842 106.09 26.6506C105.875 27.0165 105.487 27.0504 105.103 27.0103C104.345 26.9315 103.711 26.5746 103.123 26.0973C102.916 25.9299 102.89 25.7598 102.979 25.5288C103.185 24.9969 103.6 24.8032 104.114 24.7735C104.526 24.75 104.922 24.6151 105.341 24.6165C105.586 24.6172 105.806 24.4643 105.983 24.2775C106.282 23.9615 106.495 23.5824 106.696 23.1979C106.756 23.0844 106.808 22.9184 106.646 22.8327C106.507 22.7587 106.423 22.8832 106.339 22.9765C106.332 22.9848 106.322 22.9931 106.319 23.0035C106.071 23.8017 105.48 23.635 104.923 23.5167C104.391 23.4033 104.181 23.1086 104.177 22.5359V22.2254Z" fill="inherit"></path>
<path d="M78.6093 26.6563C77.4151 26.8333 76.2868 26.206 75.2384 25.4493C74.6138 24.9977 74.1242 24.3669 73.8327 23.604C73.3787 22.415 73.1591 21.1721 73.0705 19.9105C73.0214 19.216 73.0846 18.5099 73.1329 17.8113C73.1625 17.3873 73.1631 16.964 73.1739 16.5407C73.196 15.6443 73.3498 14.7624 73.4587 13.8764C73.5036 13.5077 73.8273 13.2581 74.0134 12.9475C74.5352 12.0781 75.3755 11.5801 76.1391 10.9915C76.3446 10.8331 76.5541 10.6809 76.7657 10.5322C77.1418 10.268 77.5844 10.3434 77.8618 10.7093C78.2976 11.284 78.5354 11.9508 78.6677 12.6535C78.7892 13.2968 78.2097 13.8356 77.5347 13.7215C77.3567 13.6917 77.1814 13.6737 77.0034 13.6737C76.4171 13.6737 76.1202 13.9054 75.9739 14.4913C75.8328 15.0564 75.8711 15.6332 75.8973 16.2066C75.9443 17.2151 76.1169 18.2124 76.1337 19.2243C76.1451 19.9119 76.1968 20.5987 76.2277 21.2862C76.2338 21.419 76.2875 21.5338 76.3486 21.641C76.6864 22.2421 76.9517 22.8867 77.6012 23.254C77.9833 23.4705 78.3789 23.4421 78.7678 23.4788C79.0619 23.5064 79.5247 22.886 79.6134 22.5077C79.7853 21.7731 79.8296 21.0206 79.9391 20.2771C79.9807 19.9914 80.1406 19.7708 80.3326 19.5723C80.5294 19.3682 80.7316 19.3952 80.8605 19.6241C81.0593 19.9782 81.3172 20.3103 81.3716 20.7363C81.4529 21.372 81.7531 21.9606 81.741 22.6163C81.7269 23.384 81.7276 24.1524 81.7068 24.9202C81.7007 25.1464 81.561 25.3345 81.4173 25.4922C80.6234 26.3672 80.0915 26.7344 78.6093 26.6563Z" fill="inherit"></path>
<path d="M18.0182 3.87276C18.0061 4.54161 17.7455 5.10462 17.3734 5.54384C16.6319 6.41811 16.2148 7.46943 15.6702 8.45437C14.9186 9.81418 14.5157 11.313 13.9649 12.7531C13.8192 13.1335 13.7017 13.536 13.701 13.9565C13.7003 14.339 13.799 14.4753 14.1637 14.4815C14.877 14.494 15.5869 14.4227 16.2934 14.3266C16.5634 14.2899 16.6668 14.0562 16.7542 13.8279C16.9456 13.3265 17.1309 12.8229 17.3176 12.3201C17.4439 11.9812 17.6844 11.7169 17.8818 11.4258C18.0336 11.2003 18.2512 11.2127 18.4009 11.4389C18.4634 11.5323 18.5111 11.6395 18.5501 11.7467C18.8637 12.6092 19.2902 13.4171 19.7019 14.2311C19.8389 14.5023 19.8617 14.7755 19.722 15.021C19.5031 15.4056 19.2425 15.7666 18.9879 16.1284C18.8556 16.3179 18.6494 16.3421 18.4346 16.3926C17.8999 16.5185 17.4137 16.3974 16.9046 16.2549C16.5191 16.147 16.1014 16.284 15.7064 16.2639C14.7762 16.2176 13.9246 16.4521 13.075 16.8048C12.4988 17.0441 11.9642 16.9086 11.5222 16.4189C11.3423 16.2197 11.1153 16.062 10.8983 15.9015C10.5256 15.6249 10.3798 15.2472 10.5276 14.7976C10.631 14.4829 10.7479 14.1661 10.9077 13.8791C11.3241 13.1328 11.3705 12.2779 11.6075 11.4804C11.9239 10.4152 12.1805 9.33417 12.3705 8.23995C12.6016 6.90918 13.1207 5.71815 13.7957 4.56166C14.472 3.40313 15.2786 2.35941 16.1134 1.33021C16.3545 1.0321 16.6689 1.00375 16.9986 1.11373C17.3183 1.22093 17.5762 1.38625 17.63 1.78741C17.7052 2.34282 17.7636 2.90307 17.9631 3.43494C18.0175 3.57951 18.0047 3.75103 18.0182 3.87276Z" fill="inherit"></path>
<path d="M71.9513 6.31868C72.408 6.22878 72.8761 6.20941 73.3382 6.14855C73.8815 6.07662 74.0346 5.88502 74.0917 5.31648C74.1347 4.88972 74.1958 4.46019 74.0951 4.02997C74.0682 3.91584 74.04 3.80656 73.9735 3.71318C73.7479 3.39709 73.8069 3.08446 73.989 2.78013C74.1347 2.5353 74.2972 2.30151 74.4477 2.06011C74.5948 1.82495 74.7942 1.71428 75.0696 1.74818C76.1455 1.88028 77.0999 2.31949 77.8487 3.11076C78.3854 3.67724 78.3941 4.12958 77.707 4.47955C77.3799 4.64556 77.112 4.91253 76.748 5.02873C76.5163 5.10274 76.4269 5.33723 76.4988 5.59797C76.5753 5.87256 76.7863 5.79233 76.9696 5.78127C77.9273 5.72247 78.8152 5.41469 79.6715 4.98654C80.0275 4.80949 80.4929 5.0246 80.6931 5.4458C81.2048 6.5179 80.8267 7.36034 79.7152 7.70963C79.3169 7.83482 78.9482 7.87355 78.5439 7.75735C78.1805 7.65222 77.8004 7.60795 77.4263 7.54571C77.1301 7.49659 76.9602 7.6702 76.8057 7.90952C76.4793 8.41375 76.21 8.95947 75.7687 9.38207C75.306 9.82543 75.2241 9.80953 74.7613 9.37378C74.4121 9.04523 74.2334 8.70077 74.2758 8.2028C74.31 7.80092 74.2079 7.73245 73.8123 7.7449C73.4235 7.75735 73.0333 7.71862 72.6451 7.80785C72.3469 7.87702 72.144 8.00221 72.0883 8.34804C72.0198 8.77203 71.9667 9.20293 71.7518 9.59233C71.4476 10.1443 70.9284 10.248 70.4529 9.82336C70.1379 9.54116 69.8726 9.21055 69.7067 8.8163C69.5979 8.55829 69.4522 8.52648 69.2332 8.63369C68.7295 8.87992 68.2198 9.1151 67.7234 9.37654C67.1143 9.69747 66.5354 9.63385 65.9799 9.26104C65.753 9.10818 65.5145 8.99681 65.26 8.90413C65.0028 8.81077 64.7408 8.67449 64.7354 8.34804C64.7301 8.0437 64.9584 7.87632 65.2096 7.7864C65.6931 7.61348 66.0565 7.23791 66.5025 7.00137C66.753 6.86856 66.9954 6.80769 67.2674 6.86787C67.5079 6.92112 67.7503 6.94603 67.9975 6.9405C68.5469 6.92944 68.8639 6.55593 68.7087 6.00951C68.5187 5.3386 68.2863 4.67737 67.7772 4.16968C67.6026 3.9947 67.6032 3.76023 67.6973 3.55825C67.9699 2.97242 68.2816 2.45574 69.0116 2.3437C69.6678 2.24339 70.2937 2.2282 70.9143 2.47719C71.3804 2.66393 71.4637 2.85346 71.32 3.36182C71.1964 3.79896 71.0164 4.23125 71.1185 4.70228C71.2078 5.11589 71.3092 5.52604 71.408 5.93758C71.4771 6.22809 71.6464 6.37196 71.9513 6.31868Z" fill="inherit"></path>
<path d="M16.1351 22.8678C16.1251 23.1023 16.1069 23.2628 16.1143 23.4232C16.1231 23.6314 16.2164 23.7345 16.4145 23.5961C16.706 23.3935 16.9713 23.1625 17.0284 22.7758C17.062 22.551 17.1875 22.4431 17.3904 22.3816C17.974 22.2045 18.5698 22.199 19.1648 22.2502C19.4697 22.2764 19.7444 22.4134 19.917 22.7053C20.4126 23.5436 20.6914 24.4282 20.4993 25.4235C20.4214 25.8275 20.3086 25.8883 19.9251 25.8088C19.1057 25.64 18.2823 25.4934 17.4804 25.2458C17.4038 25.2223 17.3266 25.234 17.254 25.2776C16.6852 25.6151 16.0841 25.8994 15.5844 26.3531C15.1814 26.719 14.8685 26.6706 14.4883 26.2653C13.9651 25.7071 13.7724 25.0549 13.804 24.2913C13.8234 23.8119 13.7307 23.3319 13.7368 22.8478C13.7415 22.5338 13.5434 22.3131 13.2767 22.1872C13.0148 22.0641 12.7932 22.1935 12.6172 22.3878C12.3264 22.7087 11.9866 22.8242 11.5627 22.7502C11.0873 22.6672 10.8401 22.3456 10.7427 21.9106C10.5896 21.2251 10.7494 20.5411 10.826 19.8591C10.8361 19.7678 10.8683 19.6772 10.8656 19.5873C10.8394 18.8423 11.2626 18.3519 11.7629 17.9093C11.9254 17.7654 12.1343 17.7433 12.3345 17.8076C12.636 17.9045 12.9456 17.9266 13.2552 17.9681C13.5481 18.0068 13.7025 17.8858 13.7684 17.6139C13.8147 17.4251 13.8335 17.2301 13.8792 17.0412C13.9866 16.5979 14.2089 16.472 14.624 16.6242C15.207 16.8372 15.757 17.1063 16.1378 17.6402C16.2715 17.8277 16.4609 17.8291 16.6536 17.7931C17.1513 17.6997 17.6543 17.7301 18.1554 17.7211C18.4945 17.7149 18.7706 17.8408 19.0177 18.1016C19.2548 18.3526 19.4301 18.6418 19.6262 18.9191C19.7975 19.1605 19.8263 19.4579 19.7283 19.7118C19.4119 20.53 18.9431 21.2535 18.2897 21.8296C18.0257 22.0627 17.6967 22.0738 17.3756 21.9541C17.1452 21.8677 16.9243 21.7529 16.6792 21.7162C16.2735 21.6553 16.0418 21.9085 16.0989 22.3221C16.1271 22.5261 16.1271 22.7343 16.1351 22.8678ZM16.2574 19.7304C16.254 20.1952 16.3098 20.2575 16.7711 20.3426C16.9565 20.3764 17.1338 20.3758 17.2446 20.189C17.3615 19.9926 17.2883 19.801 17.1668 19.6488C16.9841 19.4213 16.7121 19.3556 16.4448 19.3791C16.2459 19.3964 16.2533 19.5983 16.2574 19.7304ZM13.5427 19.9082C13.5494 19.6481 13.3956 19.5852 13.2163 19.579C12.8966 19.5679 12.5776 19.9144 12.587 20.254C12.5924 20.4588 12.6233 20.6545 12.8892 20.6566C13.2385 20.6587 13.5434 20.2962 13.5427 19.9082Z" fill="inherit"></path>
<path d="M97.4578 21.5705C96.5377 21.6044 95.8453 21.1216 95.1132 20.773C94.9137 20.6776 94.7317 20.4853 94.7908 20.2204C94.8506 19.9576 95.0481 19.7888 95.3012 19.7293C95.6256 19.6539 95.903 19.4935 96.1683 19.2984C96.302 19.2002 96.3597 19.0716 96.3107 18.9097C96.2375 18.6683 96.1609 18.4276 96.0696 18.1925C96.0004 18.014 95.8956 17.9566 95.7546 18.142C95.6377 18.2955 95.5188 18.4491 95.3845 18.5853C95.1582 18.8129 94.9842 18.781 94.7613 18.5569C93.9795 17.7726 93.3838 16.8555 92.8828 15.8636C92.7807 15.6623 92.6282 15.4887 92.4966 15.304C92.328 15.0682 92.416 14.9361 92.6517 14.8344C93.2615 14.5702 93.911 14.546 94.5544 14.4989C95.0769 14.4609 95.1179 14.4346 95.1233 13.8903C95.1253 13.669 95.0843 13.4594 95.0319 13.2457C94.8331 12.4288 95.226 11.8361 96.0306 11.7205C96.2066 11.6956 96.3832 11.6714 96.5605 11.6583C96.8762 11.6334 97.124 11.8374 97.206 12.2331C97.3477 12.9116 97.6606 13.5299 97.8399 14.1925C98.0031 14.7971 98.4391 14.9319 98.9595 14.9575C99.2705 14.9728 99.5868 14.9133 99.8884 15.0364C100.011 15.0862 100.161 15.1028 100.177 15.2805C100.191 15.4424 100.084 15.5385 99.9723 15.6139C99.6097 15.8574 99.2604 16.1251 98.8642 16.3125C98.5841 16.4446 98.4525 16.6576 98.5129 16.9751C98.5975 17.4226 98.8527 17.5388 99.1845 17.249C99.8252 16.6881 100.61 16.3471 101.251 15.7875C101.399 15.6582 101.6 15.6693 101.761 15.7529C101.929 15.8415 101.873 16.0351 101.864 16.1922C101.849 16.4363 101.704 16.6175 101.563 16.7953C101.046 17.4413 100.437 18.0313 100.032 18.7444C99.609 19.4886 99.0495 20.1754 98.8507 21.0442C98.8185 21.1853 98.7433 21.2614 98.6284 21.3222C98.2402 21.5256 97.8306 21.6072 97.4578 21.5705Z" fill="inherit"></path>
<path d="M66.7329 23.5037C66.7295 22.7132 66.9807 22.099 67.0935 21.4578C67.2265 20.7052 67.2131 19.9811 66.9934 19.2534C66.929 19.039 66.7846 18.9941 66.6059 19.0183C66.16 19.0791 65.7247 19.0778 65.2915 18.918C64.7872 18.7312 64.6139 18.5217 64.6367 18.061C64.675 17.2946 64.8147 17.0871 65.3957 16.9578C66.5307 16.7053 67.2581 15.9805 67.6657 14.8849C67.9491 14.1234 68.2353 13.3639 68.512 12.5996C68.751 11.9377 69.1695 11.7779 69.7598 12.1487C70.1104 12.3693 70.3415 12.7013 70.5617 13.0554C70.8492 13.5182 71.1997 13.9415 71.4429 14.4325C71.8398 15.2349 71.6135 15.8594 70.8398 16.2931C70.3911 16.5435 69.9459 16.816 69.5913 17.2061C69.2984 17.5284 69.2507 17.8418 69.4798 18.2076C69.97 18.9892 70.056 19.8434 69.9976 20.744C69.9237 21.8866 69.737 23.0189 69.7182 24.1677C69.7094 24.719 69.4092 25.145 69.0042 25.4798C68.6899 25.7406 68.3683 25.6956 68.0835 25.4113C67.8108 25.1395 67.5375 24.8677 67.2473 24.6166C66.8679 24.2888 66.6583 23.8994 66.7329 23.5037Z" fill="inherit"></path>
<path d="M107.49 6.71291C107.219 6.77518 106.946 6.65413 106.663 6.53309C105.79 6.15682 104.929 6.35948 104.093 6.66797C103.552 6.86786 103.011 7.06913 102.524 7.41566C102.172 7.66534 101.769 7.54708 101.464 7.22613C101.122 6.86577 100.744 6.54485 100.524 6.07313C100.368 5.74252 100.325 5.44857 100.567 5.14907C100.802 4.85856 101.026 4.55769 101.264 4.26927C101.507 3.97602 101.831 3.98845 102.125 4.12402C103.013 4.53417 103.946 4.3834 104.863 4.37371C105.297 4.36957 105.593 3.97946 105.577 3.52642C105.567 3.25322 105.521 2.98002 105.477 2.70889C105.394 2.2005 105.526 1.89547 106.017 1.81317C106.698 1.69904 107.322 1.45142 107.947 1.17062C108.288 1.01776 108.524 1.15747 108.649 1.51783C108.806 1.97363 108.815 2.41354 108.567 2.84514C108.489 2.98139 108.436 3.13839 108.4 3.29334C108.288 3.76437 108.479 3.92897 108.934 3.8439C109.788 3.68551 110.648 3.55825 111.508 3.43719C111.689 3.41231 111.857 3.36942 112.02 3.29678C112.504 3.08237 112.98 3.13356 113.449 3.3459C113.715 3.46626 113.736 3.59766 113.525 3.79618C112.803 4.47608 111.923 4.81223 110.995 5.06537C110.263 5.26457 109.525 5.43196 108.782 5.5772C108.498 5.63254 108.389 5.86011 108.31 6.10633C108.122 6.69771 108.125 6.69909 107.49 6.71291Z" fill="inherit"></path>
<path d="M47.5528 26.279C47.3097 26.2452 47.0417 26.1864 46.8167 25.9886C46.1223 25.3764 45.3465 24.877 44.6071 24.3279C44.3713 24.1529 44.27 24.0325 44.3888 23.7199C44.6951 22.912 45.2042 22.6852 45.9483 23.0836C46.4695 23.363 46.9859 23.6514 47.5017 23.9412C47.6932 24.0491 47.8967 24.0311 48.0888 23.989C48.3373 23.9343 48.6019 23.8769 48.8154 23.7448C49.4085 23.3761 50.0714 23.1645 50.6738 22.818C51.2406 22.4922 51.8297 22.5607 52.3979 22.8477C52.508 22.903 52.6121 22.9605 52.735 22.9867C53.054 23.0566 53.1494 23.3215 53.1809 23.603C53.2112 23.8734 53.0547 24.0519 52.8223 24.1667C51.7074 24.72 50.5509 25.1669 49.391 25.6074C49.1217 25.7098 48.8645 25.8551 48.616 26.0058C48.2956 26.2002 47.9484 26.2569 47.5528 26.279Z" fill="inherit"></path>
<path d="M77.95 17.9673C77.4228 17.9798 76.9775 17.847 76.6517 17.4258C76.2951 16.9651 76.3072 16.3654 76.6672 15.9062C77.1125 15.3376 77.7256 15.0084 78.3214 14.6501C78.5128 14.5346 78.6928 14.3963 78.8701 14.2572C79.2697 13.9432 79.445 13.5531 79.2939 13.0254C79.2099 12.7349 79.3489 12.5391 79.6136 12.5647C80.1441 12.6159 80.6781 12.6374 81.1999 12.7785C81.5955 12.885 81.8319 13.1291 81.8823 13.5289C81.9542 14.1051 82.1879 14.7048 81.7399 15.2387C81.6593 15.3348 81.5814 15.4414 81.4592 15.4773C80.7755 15.6807 80.2959 16.142 79.8667 16.7092C79.4893 17.2079 79.0017 17.5973 78.4483 17.8919C78.2757 17.9839 78.1004 17.95 77.95 17.9673Z" fill="inherit"></path>
<path d="M38.9208 23.5469C39.5144 23.6949 40.1081 23.8422 40.7012 23.9923C40.7657 24.0082 40.8248 24.0463 40.8886 24.0643C41.4406 24.2213 41.5434 24.4834 41.2392 24.9814C41.0672 25.2629 40.8973 25.5479 40.7039 25.8142C40.3405 26.3122 39.8059 26.2886 39.4822 25.7747C39.2445 25.3957 38.9724 25.0582 38.5675 24.8451C38.2827 24.6957 38.1323 24.0843 38.2834 23.8C38.4271 23.5282 38.6837 23.605 38.9208 23.5469Z" fill="inherit"></path>
<path d="M34.8712 25.7179C34.3594 25.7449 34.0585 25.4053 33.6986 25.2165C33.3453 25.0311 33.0981 24.7088 32.8859 24.3706C32.7012 24.0759 32.7684 23.8505 33.0847 23.7585C33.7557 23.5627 34.4299 23.6264 35.0868 23.8352C35.3568 23.921 35.6073 24.6445 35.5475 25.0491C35.4683 25.5893 35.3259 25.7186 34.8712 25.7179Z" fill="inherit"></path>
</svg>
</div>
</div>
</section>
<!-- FACILITIES -->
<div class="relative w-full h-full pt-[20vh] z-20 lg:absolute lg:top-0 left-0 lg:h-screen lg:pt-0 lg:px-[60px] lg:m-0 lg:bg-transparent lg:pointer-events-none lg:opacity-0" id="facilities" style="opacity: 0; visibility: hidden;">
<div class="js-f-anchor grid grid-cols-[16.5vw_67vw] items-end justify-start lg:absolute lg:top-[14vh] lg:grid-cols-[2vw_11vw] lg:gap-x-[2vw] lg:z-20" style="translate: none; rotate: none; scale: none; transform: translate(0%, 5%); opacity: 0; visibility: hidden;">
<h3 class="[writing-mode:vertical-lr] ml-[8px] lg:m-0 text-more-12 tracking-[0.4em] lg:text-more-14">強羅花壇の空間</h3>
<div class="w-2/3 mr-auto col-start-2 lg:w-full">
<a class="js-facilities-anchor link-underLine -short" href="#gora-hakone">
<span class="link-underLine__txt pointer-events-none lg:text-more-14">箱根</span>
<span class="link-underLine__line pointer-events-none -light"></span>
</a>
<a class="js-facilities-anchor link-underLine -short" href="#fuji">
<span class="link-underLine__txt pointer-events-none lg:text-more-14">富士</span>
<span class="link-underLine__line pointer-events-none -light"></span>
</a>
</div>
</div>
<div class="grid grid-cols-1 gap-y-[76px] w-sp-con mx-auto mt-more-60 lg:relative lg:w-full lg:h-full lg:m-0">
<div class="js-w-hotel grid grid-cols-1 gap-y-[32px] lg:absolute lg:top-0 lg:left-0 lg:w-full lg:h-full lg:grid-cols-[30%_40%_30%] lg:items-center" id="gora-hakone" style="grid-template-columns: 34% 32% 34%;">
<div class="lg:grid lg:col-start-2 lg:max-h-[calc(100vh-var(--s-more-20))]">
<div class="js-hotel-slider mask -vol-02 swiper aspect-[2/3] lg:w-full lg:h-full swiper-fade swiper-initialized swiper-horizontal swiper-watch-progress swiper-backface-hidden">
<div aria-live="off" class="swiper-wrapper" id="swiper-wrapper-109476d2ab3239295" style="translate: none; rotate: none; scale: none; filter: blur(5px); transform: scale(1.3, 1.3);">
<div aria-label="1 / 5" class="swiper-slide swiper-slide-visible swiper-slide-fully-visible swiper-slide-active" role="group" style="width: 576px; opacity: 1; transform: translate3d(0px, 0px, 0px);">
<img alt="gora-hakone-01" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3h1200004354s97985.webp"/>
</div>
<div aria-label="2 / 5" class="swiper-slide swiper-slide-next" role="group" style="width: 576px; opacity: 0; transform: translate3d(-576px, 0px, 0px);">
<img alt="gora-hakone-02" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2m1200004354sn899D.webp"/>
</div>
<div aria-label="3 / 5" class="swiper-slide" role="group" style="width: 576px; opacity: 0; transform: translate3d(-1152px, 0px, 0px);">
<img alt="gora-hakone-03" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4d1200004354sb2EE2.webp"/>
</div>
<div aria-label="4 / 5" class="swiper-slide" role="group" style="width: 576px; opacity: 0; transform: translate3d(-1728px, 0px, 0px);">
<img alt="gora-hakone-04" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb591200004354sdA5D5.webp"/>
</div>
<div aria-label="5 / 5" class="swiper-slide" role="group" style="width: 576px; opacity: 0; transform: translate3d(-2304px, 0px, 0px);">
<img alt="gora-hakone-05" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb651200004354sf6AAC.webp"/>
</div>
</div>
<span aria-atomic="true" aria-live="assertive" class="swiper-notification"></span></div>
</div>
<div class="js-f-info lg:w-[max(18vw,226px)] lg:my-0 lg:mx-auto">
<div class="js-f-info-i w-auto h-[26px] lg:w-[70%] lg:h-auto" style="filter: blur(4px); opacity: 0; visibility: hidden;">
<svg class="w-auto h-full lg:w-full lg:h-auto" fill="none" height="23" viewbox="0 0 129 23" width="129" xmlns="http://www.w3.org/2000/svg">
<path d="M33.4825 22.7473C33.3394 22.7635 33.1837 22.6958 33.0502 22.5563C32.94 22.4408 32.8208 22.3342 32.7017 22.2288C32.0472 21.6545 31.855 20.9617 32.125 20.1425C32.2496 19.7659 32.1394 19.6018 31.7358 19.5886C31.4274 19.5784 31.1178 19.5778 30.8095 19.588C30.5843 19.5958 30.3753 19.5479 30.1687 19.4695C29.813 19.3347 29.6544 19.0551 29.7238 18.6718C29.8568 17.9413 30.137 17.3173 30.8352 16.9329C31.0981 16.788 31.3119 16.5431 31.5226 16.3191C31.7274 16.1023 31.6789 15.9862 31.3903 15.9173C30.9262 15.8077 30.4604 15.7041 29.9981 15.5862C29.7508 15.5233 29.4447 15.4844 29.4034 15.1718C29.3519 14.7814 29.2873 14.3442 29.6519 14.0592C30.4298 13.4514 31.0723 12.6909 31.867 12.1077C32.258 11.8203 32.631 11.5077 33.0586 11.2658C33.5975 10.9604 34.6442 11.1328 35.079 11.7089C35.4712 12.2299 35.9371 12.6975 36.3017 13.2472C36.397 13.3915 36.555 13.4975 36.6934 13.6095C36.861 13.7448 37.0018 13.6502 37.0425 13.4939C37.1496 13.0825 37.4472 12.858 37.7682 12.6287C38.0772 12.4077 38.2862 12.0789 38.5454 11.8041C38.87 11.4604 39.2377 11.1807 39.6545 10.9562C39.8934 10.8274 40.1263 10.7568 40.3503 10.9358C40.5886 11.125 40.773 11.3676 40.7628 11.6927C40.7569 11.8795 40.6934 12.0646 40.682 12.252C40.6665 12.5011 40.5569 12.6897 40.3676 12.837C40.061 13.0754 39.9628 13.4143 39.9024 13.773C39.8814 13.8963 39.9083 14.0293 39.9868 14.1095C40.0874 14.2125 40.1826 14.0861 40.2736 14.0383C40.5551 13.8909 40.8581 13.8999 41.1491 13.9604C41.4263 14.0179 41.5563 13.94 41.6173 13.6652C41.7707 12.9694 42.1395 12.4059 42.7102 11.976C43.0102 11.7508 43.1288 11.7813 43.2886 12.1227C43.3186 12.1855 43.3371 12.2652 43.3868 12.3059C43.7377 12.5933 43.8449 12.9885 43.9 13.4119C43.9282 13.6299 44.0252 13.6873 44.2677 13.6562C44.7156 13.5987 45.1324 13.4287 45.5737 13.3568C45.9288 13.2993 46.1054 13.4233 46.1396 13.7843C46.1695 14.0993 46.0928 14.4005 45.9737 14.6862C45.8827 14.9059 45.7677 15.1179 45.6432 15.3209C45.4917 15.5676 45.2473 15.6802 44.9653 15.6892C44.727 15.6969 44.6959 15.8095 44.7725 16C44.8204 16.1197 44.8659 16.2407 44.9168 16.3592C45.0282 16.6209 44.9449 16.7754 44.6563 16.7652C44.3623 16.755 44.1084 16.8365 43.8647 16.9832C43.6174 17.1317 43.5898 17.2838 43.7593 17.5227C43.8934 17.7113 44.1114 17.8557 44.0749 18.1371C44.0563 18.282 44.0174 18.403 43.8988 18.4988C43.4533 18.8605 43.0114 19.2251 42.6335 19.6623C42.4156 19.9144 42.0192 19.8814 41.8874 19.5647C41.6814 19.0706 41.3149 18.706 40.9862 18.3132C40.764 18.0479 40.794 17.8143 40.9371 17.5503C40.9748 17.4802 41.0174 17.4125 41.0557 17.3431C41.2976 16.903 41.2569 16.791 40.7934 16.6173C40.4868 16.5023 40.4443 16.3389 40.6557 16.0928C40.7245 16.0125 40.8389 15.9491 40.776 15.8155C40.7191 15.6958 40.6042 15.6898 40.4928 15.6946C40.3179 15.7017 40.1581 15.6658 40.009 15.5724C39.8545 15.4748 39.7646 15.5437 39.7257 15.7011C39.6395 16.0485 39.5407 16.394 39.4796 16.7455C39.4245 17.0616 39.585 17.3515 39.6742 17.6419C39.8066 18.073 39.8072 18.4802 39.6359 18.894C39.4802 19.2694 39.4634 19.697 39.2227 20.0395C38.9712 20.397 38.7425 20.7623 38.5862 21.1761C38.5131 21.3701 38.282 21.3665 38.1185 21.1904C37.894 20.9491 37.7921 20.6443 37.7209 20.3323C37.6395 19.9736 37.5688 19.6138 37.5694 19.2407C37.57 18.8736 37.3658 18.7575 37.0736 18.9659C36.6419 19.2742 36.1981 19.4234 35.6514 19.3449C35.1346 19.2707 34.6275 19.4413 34.1286 19.5814C33.8855 19.6497 33.8412 19.8659 33.852 20.0581C33.8789 20.5132 33.9197 20.9737 34.2286 21.3491C34.5041 21.6826 34.3855 22.024 34.2448 22.3581C34.1137 22.6689 33.8394 22.7443 33.4825 22.7473ZM33.3963 18.4251C33.9382 18.4928 34.3843 18.27 34.8221 18.0335C34.9969 17.9395 35.0412 17.8185 34.9005 17.6227C34.6682 17.3 34.352 17.0742 34.0604 16.8209C33.9221 16.7012 33.746 16.6982 33.558 16.788C33.0712 17.0209 32.6741 17.3539 32.337 17.77C32.1232 18.0335 32.1646 18.212 32.5017 18.3042C32.7981 18.3862 33.1011 18.4515 33.3963 18.4251ZM37.6832 14.7329C37.5449 15.2431 37.146 15.4916 36.7658 15.7628C36.5496 15.9167 36.3317 15.9736 36.0664 15.9281C35.8634 15.8928 35.6472 15.8898 35.4484 15.9736C35.252 16.0556 35.2364 16.1994 35.4053 16.3305C35.4592 16.3724 35.5275 16.397 35.5832 16.4377C36.173 16.8682 36.9365 17.0179 37.4395 17.591C37.5437 17.7101 37.6173 17.6359 37.6461 17.5179C37.7221 17.1994 37.7928 16.882 37.7473 16.5473C37.6994 16.1946 37.7059 15.8389 37.7449 15.4838C37.7724 15.2341 37.8018 14.9808 37.6832 14.7329ZM34.1808 14.4472C34.3328 14.4664 34.4107 14.4251 34.4712 14.3538C34.5472 14.264 34.4838 14.182 34.4364 14.1107C34.2592 13.8442 34.0478 13.6095 33.7652 13.4496C33.6412 13.3802 33.5089 13.3305 33.3676 13.3897C33.0754 13.5125 32.7735 13.6233 32.5412 13.8502C32.4191 13.9694 32.2017 14.0778 32.2861 14.2778C32.3694 14.4754 32.6233 14.4706 32.7891 14.4454C33.2748 14.3724 33.7532 14.3293 34.1808 14.4472Z" fill="#111111"></path>
<path d="M30.2272 8.25834C30.2266 7.35354 30.223 6.44815 30.229 5.54334C30.2308 5.19722 30.1925 4.85352 30.2003 4.50441C30.2123 3.98464 30.5194 3.76548 30.8955 3.56128C31.0524 3.47565 31.2177 3.40679 31.3787 3.32894C31.7853 3.13195 32.171 3.34093 32.5638 3.39243C32.6889 3.40858 32.8045 3.48943 32.9296 3.51696C33.1716 3.56967 33.1548 3.7104 33.0566 3.86968C32.9422 4.05591 32.8326 4.25172 32.6835 4.408C32.2865 4.82297 32.2973 5.314 32.353 5.83198C32.4183 6.44336 32.5254 7.05054 32.5266 7.66792C32.5272 8.19966 32.9704 8.5859 33.4961 8.53199C33.935 8.48709 34.3249 8.24337 34.7662 8.21883C35.2943 8.18948 35.8015 7.97511 36.3255 7.99067C36.8698 8.00685 37.3932 7.95714 37.9165 7.82421C38.4052 7.69966 38.9094 7.65355 39.4034 7.54577C40.0728 7.39965 40.7645 7.4296 41.4501 7.46493C41.7285 7.47929 42.0082 7.4745 42.2872 7.4715C42.5681 7.46792 42.7603 7.33977 42.837 7.05953C42.9812 6.53197 43.0992 5.99784 43.1866 5.45831C43.2387 5.13555 43.1316 5.02298 42.7968 5.03915C42.2322 5.06609 41.6681 5.03855 41.1028 5.14814C40.7171 5.22299 40.4878 5.39963 40.371 5.74814C40.2393 6.14156 39.9627 6.40084 39.6159 6.59904C39.3381 6.75773 39.1148 6.7248 38.8872 6.50264C38.7082 6.32719 38.5405 6.14156 38.4423 5.90922C38.35 5.69185 38.2111 5.58286 37.9638 5.58586C37.6878 5.58946 37.4477 5.64693 37.2943 5.89245C37.2375 5.98347 37.1956 6.09125 37.177 6.19664C37.098 6.63796 36.8009 6.93379 36.498 7.22001C36.2513 7.45296 35.9961 7.44157 35.768 7.1787C35.574 6.95475 35.3458 6.75234 35.2183 6.47629C35.1536 6.33797 35.0488 6.30203 34.9057 6.30084C34.5692 6.29724 34.2548 6.40623 33.9488 6.51222C33.3698 6.71342 33.044 6.3733 32.747 5.98826C32.4734 5.63317 32.5644 5.24514 32.7722 4.89723C32.9267 4.63855 33.1554 4.44392 33.4799 4.4056C33.7847 4.36968 34.0884 4.31279 34.3943 4.29543C34.8243 4.27147 35.0033 4.1559 35.1123 3.73194C35.1829 3.4535 35.2434 3.16906 35.2674 2.88343C35.2967 2.53193 35.4297 2.22953 35.6213 1.94569C35.889 1.54989 36.2878 1.47504 36.6806 1.75228C37.2435 2.1487 37.5728 2.6445 37.3866 3.37625C37.3177 3.64812 37.3507 3.93075 37.4153 4.20619C37.4375 4.30022 37.471 4.37627 37.5746 4.39902C37.8608 4.4613 38.3956 4.05531 38.4117 3.7595C38.4381 3.26069 38.4057 2.76308 38.3393 2.27024C38.2596 1.687 38.4034 1.1385 38.604 0.612151C38.7435 0.246868 39.101 0.0845928 39.4776 0.00674468C39.5788 -0.0141975 39.6776 0.0145295 39.7591 0.0804081C40.074 0.337289 40.4094 0.575018 40.6962 0.860662C41.0058 1.16785 41.1567 1.54509 41.019 2.00198C40.919 2.33433 40.9082 2.68823 40.7884 3.01936C40.7501 3.12535 40.7681 3.25769 40.7813 3.37505C40.8213 3.74212 40.9411 3.85111 41.3082 3.8038C41.6417 3.76129 41.983 3.74632 42.3016 3.62356C42.5777 3.51696 42.8699 3.54392 43.1543 3.51457C43.5567 3.47267 43.9124 3.27925 44.2968 3.18045C44.8789 3.03135 45.5035 3.27445 45.6771 3.8014C45.7418 3.99842 45.8364 4.18584 45.9052 4.38225C46.0406 4.76908 45.9406 5.10083 45.6675 5.39844C45.3453 5.74874 45.1579 6.15174 45.204 6.64516C45.2711 7.35893 45.0094 7.97032 44.5962 8.53141C44.5196 8.63559 44.443 8.741 44.3789 8.85296C44.0274 9.47153 43.4465 9.70986 42.7926 9.43262C42.3459 9.24339 41.8968 9.17693 41.4309 9.2386C40.989 9.29788 40.5501 9.20507 40.1004 9.30088C39.5848 9.41106 39.0836 9.56973 38.571 9.68172C37.6339 9.88651 36.7022 10.1123 35.7231 10.0787C35.0303 10.0554 34.3339 10.1452 33.6386 10.1775C33.3877 10.1895 33.1997 10.3722 32.9686 10.4153C32.2752 10.544 31.6344 10.3614 31.0254 10.0356C31.0087 10.0266 30.9967 10.0057 30.9793 9.99848C30.2793 9.70867 30.132 9.14818 30.2266 8.46734C30.2356 8.39907 30.2278 8.3284 30.2272 8.25834Z" fill="#111111"></path>
<path d="M5.99703 8.58825C6.03892 7.90143 6.13712 7.18943 6.1084 6.47087C6.10299 6.34153 6.09162 6.21279 6.07905 6.08463C6.06169 5.91757 6.01556 5.76068 5.82515 5.72415C5.62457 5.68523 5.4503 5.70979 5.28324 5.87625C4.86286 6.29421 4.51437 6.81159 4.00357 7.1044C3.48919 7.39902 2.94009 7.72238 2.27121 7.45651C1.88618 7.30321 1.46883 7.23556 1.08979 7.04872C0.673609 6.84392 0.378371 6.51159 0.0963633 6.16668C-0.0114545 6.03494 -0.0821026 5.83493 0.184957 5.77746C0.691554 5.66847 1.01552 5.28104 1.40056 4.99539C1.77061 4.72115 2.1335 4.44868 2.57241 4.29659C2.82273 4.20977 3.02872 4.04329 3.22272 3.87922C3.73293 3.44808 4.29998 3.09837 4.84013 2.71095C5.01976 2.58219 5.22575 2.51633 5.45151 2.49418C6.08084 2.4307 6.62994 2.5313 7.18204 2.91813C7.64792 3.24509 8.13837 3.55168 8.62161 3.86066C9.32341 4.31097 9.53719 4.90978 9.3725 5.69961C9.19465 6.55351 9.05515 7.41519 8.87968 8.2703C8.65633 9.36012 8.33957 10.4278 8.15094 11.5248C8.07909 11.9434 7.96769 12.3673 7.6689 12.6985C7.22217 13.1937 6.67365 13.435 5.9982 13.3314C5.9 13.3159 5.80121 13.2979 5.70658 13.2698C5.62157 13.244 5.55209 13.2566 5.48382 13.3099C5.35447 13.4099 5.33474 13.8566 5.44011 14.0991C5.53415 14.3171 5.71316 14.3123 5.89221 14.2734C6.14374 14.2189 6.39581 14.1584 6.63773 14.0722C7.4078 13.7979 7.87127 14.3542 7.9246 14.9913C7.96052 15.4279 7.84013 15.8374 7.72698 16.2488C7.56112 16.8518 7.34373 17.447 7.22575 18.0548C7.06827 18.8686 7.04674 19.7075 6.9557 20.5345C6.9012 21.0315 6.84371 21.5429 6.35989 21.8363C5.96527 22.0752 5.50239 22.0591 5.06348 22.0932C4.87127 22.1082 4.72752 21.9118 4.58922 21.7657C4.16406 21.3172 4.07005 20.698 3.76466 20.1872C3.64909 19.9938 3.53232 19.7932 3.48981 19.562C3.43948 19.289 3.50955 19.1854 3.78381 19.1758C3.87244 19.1722 3.96286 19.1854 4.05028 19.2016C4.46467 19.2764 4.53473 19.2453 4.58323 18.8381C4.63652 18.3932 4.75567 17.962 4.82273 17.5213C4.86107 17.2656 4.81794 17.0081 4.81915 16.7524C4.82094 16.4141 4.43773 16.2368 4.14249 16.4069C4.03171 16.471 3.93471 16.5518 3.80957 16.5985C3.60954 16.674 3.45027 16.6428 3.282 16.5189C2.7269 16.1093 2.66166 15.9752 2.8455 15.3117C2.96165 14.8913 3.10894 14.4782 3.27063 14.0728C3.52811 13.4266 3.68381 12.7703 3.64551 12.0673C3.62153 11.6392 3.84371 11.4482 4.27604 11.4949C4.52216 11.5212 4.76407 11.5883 5.0084 11.6344C5.34132 11.6979 5.40718 11.6536 5.48265 11.3308C5.6587 10.5775 5.86348 9.83018 5.97605 9.06252C5.9982 8.91282 6.00061 8.76611 5.99703 8.58825Z" fill="#111111"></path>
<path d="M89.9632 7.61342C89.9614 7.25832 90.0554 7.11101 90.5949 6.99365C90.7949 6.95052 91.0057 6.94034 91.2159 6.98108C91.7428 7.08286 92.2482 6.90323 92.7416 6.78646C93.7584 6.54513 94.7872 6.38645 95.8153 6.21639C96.3525 6.12716 96.901 6.12237 97.4411 6.06548C98.1279 5.99303 98.8291 5.97207 99.492 5.75172C99.7429 5.66787 99.9914 5.54153 100.205 5.38525C100.438 5.21458 100.666 5.22836 100.932 5.24154C101.602 5.27565 101.975 5.71818 102.315 6.19483C102.566 6.54633 102.438 6.97808 102.351 7.32598C101.971 8.83558 101.403 10.2805 100.565 11.5979C100.354 11.9302 100.153 12.3374 99.695 12.4524C99.5645 12.4853 99.4333 12.5099 99.3154 12.4506C99.0201 12.3009 98.7184 12.2925 98.392 12.3075C97.8597 12.3314 97.3231 12.2949 96.7908 12.2937C96.198 12.2925 95.604 12.4284 95.0321 12.5883C94.2854 12.7973 93.5321 12.9781 92.7788 13.1578C92.2159 13.2919 91.8338 12.9428 91.5129 12.5428C90.9231 11.8063 90.6219 10.9542 90.5638 10.017C90.5297 9.46253 90.3787 8.94517 90.135 8.44994C90.0194 8.21462 89.9584 7.96432 89.9632 7.61342ZM94.6405 11.1374C94.9926 11.2182 95.2501 11.056 95.5291 11.0506C96.3848 11.0338 97.2405 11.0805 98.0974 11.023C98.2914 11.0099 98.4052 10.9368 98.5273 10.8135C98.7244 10.6134 98.7435 10.3494 98.798 10.1003C98.9423 9.44038 99.2459 8.82601 99.3351 8.14995C99.3938 7.70863 99.1968 7.51401 98.7711 7.64754C98.5651 7.71163 98.3603 7.74395 98.1531 7.71521C97.1764 7.57868 96.1986 7.55233 95.2153 7.63497C95.0015 7.65293 94.7818 7.70263 94.5632 7.65054C94.1069 7.54155 93.677 7.63917 93.253 7.81223C93.0189 7.90743 92.9105 8.06253 92.9165 8.31522C92.9327 9.0745 92.7752 9.81703 92.6674 10.5619C92.559 11.3111 92.9806 11.4063 93.5303 11.3332C93.0835 10.9847 92.8823 10.5248 93.0902 10.1392C93.4333 9.50206 93.7375 8.84037 94.1782 8.25952C94.3387 8.04815 94.5345 7.93676 94.8165 7.94934C95.1638 7.96491 95.5129 7.93437 95.8614 7.93318C96.0405 7.93198 96.2111 7.94934 96.3423 8.11461C96.4195 8.21163 96.5423 8.2757 96.653 8.34156C96.968 8.52899 97.3064 8.68288 97.5992 8.89905C97.9393 9.15056 98.083 9.50745 98.0094 9.94638C97.9417 10.3488 97.6363 10.3955 97.3231 10.4919C96.777 10.6602 96.2111 10.6943 95.6566 10.7991C95.3237 10.8613 94.9758 10.9134 94.6405 11.1374ZM95.9171 9.59846C95.9112 9.42481 95.492 8.92001 95.3255 8.88229C95.2393 8.86254 95.1626 8.88169 95.1069 8.94517C94.8495 9.23858 94.653 9.57212 94.5321 9.94159C94.4776 10.1081 94.5608 10.241 94.7542 10.1482C95.0782 9.99309 95.4285 9.91882 95.7602 9.79008C95.8471 9.75594 95.9219 9.70925 95.9171 9.59846Z" fill="#111111"></path>
<path d="M92.8811 18.7816C92.8721 18.3528 92.938 17.9307 92.9841 17.5061C93.0272 17.1127 92.8398 16.7474 92.8092 16.3618C92.7584 15.7103 92.447 15.1474 92.2086 14.56C92.0158 14.0839 92.2362 13.739 92.744 13.6755C93.1937 13.6198 93.6326 13.6761 94.0757 13.721C94.3374 13.7474 94.538 13.9067 94.7362 14.0498C95.2039 14.3863 95.6853 14.3073 96.1799 14.1486C96.5853 14.0186 96.9105 13.7552 97.2566 13.5216C97.8057 13.1516 98.4548 13.0073 99.0273 13.3186C99.4285 13.5366 99.8081 13.8857 100.095 14.2839C100.157 14.3707 100.136 14.466 100.113 14.5689C100.008 15.0474 99.8291 15.5007 99.6656 15.9606C99.4548 16.5546 99.383 17.1791 99.3063 17.8007C99.2716 18.0863 99.2692 18.3762 99.2399 18.6624C99.1578 19.4612 98.2309 20.0588 97.4776 19.9906C97.0189 19.9486 96.8021 19.6043 96.9201 19.1588C96.9428 19.0726 96.9632 18.981 97.0081 18.9055C97.4063 18.2367 97.3231 17.554 96.9955 16.9091C96.7518 16.43 96.8135 15.9713 96.9021 15.4917C96.9416 15.2791 97.0422 15.027 96.8686 14.8695C96.6961 14.7133 96.4985 14.9145 96.3201 14.9797C95.826 15.1606 95.3494 15.3863 94.8236 15.4785C94.5686 15.5228 94.4057 15.7785 94.4781 16.0809C94.6302 16.7187 94.6716 17.3792 94.8823 18.0037C94.9105 18.087 94.9153 18.2085 95.0075 18.2235C95.1296 18.2432 95.162 18.1073 95.1931 18.0253C95.3135 17.7043 95.3308 17.3798 95.1278 17.0821C94.9111 16.7654 94.8931 16.4055 94.9165 16.0492C94.9416 15.6642 95.3069 15.6151 95.7231 15.8007C96.4512 16.1258 96.5087 16.8169 96.7333 17.4271C96.9087 17.9055 96.874 18.3732 96.7237 18.857C96.5919 19.2786 96.5997 19.7367 96.3811 20.1427C96.2776 20.3349 96.4051 20.4421 96.6057 20.4313C96.8835 20.4157 97.1608 20.3774 97.4213 20.275C98.3536 19.9091 99.271 19.9762 100.182 20.3528C100.357 20.4253 100.53 20.5055 100.712 20.5522C100.945 20.6127 100.939 20.7355 100.843 20.9055C100.727 21.1139 100.609 21.3217 100.499 21.5331C100.445 21.6361 100.375 21.7187 100.268 21.7552C99.4692 22.0235 98.6812 22.2205 97.8297 21.8984C97.1991 21.6594 96.5243 21.7187 95.8542 21.8026C95.2841 21.8732 94.8823 22.1223 94.5865 22.6127C94.3949 22.9295 94.0488 22.9589 93.7063 22.9241C93.0308 22.8559 92.4655 22.5469 91.941 22.1337C91.7565 21.9888 91.7332 21.8415 91.8128 21.6415C91.9961 21.181 92.3661 21.0133 92.8248 20.9876C93.1919 20.9672 93.5452 20.8505 93.9183 20.8516C94.1368 20.8522 94.3332 20.7199 94.4907 20.5582C94.7578 20.2846 94.9476 19.9564 95.1267 19.6235C95.1799 19.5253 95.2267 19.3816 95.0823 19.3073C94.9578 19.2433 94.8829 19.351 94.8087 19.4319C94.8021 19.4391 94.7931 19.4462 94.7901 19.4552C94.5697 20.1463 94.0428 20.0019 93.5458 19.8995C93.0715 19.8013 92.8847 19.5462 92.8811 19.0504V18.7816Z" fill="#111111"></path>
<path d="M70.0851 22.6268C69.0204 22.7801 68.0144 22.237 67.0796 21.5819C66.5227 21.1908 66.0862 20.6447 65.8263 19.9842C65.4215 18.9549 65.2257 17.8788 65.1467 16.7866C65.103 16.1854 65.1592 15.574 65.2023 14.9692C65.2287 14.6022 65.2293 14.2357 65.2389 13.8692C65.2586 13.0931 65.3957 12.3297 65.4928 11.5626C65.5329 11.2434 65.8215 11.0273 65.9874 10.7584C66.4527 10.0057 67.2018 9.57453 67.8826 9.06496C68.0659 8.92783 68.2527 8.79609 68.4413 8.66735C68.7766 8.43859 69.1713 8.50386 69.4186 8.82063C69.8072 9.31825 70.0192 9.8955 70.1371 10.5039C70.2455 11.0608 69.7287 11.5273 69.1269 11.4285C68.9682 11.4027 68.812 11.3871 68.6533 11.3871C68.1305 11.3871 67.8658 11.5877 67.7353 12.0949C67.6096 12.5841 67.6437 13.0836 67.667 13.58C67.709 14.453 67.8628 15.3165 67.8778 16.1926C67.888 16.7878 67.9341 17.3824 67.9617 17.9776C67.967 18.0926 68.015 18.192 68.0694 18.2848C68.3706 18.8052 68.6072 19.3633 69.1862 19.6812C69.527 19.8687 69.8797 19.8441 70.2264 19.8759C70.4886 19.8998 70.9012 19.3627 70.9803 19.0351C71.1336 18.3992 71.1731 17.7477 71.2707 17.104C71.3078 16.8567 71.4503 16.6656 71.6216 16.4938C71.797 16.3171 71.9773 16.3405 72.0923 16.5387C72.2695 16.8453 72.4994 17.1327 72.5479 17.5016C72.6204 18.0519 72.8881 18.5615 72.8773 19.1291C72.8647 19.7938 72.8653 20.4591 72.8468 21.1238C72.8414 21.3196 72.7168 21.4825 72.5887 21.619C71.8809 22.3765 71.4066 22.6944 70.0851 22.6268Z" fill="#111111"></path>
<path d="M16.0652 2.90069C16.0544 3.47975 15.8221 3.96717 15.4903 4.34742C14.8292 5.10432 14.4574 6.01451 13.9717 6.86722C13.3017 8.04448 12.9424 9.34209 12.4514 10.5888C12.3214 10.9182 12.2167 11.2667 12.216 11.6307C12.2154 11.9619 12.3035 12.0798 12.6286 12.0852C13.2646 12.096 13.8975 12.0343 14.5274 11.9511C14.7682 11.9194 14.8604 11.717 14.9382 11.5194C15.1089 11.0852 15.2741 10.6493 15.4406 10.214C15.5532 9.92055 15.7676 9.69179 15.9436 9.4397C16.0789 9.24449 16.273 9.25527 16.4065 9.45108C16.4622 9.53191 16.5047 9.62472 16.5395 9.71754C16.8191 10.4643 17.1993 11.1637 17.5664 11.8685C17.6886 12.1032 17.7089 12.3397 17.5844 12.5523C17.3892 12.8852 17.1568 13.1978 16.9299 13.511C16.8119 13.6751 16.628 13.696 16.4365 13.7397C15.9598 13.8487 15.5263 13.7439 15.0724 13.6206C14.7286 13.5272 14.3562 13.6457 14.0041 13.6284C13.1747 13.5883 12.4154 13.7912 11.6579 14.0966C11.1442 14.3038 10.6675 14.1865 10.2735 13.7625C10.113 13.59 9.91063 13.4535 9.71722 13.3146C9.38488 13.0751 9.25492 12.7481 9.38667 12.3589C9.47889 12.0864 9.58309 11.8122 9.72559 11.5637C10.0968 10.9176 10.1382 10.1774 10.3495 9.48701C10.6316 8.56484 10.8603 7.62891 11.0298 6.68159C11.2358 5.52947 11.6987 4.49833 12.3005 3.49711C12.9035 2.49411 13.6226 1.5905 14.3669 0.69947C14.5819 0.441384 14.8622 0.416841 15.1562 0.51205C15.4412 0.604865 15.6712 0.747988 15.7191 1.09529C15.7862 1.57614 15.8382 2.06117 16.0161 2.52165C16.0646 2.64681 16.0532 2.7953 16.0652 2.90069Z" fill="#111111"></path>
<path d="M64.1603 5.00898C64.5675 4.93115 64.9848 4.91437 65.3968 4.86169C65.8812 4.79941 66.0178 4.63354 66.0686 4.14132C66.107 3.77185 66.1615 3.39999 66.0716 3.02753C66.0477 2.92872 66.0226 2.83411 65.9633 2.75327C65.7621 2.47961 65.8147 2.20895 65.977 1.94547C66.107 1.73351 66.2519 1.5311 66.386 1.32212C66.5172 1.11853 66.695 1.02272 66.9405 1.05206C67.8998 1.16642 68.7507 1.54667 69.4184 2.23172C69.8968 2.72214 69.9046 3.11376 69.292 3.41675C69.0004 3.56047 68.7615 3.79161 68.4369 3.89221C68.2304 3.95628 68.1507 4.15928 68.2148 4.38503C68.283 4.62275 68.4711 4.55329 68.6346 4.54372C69.4884 4.49281 70.2801 4.22635 71.0436 3.85568C71.3609 3.70239 71.7759 3.88862 71.9544 4.25329C72.4106 5.18145 72.0735 5.91079 71.0825 6.2132C70.7274 6.32158 70.3986 6.35511 70.0382 6.25451C69.7142 6.16349 69.3753 6.12517 69.0417 6.07128C68.7777 6.02875 68.6262 6.17906 68.4884 6.38625C68.1974 6.82279 67.9573 7.29525 67.5639 7.66112C67.1513 8.04495 67.0783 8.03119 66.6657 7.65393C66.3543 7.3695 66.195 7.07128 66.2327 6.64015C66.2633 6.29223 66.1723 6.23295 65.8196 6.24373C65.4728 6.25451 65.1249 6.22098 64.7788 6.29823C64.513 6.35811 64.3321 6.46649 64.2824 6.7659C64.2213 7.13297 64.174 7.50602 63.9824 7.84315C63.7112 8.321 63.2483 8.41083 62.8243 8.04316C62.5435 7.79885 62.3069 7.51262 62.159 7.17129C62.062 6.94793 61.9321 6.92039 61.7368 7.0132C61.2877 7.22637 60.8333 7.42998 60.3907 7.65633C59.8476 7.93417 59.3314 7.87909 58.8362 7.55633C58.6339 7.42399 58.4212 7.32757 58.1943 7.24733C57.965 7.16651 57.7314 7.04853 57.7266 6.7659C57.7219 6.50242 57.9254 6.35751 58.1494 6.27966C58.5805 6.12996 58.9045 5.8048 59.3021 5.60002C59.5254 5.48504 59.7416 5.43234 59.9841 5.48444C60.1985 5.53054 60.4147 5.55211 60.6351 5.54732C61.1249 5.53774 61.4075 5.21438 61.2692 4.74132C61.0997 4.16047 60.8925 3.58801 60.4386 3.14848C60.283 2.99699 60.2835 2.794 60.3674 2.61913C60.6105 2.11195 60.8883 1.66463 61.5393 1.56763C62.1243 1.4808 62.6824 1.46764 63.2357 1.6832C63.6513 1.84487 63.7255 2.00896 63.5974 2.44907C63.4872 2.82753 63.3267 3.20179 63.4177 3.60958C63.4973 3.96766 63.5878 4.32275 63.6758 4.67904C63.7375 4.93055 63.8884 5.0551 64.1603 5.00898Z" fill="#111111"></path>
<path d="M14.3845 19.3338C14.3755 19.5368 14.3593 19.6758 14.3659 19.8147C14.3737 19.9949 14.4569 20.0842 14.6335 19.9644C14.8934 19.7889 15.13 19.5889 15.1808 19.2542C15.2108 19.0596 15.3228 18.9662 15.5036 18.9129C16.024 18.7596 16.5551 18.7548 17.0857 18.7991C17.3575 18.8219 17.6024 18.9404 17.7563 19.1931C18.1982 19.9189 18.4468 20.6848 18.2755 21.5465C18.206 21.8961 18.1055 21.9489 17.7635 21.88C17.033 21.7339 16.2988 21.6069 15.5839 21.3925C15.5156 21.3722 15.4467 21.3824 15.382 21.4201C14.8749 21.7123 14.339 21.9584 13.8934 22.3512C13.5341 22.668 13.2551 22.6261 12.9162 22.2752C12.4497 21.7919 12.2778 21.2273 12.306 20.5662C12.3233 20.1512 12.2407 19.7356 12.2461 19.3165C12.2503 19.0446 12.0736 18.8536 11.8359 18.7446C11.6024 18.638 11.4048 18.75 11.2479 18.9183C10.9886 19.1961 10.6856 19.2961 10.3077 19.232C9.88381 19.1602 9.66342 18.8817 9.57662 18.5051C9.44008 17.9117 9.58258 17.3194 9.65085 16.729C9.65984 16.65 9.68857 16.5715 9.68619 16.4937C9.66284 15.8488 10.0401 15.4242 10.4862 15.041C10.6311 14.9164 10.8173 14.8973 10.9958 14.953C11.2646 15.0368 11.5407 15.0559 11.8167 15.0919C12.0778 15.1254 12.2156 15.0206 12.2743 14.7853C12.3156 14.6218 12.3323 14.453 12.3731 14.2895C12.4688 13.9056 12.667 13.7967 13.0371 13.9284C13.5569 14.1128 14.0473 14.3458 14.3868 14.808C14.506 14.9703 14.6749 14.9715 14.8467 14.9404C15.2905 14.8595 15.7389 14.8859 16.1857 14.8781C16.4881 14.8727 16.7342 14.9817 16.9545 15.2074C17.1659 15.4248 17.3222 15.6751 17.4971 15.9152C17.6498 16.1242 17.6755 16.3817 17.5881 16.6015C17.306 17.3099 16.8881 17.9362 16.3054 18.435C16.0701 18.6368 15.7767 18.6464 15.4904 18.5428C15.285 18.468 15.088 18.3686 14.8695 18.3368C14.5078 18.2841 14.3012 18.5033 14.3521 18.8614C14.3773 19.038 14.3773 19.2183 14.3845 19.3338ZM14.4934 16.6176C14.4904 17.02 14.5402 17.0739 14.9515 17.1476C15.1168 17.1769 15.2749 17.1763 15.3737 17.0146C15.4779 16.8446 15.4126 16.6787 15.3042 16.547C15.1413 16.35 14.8988 16.2931 14.6605 16.3135C14.4832 16.3284 14.4898 16.5033 14.4934 16.6176ZM12.0731 16.7715C12.079 16.5464 11.9419 16.4919 11.782 16.4865C11.497 16.4769 11.2126 16.7769 11.2209 17.0709C11.2257 17.2482 11.2533 17.4177 11.4904 17.4194C11.8018 17.4213 12.0736 17.1075 12.0731 16.7715Z" fill="#111111"></path>
<path d="M86.8939 18.2005C86.0735 18.2298 85.4562 17.8119 84.8035 17.5101C84.6256 17.4274 84.4633 17.261 84.516 17.0316C84.5693 16.8041 84.7454 16.658 84.9711 16.6065C85.2603 16.5412 85.5077 16.4023 85.7442 16.2334C85.8634 16.1484 85.9149 16.037 85.8711 15.8969C85.8059 15.6879 85.7376 15.4795 85.6562 15.2759C85.5945 15.1214 85.5011 15.0717 85.3753 15.2322C85.2711 15.3651 85.1651 15.4981 85.0454 15.616C84.8436 15.813 84.6885 15.7855 84.4897 15.5915C83.7927 14.9124 83.2615 14.1184 82.8148 13.2597C82.7238 13.0855 82.5879 12.9352 82.4705 12.7753C82.3202 12.5711 82.3986 12.4567 82.6088 12.3687C83.1525 12.14 83.7316 12.119 84.3052 12.0783C84.7711 12.0453 84.8077 12.0226 84.8124 11.5513C84.8142 11.3597 84.7777 11.1783 84.731 10.9932C84.5538 10.286 84.904 9.77285 85.6214 9.67284C85.7783 9.65129 85.9358 9.63033 86.0939 9.61895C86.3753 9.5974 86.5963 9.77404 86.6694 10.1166C86.7957 10.704 87.0747 11.2393 87.2346 11.813C87.3801 12.3363 87.7688 12.4531 88.2328 12.4753C88.5101 12.4885 88.7921 12.4369 89.061 12.5435C89.17 12.5866 89.3041 12.601 89.3179 12.7549C89.3305 12.895 89.2358 12.9783 89.1358 13.0435C88.8125 13.2543 88.5011 13.4861 88.1478 13.6483C87.8981 13.7627 87.7807 13.9471 87.8346 14.222C87.9101 14.6094 88.1376 14.71 88.4334 14.4591C89.0047 13.9735 89.7047 13.6783 90.276 13.1938C90.4077 13.0819 90.5874 13.0915 90.7305 13.1639C90.8808 13.2406 90.8305 13.4082 90.8227 13.5442C90.8095 13.7555 90.6802 13.9124 90.5538 14.0663C90.0933 14.6256 89.5502 15.1364 89.1891 15.7537C88.8119 16.3981 88.3131 16.9927 88.1358 17.7448C88.1071 17.867 88.04 17.9328 87.9376 17.9855C87.5915 18.1616 87.2263 18.2322 86.8939 18.2005Z" fill="#111111"></path>
<path d="M59.4957 19.8827C59.4927 19.1983 59.7166 18.6666 59.8172 18.1115C59.9358 17.46 59.9238 16.833 59.728 16.2031C59.6705 16.0174 59.5417 15.9785 59.3825 15.9995C58.9849 16.0522 58.5968 16.051 58.2106 15.9126C57.7609 15.751 57.6064 15.5695 57.6268 15.1707C57.6609 14.5072 57.7854 14.3276 58.3034 14.2156C59.3154 13.9971 59.9639 13.3695 60.3274 12.421C60.5801 11.7617 60.8352 11.1042 61.0819 10.4425C61.295 9.86946 61.6681 9.73114 62.1945 10.0521C62.5071 10.2431 62.7131 10.5305 62.9095 10.8371C63.1657 11.2377 63.4783 11.6042 63.6951 12.0294C64.049 12.724 63.8472 13.2647 63.1574 13.6402C62.7573 13.8569 62.3604 14.0929 62.0442 14.4306C61.7831 14.7096 61.7406 14.9809 61.9448 15.2977C62.3819 15.9743 62.4586 16.7138 62.4064 17.4935C62.3406 18.4827 62.1741 19.463 62.1573 20.4576C62.1495 20.9349 61.8819 21.3037 61.5208 21.5936C61.2406 21.8193 60.9538 21.7804 60.6999 21.5343C60.4567 21.2989 60.213 21.0636 59.9543 20.8462C59.616 20.5624 59.4292 20.2253 59.4957 19.8827Z" fill="#111111"></path>
<path d="M95.8436 5.34346C95.6023 5.39736 95.3592 5.29256 95.1071 5.18778C94.3286 4.86202 93.561 5.03747 92.8155 5.30455C92.3334 5.47761 91.8502 5.65185 91.416 5.95186C91.1023 6.16802 90.743 6.06563 90.4711 5.78777C90.1663 5.4758 89.8292 5.19796 89.6328 4.78956C89.4945 4.50333 89.4561 4.24885 89.6717 3.98956C89.8813 3.73805 90.0807 3.47757 90.2927 3.22787C90.5094 2.97398 90.7987 2.98475 91.0603 3.10212C91.8526 3.45721 92.6843 3.32668 93.5023 3.31829C93.8885 3.31471 94.1526 2.97696 94.1382 2.58474C94.1298 2.34822 94.0885 2.1117 94.049 1.87697C93.9753 1.43683 94.0927 1.17275 94.5304 1.1015C95.1382 1.00269 95.6945 0.788316 96.2514 0.545214C96.5556 0.412872 96.7664 0.533829 96.8772 0.845806C97.0173 1.24042 97.0257 1.62127 96.8047 1.99493C96.7347 2.11289 96.688 2.24881 96.655 2.38296C96.5556 2.79075 96.7257 2.93325 97.1317 2.85961C97.8934 2.72248 98.6598 2.6123 99.4263 2.5075C99.588 2.48595 99.7377 2.44882 99.8832 2.38594C100.315 2.20031 100.739 2.24462 101.157 2.42846C101.394 2.53266 101.413 2.64642 101.225 2.81829C100.581 3.40692 99.7964 3.69794 98.9688 3.9171C98.3161 4.08955 97.6586 4.23447 96.9964 4.36021C96.743 4.40813 96.6454 4.60514 96.5754 4.81831C96.4077 5.3303 96.4101 5.33149 95.8436 5.34346Z" fill="#111111"></path>
<path d="M42.3929 22.2932C42.1761 22.2638 41.9372 22.2129 41.7366 22.0417C41.1174 21.5117 40.4258 21.0794 39.7665 20.6039C39.5563 20.4524 39.4659 20.3482 39.5719 20.0776C39.845 19.3782 40.2988 19.1818 40.9623 19.5267C41.427 19.7686 41.8875 20.0183 42.3474 20.2692C42.518 20.3626 42.6995 20.347 42.8707 20.3105C43.0923 20.2632 43.3282 20.2135 43.5186 20.0991C44.0474 19.78 44.6384 19.5967 45.1755 19.2967C45.6809 19.0147 46.2061 19.074 46.7127 19.3225C46.8109 19.3704 46.9037 19.4201 47.0133 19.4428C47.2977 19.5033 47.3827 19.7327 47.4109 19.9764C47.4379 20.2105 47.2983 20.365 47.0911 20.4644C46.0971 20.9434 45.066 21.3303 44.0318 21.7117C43.7917 21.8003 43.5623 21.9261 43.3408 22.0566C43.0551 22.2249 42.7456 22.274 42.3929 22.2932Z" fill="#111111"></path>
<path d="M69.5017 15.0933C69.0316 15.1041 68.6345 14.9891 68.3441 14.6244C68.0262 14.2256 68.0369 13.7065 68.3579 13.3089C68.7549 12.8166 69.3016 12.5316 69.8328 12.2214C70.0034 12.1214 70.1639 12.0016 70.322 11.8813C70.6783 11.6094 70.8346 11.2717 70.6999 10.8148C70.625 10.5633 70.7489 10.3938 70.9849 10.416C71.4579 10.4603 71.934 10.4789 72.3993 10.601C72.752 10.6932 72.9628 10.9046 73.0077 11.2507C73.0717 11.7495 73.2801 12.2687 72.8807 12.731C72.8088 12.8142 72.7394 12.9065 72.6304 12.9376C72.0208 13.1136 71.5933 13.513 71.2106 14.0041C70.8741 14.4358 70.4394 14.7729 69.9459 15.028C69.7921 15.1077 69.6358 15.0783 69.5017 15.0933Z" fill="#111111"></path>
<path d="M34.7036 19.9219C35.2329 20.05 35.7622 20.1776 36.291 20.3075C36.3485 20.3213 36.4012 20.3542 36.4581 20.3698C36.9503 20.5057 37.0419 20.7327 36.7707 21.1638C36.6174 21.4075 36.4659 21.6542 36.2934 21.8848C35.9694 22.3159 35.4928 22.2956 35.2042 21.8506C34.9922 21.5225 34.7497 21.2303 34.3886 21.0458C34.1347 20.9165 34.0006 20.3872 34.1353 20.1411C34.2634 19.9057 34.4922 19.9722 34.7036 19.9219Z" fill="#111111"></path>
<path d="M31.0952 21.7906C30.6389 21.814 30.3706 21.52 30.0496 21.3565C29.7346 21.196 29.5143 20.917 29.3251 20.6242C29.1604 20.3691 29.2203 20.1739 29.5023 20.0942C30.1005 19.9247 30.7017 19.9798 31.2874 20.1607C31.5281 20.2349 31.7514 20.8613 31.6981 21.2116C31.6275 21.6793 31.5005 21.7912 31.0952 21.7906Z" fill="#111111"></path>
<path d="M117.877 8.72758C117.933 8.34675 117.984 7.97667 117.999 7.59823C118.013 7.20302 117.769 6.69044 117.321 6.67606C117.059 6.67009 116.778 6.75331 116.602 7.21919C116.497 7.50242 116.55 7.75452 116.596 8.01141C116.596 8.01141 116.737 8.77429 116.851 9.35455C116.851 9.35455 117.06 10.2725 117.075 10.3456C117.097 10.4635 117.054 10.4965 117.005 10.5102C116.95 10.5288 116.886 10.4821 116.853 10.4007C116.804 10.2743 116.45 9.41144 116.336 9.14675C116.092 8.58687 115.902 8.10302 115.644 7.60841C115.56 7.44553 115.416 7.32697 115.241 7.27668C115.051 7.22158 114.862 7.26411 114.685 7.36769C114.463 7.49643 114.402 7.70362 114.37 7.90542C114.329 8.15393 114.457 8.33057 114.559 8.51202C114.559 8.51202 115.192 9.40305 115.709 9.95515C115.831 10.0851 116.254 10.5779 116.314 10.6468C116.366 10.7061 116.35 10.7647 116.327 10.7977C116.304 10.83 116.23 10.836 116.181 10.8019C116.077 10.7324 115.42 10.1887 115.259 10.0629C114.889 9.76952 114.536 9.44795 114.152 9.15933C113.887 8.95752 113.576 8.85813 113.336 8.94555C113.102 9.03058 112.955 9.20364 112.924 9.42759C112.88 9.75156 113.063 10.0282 113.309 10.2072C113.572 10.3965 113.871 10.5126 114.158 10.6246C114.286 10.6731 114.414 10.7222 114.539 10.7791C114.82 10.9055 115.648 11.1983 115.748 11.2288C115.827 11.2516 115.994 11.3246 115.965 11.4348C115.939 11.5426 115.691 11.4773 115.587 11.463C115.029 11.3797 114.378 11.2288 113.82 11.1396C113.82 11.1396 113.484 11.0845 113.334 11.0498C113.08 10.9911 112.778 11.054 112.546 11.2516C112.348 11.418 112.267 11.6037 112.28 11.8091C112.3 12.1815 112.447 12.3546 112.892 12.5157C113.014 12.56 113.209 12.5725 113.346 12.5522C113.814 12.4809 114.156 12.3767 114.545 12.2977C114.814 12.2426 115.566 12.0348 115.76 11.9965C115.845 11.9779 115.908 11.9821 115.933 12.0576C115.957 12.1324 115.904 12.1815 115.85 12.2061C115.485 12.3666 115.057 12.5642 114.699 12.7186C114.36 12.8654 113.989 13.0199 113.637 13.1809C113.375 13.3007 113.024 13.4803 112.912 13.942C112.857 14.166 113.014 14.4792 113.185 14.5995C113.415 14.7624 113.653 14.7301 113.896 14.5504C114.315 14.239 115.206 13.5145 115.697 13.1157C115.835 13.0019 115.969 12.8839 116.098 12.7678C116.192 12.6845 116.259 12.7049 116.295 12.7414C116.326 12.772 116.346 12.8246 116.269 12.9181L116.036 13.1971C115.689 13.6163 115.326 14.0498 114.986 14.4875C114.826 14.6971 114.665 14.9091 114.502 15.2367C114.315 15.6151 114.408 15.9714 114.754 16.1894C114.909 16.287 115.088 16.2726 115.288 16.1462C115.444 16.0486 115.642 15.8959 115.742 15.6882C115.949 15.257 116.141 14.8091 116.326 14.3756C116.409 14.1803 116.493 13.9851 116.578 13.7893C116.618 13.7001 116.659 13.6127 116.702 13.5247C116.753 13.4193 116.792 13.3073 116.839 13.1995C116.871 13.1282 116.918 13.1079 116.987 13.1282C117.056 13.1486 117.069 13.2091 117.059 13.2522C117.009 13.4881 116.909 13.9175 116.849 14.2414C116.798 14.5037 116.747 14.7666 116.698 15.0289L116.686 15.1001C116.647 15.3055 116.607 15.5175 116.602 15.7289C116.59 16.1648 116.702 16.4474 117.009 16.6349C117.142 16.7163 117.299 16.7367 117.453 16.6959C117.612 16.651 117.696 16.5331 117.771 16.3887C117.95 16.0385 117.923 15.6762 117.881 15.3343C117.854 15.1265 117.827 14.9169 117.799 14.7073C117.799 14.7073 117.618 13.3276 117.608 13.2504C117.6 13.1893 117.642 13.0995 117.724 13.0911C117.797 13.0851 117.866 13.124 117.881 13.1971C117.948 13.5474 118.039 13.8809 118.107 14.2312C118.33 15.3732 118.739 16.1648 119.395 16.1869C119.663 16.1953 119.836 16.5247 119.845 16.6918C119.849 16.8061 119.741 16.8037 119.613 16.8139C119.497 16.8241 119.368 16.7995 119.186 16.7349C119.157 16.7247 119.12 16.7103 119.081 16.6941H119.08C118.856 16.6295 118.52 16.3624 118.52 16.3624C118.52 16.3624 118.442 16.6073 118.151 16.887L118.147 16.8912C117.917 17.0906 117.699 17.2091 117.423 17.2534C117.172 17.29 116.938 17.2397 116.723 17.0972C116.584 17.0055 116.438 16.9073 116.326 16.7774C116.244 16.6816 116.043 16.3624 116.043 16.3624C116.043 16.3624 115.664 16.6061 115.53 16.6738C115.115 16.881 114.705 16.8121 114.353 16.5738C114.071 16.3828 113.902 16.1115 113.865 15.7881C113.838 15.5498 113.924 15.157 113.924 15.157C113.924 15.157 113.458 15.1959 113.403 15.1839H113.397C113.096 15.1636 112.845 15.0312 112.627 14.7768C112.448 14.569 112.355 14.3348 112.342 14.0642C112.335 13.9037 112.367 13.7384 112.438 13.5594L112.44 13.5534C112.44 13.5534 112.572 13.2869 112.766 13.0528C112.473 12.9959 112.285 12.8552 112.285 12.8552C111.96 12.621 111.777 12.2875 111.742 11.9396C111.675 11.2923 112.082 10.9234 112.236 10.7689C112.412 10.5941 112.717 10.5066 112.717 10.5066C112.717 10.5066 112.503 10.2438 112.406 10.0342C112.406 10.0324 112.402 10.0282 112.402 10.0282C112.212 9.58629 112.373 8.98208 112.687 8.6725C112.911 8.45274 113.215 8.37968 113.417 8.36111C113.578 8.34675 113.826 8.40782 113.826 8.40782C113.826 8.40782 113.774 8.20243 113.786 8.02759C113.816 7.58387 114.05 7.13416 114.424 6.92218C114.791 6.7168 115.243 6.65152 115.774 6.94853C115.774 6.94853 115.945 7.04014 116.059 7.20123C116.059 7.18865 116.061 6.98506 116.193 6.7653C116.405 6.4539 116.784 6.14253 117.401 6.14852C117.919 6.1551 118.194 6.48086 118.441 6.81259C118.441 6.81259 118.55 6.96289 118.563 7.14195C118.563 7.14195 118.713 7.00182 118.941 6.88744C119.212 6.75331 119.483 6.68624 119.859 6.75751C119.936 6.77187 120.008 6.83055 119.948 6.94075C119.896 7.03595 119.865 7.07667 119.798 7.19523C119.735 7.30482 119.657 7.3641 119.513 7.39225C119.244 7.44553 119.025 7.60423 118.844 7.87249C118.436 8.47728 118.219 9.16711 118.007 9.8348L117.821 10.427C117.807 10.4701 117.785 10.5007 117.756 10.5144C117.722 10.5306 117.681 10.5246 117.651 10.5168C117.604 10.5007 117.563 10.4737 117.59 10.3438C117.689 9.81862 117.804 9.218 117.877 8.72758Z" fill="#111111"></path>
<path d="M117.375 12.8891C116.836 12.9185 116.249 12.5041 116.151 11.9334C116.097 11.6191 116.197 11.3328 116.383 11.0759C116.561 10.8298 116.818 10.7394 117.106 10.7286C117.281 10.7214 117.498 10.734 117.603 10.749C117.691 10.7622 117.723 10.8346 117.69 10.9053C117.624 11.0466 117.542 11.0825 117.398 11.0622C117.146 11.0275 116.906 11.0772 116.738 11.2993C116.451 11.6808 116.49 12.2209 116.998 12.4305C117.155 12.4951 117.33 12.5215 117.499 12.552C117.627 12.5748 117.686 12.6041 117.683 12.7346C117.679 12.8886 117.469 12.8838 117.375 12.8891Z" fill="#111111"></path>
<path d="M124.292 10.7046C124.255 10.7046 124.207 10.6842 124.136 10.6375C124.103 10.616 124.064 10.6052 124.027 10.5956C123.945 10.574 123.916 10.5603 123.915 10.4986C123.912 10.3998 123.922 10.2956 123.944 10.1986C123.982 10.0327 124.024 9.86684 124.066 9.70157C124.103 9.55487 124.227 9.03988 124.227 9.03988C124.348 8.52431 124.469 8.00814 124.579 7.49017C124.63 7.25184 124.746 7.04225 124.916 6.88358C125.086 6.72549 125.3 6.62728 125.533 6.60093C125.576 6.59614 125.619 6.59375 125.661 6.59375C126.164 6.59375 126.595 6.94286 126.71 7.44286C126.803 7.85186 126.688 8.20336 126.349 8.54946C125.952 8.95427 125.564 9.36624 125.178 9.78061C125.024 9.94648 124.874 10.1171 124.724 10.2878L124.445 10.6028C124.38 10.6752 124.336 10.7046 124.292 10.7046Z" fill="#111111"></path>
<path d="M123.166 10.497C123.093 10.497 123.046 10.473 123.018 10.3503C122.937 9.99877 122.848 9.64906 122.76 9.29934C122.705 9.08199 122.65 8.86462 122.597 8.64664C122.499 8.24366 122.406 7.84006 122.312 7.43585L122.271 7.25682C122.263 7.22389 122.263 7.18915 122.261 7.15083L122.258 7.08615C122.282 6.57477 122.502 6.23883 122.948 6.03345C123.03 5.99632 123.139 5.97656 123.264 5.97656C123.401 5.97656 123.554 6.0011 123.673 6.04123C123.837 6.09631 123.944 6.18615 124.086 6.33165C124.195 6.44303 124.281 6.58255 124.339 6.74543C124.411 6.94124 124.361 7.15322 124.314 7.35802C124.147 8.07358 123.979 8.78798 123.81 9.50175C123.742 9.78558 123.675 10.0694 123.594 10.3491C123.584 10.3862 123.529 10.4365 123.48 10.4551C123.433 10.473 123.378 10.4772 123.32 10.482C123.288 10.4844 123.184 10.497 123.166 10.497Z" fill="#111111"></path>
<path d="M124.728 11.2824C124.646 11.1782 124.563 11.074 124.5 10.9591C124.499 10.9441 124.536 10.8693 124.581 10.8207C124.843 10.5453 125.105 10.2716 125.368 9.99798L126.035 9.30097C126.114 9.21773 126.19 9.13031 126.266 9.04348C126.368 8.92611 126.47 8.80875 126.581 8.70037C126.802 8.4866 127.029 8.26562 127.38 8.26562C127.759 8.27761 128.095 8.53091 128.272 8.77701C128.387 8.93689 128.464 9.15666 128.482 9.38061C128.506 9.69079 128.415 9.96087 128.232 10.1207C128.098 10.2393 127.926 10.3357 127.749 10.3932C127.311 10.5345 126.871 10.6669 126.431 10.7992C126.164 10.8794 125.898 10.9597 125.631 11.0417C125.444 11.1004 125.258 11.1645 125.072 11.2291L124.769 11.3345L124.728 11.2824Z" fill="#111111"></path>
<path d="M128.008 12.957C127.883 12.957 127.745 12.9157 127.611 12.8762L127.528 12.8522C126.922 12.6804 126.318 12.5007 125.715 12.3205L125.304 12.1977C125.166 12.1564 125.03 12.1097 124.894 12.0636L124.84 12.045C124.751 12.0145 124.725 11.9654 124.731 11.8354C124.74 11.648 124.826 11.5905 125.003 11.5396C125.387 11.4283 125.768 11.3103 126.15 11.1929C126.654 11.0372 127.157 10.8821 127.666 10.7414C127.775 10.7109 127.884 10.6953 127.989 10.6953C128.474 10.6953 128.846 11.0235 128.959 11.5516C128.974 11.6199 128.982 11.6887 128.989 11.7588L129.001 11.8588C128.997 12.3959 128.666 12.8043 128.114 12.9444C128.082 12.9528 128.046 12.957 128.008 12.957Z" fill="#111111"></path>
<path d="M125.59 16.8468C125.503 16.8426 125.457 16.8372 125.411 16.8294C125.278 16.8061 125.149 16.7582 125.018 16.7073C124.742 16.5995 124.666 16.3306 124.593 16.0713C124.424 15.4761 124.282 14.915 124.139 14.3546L124.102 14.2084C124.012 13.8581 123.929 13.506 123.851 13.1533C123.828 13.0474 123.861 12.9773 123.954 12.9402C124.007 12.9192 124.057 12.8947 124.108 12.8689C124.14 12.8521 124.169 12.8438 124.195 12.8438C124.233 12.8438 124.269 12.8617 124.307 12.9006C124.466 13.0623 124.626 13.2222 124.786 13.3821C124.953 13.5485 125.12 13.715 125.285 13.8833L125.472 14.0719C125.755 14.3582 126.037 14.6438 126.309 14.9408C126.517 15.1677 126.726 15.4522 126.707 15.8348C126.692 16.1432 126.608 16.369 126.449 16.5258C126.242 16.73 125.939 16.8474 125.62 16.8474L125.59 16.8468Z" fill="#111111"></path>
<path d="M118.826 12.8751C118.588 12.8547 118.367 12.7506 118.151 12.5566C117.884 12.3164 117.764 12.0416 117.783 11.717C117.801 11.4158 117.922 11.1805 118.151 10.9985C118.374 10.8212 118.614 10.7344 118.883 10.7344C119.034 10.7344 119.192 10.7613 119.366 10.8164C119.536 10.8703 119.709 10.9158 119.882 10.9619C120.025 11.0002 120.168 11.038 120.31 11.0805C120.442 11.1206 120.573 11.1655 120.703 11.211C120.825 11.2529 120.947 11.2955 121.07 11.3332C121.377 11.4272 121.686 11.5134 121.995 11.5978C122.141 11.638 122.188 11.7074 122.167 11.8535C122.135 12.0703 122.116 12.0895 121.882 12.141C121.585 12.2068 121.289 12.2841 120.995 12.3613L120.253 12.5572C119.898 12.6506 119.544 12.7446 119.189 12.835C119.115 12.8542 119.037 12.8596 118.953 12.8649L118.826 12.8751Z" fill="#111111"></path>
<path d="M123.278 17.4434C122.75 17.4152 122.418 17.2176 122.235 16.823C122.129 16.5973 122.176 16.3721 122.238 16.1422C122.413 15.5021 122.591 14.8631 122.769 14.2242C122.877 13.8356 122.978 13.4787 123.087 13.1236C123.092 13.1044 123.133 13.0715 123.168 13.0625L123.227 13.0691C123.248 13.0739 123.273 13.0793 123.297 13.0793C123.35 13.0721 123.371 13.0697 123.39 13.0697C123.514 13.0697 123.542 13.1595 123.578 13.3056C123.738 13.9272 123.881 14.5098 124.021 15.0931L124.071 15.302C124.152 15.6314 124.235 15.9715 124.293 16.3092C124.337 16.5625 124.258 16.8254 124.07 17.0494C123.868 17.2889 123.569 17.444 123.31 17.444L123.278 17.4434Z" fill="#111111"></path>
<path d="M122.561 10.8476C122.519 10.8476 122.466 10.8291 122.442 10.8063C122.338 10.7069 122.24 10.6015 122.142 10.4956C122.079 10.4279 122.016 10.3602 121.951 10.2944C121.868 10.2075 121.781 10.1225 121.695 10.0375C121.579 9.9231 121.463 9.80872 121.351 9.69016C120.947 9.26141 120.545 8.82966 120.147 8.39493C119.767 7.98055 119.899 7.45241 120.184 7.13743C120.405 6.89131 120.647 6.76497 120.922 6.75179L121.013 6.75C121.273 6.75 121.45 6.80689 121.664 6.95898C121.827 7.07515 121.896 7.25539 121.947 7.43384C122.027 7.72007 122.105 8.0069 122.181 8.29434L122.377 9.02249C122.456 9.32008 122.536 9.6177 122.621 9.9135C122.666 10.0716 122.728 10.2255 122.789 10.3782C122.837 10.4979 122.844 10.5973 122.809 10.674C122.775 10.7476 122.7 10.8045 122.587 10.8441L122.561 10.8476Z" fill="#111111"></path>
<path d="M127.409 15.3424C127.368 15.3424 127.328 15.3401 127.286 15.3353C127.146 15.3185 127.001 15.2544 126.915 15.173C126.712 14.9796 126.522 14.7718 126.332 14.564L125.966 14.1652C125.88 14.0712 125.794 13.9778 125.706 13.8856L125.324 13.4897C125.152 13.3131 124.98 13.1364 124.812 12.958C124.757 12.9005 124.709 12.8388 124.659 12.7766C124.615 12.7215 124.571 12.6664 124.523 12.6143C124.458 12.5424 124.487 12.4975 124.547 12.4173C124.564 12.3969 124.579 12.376 124.591 12.3556C124.603 12.3358 124.667 12.3047 124.72 12.3047C124.938 12.364 125.134 12.4298 125.33 12.4963L125.641 12.6005C125.824 12.6592 126.519 12.8772 126.519 12.8772C126.681 12.9281 126.846 12.9688 127.011 13.0089C127.334 13.0879 127.667 13.17 127.963 13.3346C128.298 13.5221 128.494 13.8083 128.512 14.1406C128.476 14.7287 128.053 15.3424 127.409 15.3424Z" fill="#111111"></path>
<path d="M119.301 15.091C118.801 15.0617 118.475 14.7964 118.359 14.3234C118.3 14.0796 118.262 13.818 118.392 13.5563C118.502 13.3335 118.686 13.1874 118.938 13.1216C119.318 13.0233 119.701 12.9347 120.083 12.8461C120.31 12.7934 120.535 12.7413 120.76 12.6868C121.015 12.6251 121.268 12.5611 121.523 12.4964L122.001 12.3754C122.109 12.3479 122.174 12.3311 122.238 12.3281C122.327 12.3281 122.383 12.3748 122.419 12.47C122.456 12.5659 122.461 12.618 122.402 12.673C122.129 12.9209 121.255 13.7263 121.255 13.7263L120.72 14.2234C120.546 14.3874 120.371 14.5509 120.201 14.721C119.972 14.9515 119.677 15.0725 119.301 15.091Z" fill="#111111"></path>
<path d="M120.916 16.7549C120.638 16.7549 120.404 16.6519 120.257 16.4651C120.195 16.3879 120.144 16.304 120.101 16.2154C119.986 15.9729 119.955 15.7124 120.012 15.4819C120.059 15.2933 120.164 15.1148 120.315 14.9657L120.656 14.625C120.812 14.4675 120.969 14.3094 121.128 14.1549C121.577 13.7214 122.028 13.292 122.482 12.8645C122.543 12.8076 122.592 12.7812 122.638 12.7812C122.661 12.7812 122.684 12.7878 122.707 12.801C122.9 12.9094 122.914 12.9609 122.867 13.1076L122.187 15.2448L122.1 15.5238C122.055 15.6717 122.009 15.8196 121.96 15.9663C121.898 16.1519 121.809 16.3088 121.697 16.4322C121.516 16.631 121.217 16.7549 120.916 16.7549Z" fill="#111111"></path>
<path d="M122.174 11.3324C122.14 11.3324 122.102 11.3222 122.065 11.3121C121.811 11.2438 121.568 11.1713 121.324 11.0989C121.165 11.051 120.374 10.821 120.374 10.821C119.943 10.6947 119.497 10.5635 119.053 10.4588C118.624 10.3576 118.402 10.0522 118.393 9.55216C118.375 9.34677 118.468 9.10844 118.647 8.89346C118.725 8.80006 118.811 8.7234 118.902 8.66593C119.01 8.59826 119.149 8.55274 119.326 8.52641C119.375 8.51921 119.423 8.51562 119.471 8.51562C119.587 8.51562 119.694 8.53838 119.789 8.58329C119.874 8.62341 119.962 8.68987 120.06 8.78568C120.347 9.07072 121.518 10.2414 121.819 10.5396C121.933 10.6528 122.057 10.7558 122.181 10.8588L122.259 10.9228C122.343 10.9941 122.368 11.0612 122.283 11.2522C122.251 11.3228 122.21 11.3324 122.174 11.3324Z" fill="#111111"></path>
<path d="M123.437 10.7227C124.111 10.7072 124.44 11.1395 124.462 11.7413C124.483 12.3012 124.025 12.7569 123.482 12.7671C122.861 12.7797 122.423 12.2503 122.442 11.6018C122.453 11.218 122.93 10.6665 123.437 10.7227Z" fill="#111111"></path>
</svg>
</div>
<p class="js-f-info-i font-abc text-more-14 mt-[1em] lg:text-more-14 lg:mt-[2.4em]" style="filter: blur(4px); opacity: 0; visibility: hidden;">箱根</p>
<p class="text-more-13 mt-[1.4em] js-f-info-i lg:text-more-11 lg:mt-[1.4em]" style="filter: blur(4px); opacity: 0; visibility: hidden;">〒 250-0408<br/>神奈川県足柄下郡箱根町強羅1300<br/>0460-82-3331</p>
<div class="js-f-info-i grid grid-cols-5 gap-x-[4px] mt-[32px] lg:mt-more-60" style="filter: blur(4px); opacity: 0; visibility: hidden;">
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer is-active" data-id="0">
<img alt="gora-hakone-01" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3h1200004354s97985.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="1">
<img alt="gora-hakone-02" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb2m1200004354sn899D.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="2">
<img alt="gora-hakone-03" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4d1200004354sb2EE2.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="3">
<img alt="gora-hakone-04" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb591200004354sdA5D5.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="4">
<img alt="gora-hakone-05" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb651200004354sf6AAC.webp"/>
</div>
</div>
<a class="js-f-info-i link-underLine mt-[2em] -short lg:mt-more-60" href="https://www.gorakadan.com/hakone/" rel="noopener noreferrer" style="filter: blur(4px); opacity: 0; visibility: hidden;" target="_blank">
<span class="link-underLine__txt lg:text-more-14">詳細を見る</span>
<span class="link-underLine__line -light"></span>
</a>
</div>
</div>
<div class="js-w-hotel grid grid-cols-1 gap-y-[32px] lg:absolute lg:top-0 lg:left-0 lg:w-full lg:h-full lg:grid-cols-[30%_40%_30%] lg:items-center" id="fuji">
<div class="lg:grid lg:col-start-2 lg:max-h-[calc(100vh-var(--s-more-20))]">
<div class="js-hotel-slider mask -vol-02 swiper aspect-[2/3] lg:w-full lg:h-full swiper-fade swiper-initialized swiper-horizontal swiper-watch-progress swiper-backface-hidden">
<div aria-live="off" class="swiper-wrapper" id="swiper-wrapper-2605721413bafc15" style="translate: none; rotate: none; scale: none; filter: blur(5px); transform: scale(1.3, 1.3);">
<div aria-label="1 / 5" class="swiper-slide swiper-slide-visible swiper-slide-fully-visible swiper-slide-active" role="group" style="width: 720px; opacity: 1; transform: translate3d(0px, 0px, 0px);">
<img alt="gora-hakone-01" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb711200004354shC7DA.webp"/>
</div>
<div aria-label="2 / 5" class="swiper-slide swiper-slide-next" role="group" style="width: 720px; opacity: 0; transform: translate3d(-720px, 0px, 0px);">
<img alt="gora-hakone-02" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3i1200004354sp51A5.webp"/>
</div>
<div aria-label="3 / 5" class="swiper-slide" role="group" style="width: 720px; opacity: 0; transform: translate3d(-1440px, 0px, 0px);">
<img alt="gora-hakone-03" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb0u1200004354sj3AC8.webp"/>
</div>
<div aria-label="4 / 5" class="swiper-slide" role="group" style="width: 720px; opacity: 0; transform: translate3d(-2160px, 0px, 0px);">
<img alt="gora-hakone-04" class="w-full h-full object-cover" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4e1200004354srBF01.webp"/>
</div>
<div aria-label="5 / 5" class="swiper-slide" role="group" style="width: 720px; opacity: 0; transform: translate3d(-2880px, 0px, 0px);">
<img alt="gora-hakone-05" class="w-full h-full object-cover" src="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/fuji-05.webp"/>
</div>
</div>
<span aria-atomic="true" aria-live="assertive" class="swiper-notification"></span></div>
</div>
<div class="js-f-info lg:w-[max(18vw,226px)] lg:my-0 lg:mx-auto">
<div class="js-f-info-i w-auto h-[23px] lg:w-full lg:h-auto" style="filter: blur(4px); opacity: 0; visibility: hidden;">
<svg class="w-auto h-full lg:w-full lg:h-auto" fill="none" height="28" viewbox="0 0 244 28" width="244" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_7202_14065)">
<path d="M32.846 8.39062C26.6628 8.39062 22 12.6408 22 18.2772C22 23.9285 26.6628 28.1902 32.846 28.1902C39.0292 28.1902 43.6926 23.9285 43.6926 18.2772C43.6926 12.6408 39.0292 8.39062 32.846 8.39062ZM40.8627 18.2509C40.8627 23.5513 37.5663 27.2525 32.846 27.2525C28.1257 27.2525 24.8288 23.5513 24.8288 18.2509C24.8288 12.9971 28.1257 9.32774 32.846 9.32774C37.5663 9.32774 40.8627 12.9971 40.8627 18.2509Z" fill="#352F2D"></path>
<path d="M60.8476 24.6447L56.9741 18.4966C60.2298 17.6743 62.0925 15.901 62.0925 13.6123C62.0925 8.9383 57.3723 8.54688 52.5381 8.54688H45.8672L46.3418 8.80874C47.3334 9.35498 47.4153 10.2586 47.4153 11.2149V25.3353C47.4153 26.4931 47.1175 27.2392 46.4492 27.7536L46.1248 28.0039H51.2275L50.9032 27.7536C50.2354 27.2392 49.9376 26.4931 49.9376 25.3353V19.0209H52.4459C53.2883 19.0209 54.0016 18.9891 54.6221 18.9237L60.3535 28.0039H63.8668L63.4074 27.7431C62.4332 27.1898 60.8639 24.6705 60.8476 24.6447ZM59.3239 13.586C59.3239 14.7191 58.96 15.66 58.2418 16.3825C57.1168 17.5139 55.1217 18.1101 52.4659 18.1101C52.4285 18.1101 52.3905 18.1101 52.3531 18.1096H49.9376V9.48399H52.2598C56.3964 9.51034 59.3239 9.82051 59.3239 13.586Z" fill="#352F2D"></path>
<path d="M84.3094 25.6504L77.2311 8.63252L77.1953 8.54688H74.5813L67.4993 25.6241C67.0366 26.7352 66.7713 27.2891 65.8107 27.7382L65.2422 28.0039H69.4987L69.0203 27.742C68.4187 27.4126 68.1507 26.9669 68.6764 25.758L70.0872 22.3664H80.336L81.7474 25.7844V25.7849C82.1949 26.8587 82.113 27.3709 81.4339 27.742L80.9555 28.0039H86.3934L85.9828 27.7459C85.0232 27.1447 84.5985 26.3493 84.3094 25.6504ZM79.9748 21.4287H70.4794L75.2274 9.91659L79.9748 21.4287Z" fill="#352F2D"></path>
<path d="M178.368 25.6504L171.29 8.63252L171.254 8.54688H168.64L161.557 25.6241C161.095 26.7352 160.829 27.2897 159.869 27.7382L159.3 28.0039H163.556L163.078 27.742C162.477 27.4126 162.21 26.9663 162.735 25.758L164.145 22.3664H174.395L175.806 25.7844V25.7849C176.253 26.8582 176.171 27.3704 175.493 27.742L175.015 28.0039H180.452L180.041 27.7459C179.081 27.1442 178.657 26.3487 178.368 25.6504ZM174.033 21.4287H164.538L169.286 9.91659L174.033 21.4287Z" fill="#352F2D"></path>
<path d="M136.617 25.6504L129.54 8.63252L129.504 8.54688H126.89L119.808 25.6241C119.345 26.7352 119.079 27.2897 118.119 27.7382L117.551 28.0039H121.806L121.328 27.742C120.727 27.4126 120.46 26.9663 120.985 25.758L122.395 22.3664H132.645L134.055 25.7844V25.7849C134.504 26.8582 134.422 27.3704 133.742 27.742L133.265 28.0039H138.701L138.291 27.7459C137.331 27.1442 136.907 26.3487 136.617 25.6504ZM132.283 21.4287H122.788L127.535 9.91659L132.283 21.4287Z" fill="#352F2D"></path>
<path d="M112.9 24.5371L106.185 16.3633L114.28 8.54688H110.801L111.145 8.79886C111.398 8.98441 111.555 9.21993 111.566 9.42909C111.574 9.58665 111.502 9.73597 111.349 9.87596L101.181 19.689V11.2149C101.181 10.073 101.488 9.32753 102.177 8.79721L102.502 8.54688H97.3369L97.6624 8.79721C98.3301 9.31106 98.6279 10.0571 98.6279 11.2149V25.3353C98.6279 26.4931 98.3301 27.2392 97.6618 27.7536L97.3375 28.0039H102.501L102.177 27.7536C101.488 27.2227 101.181 26.4766 101.181 25.3353V21.2064L104.509 17.9789L112.692 27.9534L112.733 28.0039H116.648L116.145 27.7409C114.84 27.0591 112.923 24.5656 112.9 24.5371Z" fill="#352F2D"></path>
<path d="M148.402 8.54688H140.419L140.744 8.79721C141.211 9.15679 141.833 9.79855 141.833 11.2149V25.3353C141.833 26.7511 141.211 27.3934 140.744 27.7536L140.419 28.0039H148.402C154.74 28.0039 159.34 23.9458 159.34 18.3544C159.34 12.5802 154.842 8.54688 148.402 8.54688ZM156.511 18.3281C156.511 23.4737 153.164 27.0668 148.372 27.0668H144.385V9.48399H148.372C153.316 9.48399 156.511 12.9552 156.511 18.3281Z" fill="#352F2D"></path>
<path d="M197.432 8.54688L197.757 8.79721C198.446 9.32808 198.753 10.0736 198.753 11.2149V24.34L184.777 8.59354L184.736 8.54688H182.292L182.617 8.79721C183.285 9.31106 183.582 10.0566 183.582 11.2149V25.3353C183.582 26.4931 183.285 27.2392 182.617 27.7536L182.293 28.0039H186.134L185.809 27.7536C185.141 27.2392 184.843 26.4931 184.843 25.3353V12.0016L198.912 27.9572L198.952 28.0039H199.983V11.2149C199.983 10.0566 200.281 9.31106 200.949 8.79721L201.273 8.54688H197.432Z" fill="#352F2D"></path>
<path d="M15.198 18.6538C16.0219 19.2495 16.0149 20.7268 16.004 22.9628C16.0029 23.2334 16.0013 23.5156 16.0013 23.8093C16.0013 25.8812 13.8723 27.3849 10.9388 27.3849C6.23964 27.3849 2.82935 23.4992 2.82935 18.146C2.85972 12.7929 6.30419 9.19653 11.3998 9.19653C13.5392 9.19653 15.4746 10.2072 16.5774 11.9014L16.7097 12.1039L17.7339 10.0057L17.627 9.9393C15.9574 8.89734 13.8804 8.39063 11.2772 8.39063C4.74306 8.39062 0 12.5486 0 18.2772C0 23.9285 4.64976 28.1902 10.8151 28.1902C13.7703 28.1902 15.8062 27.3986 17.0357 26.6936L18.023 28.1847H18.4651V25.2394L18.467 25.0053L18.4922 22.1761C18.4922 20.7482 18.6956 19.0727 19.2662 18.6533L19.6096 18.4013H14.8481L15.198 18.6538Z" fill="#352F2D"></path>
<path d="M37.3937 5.44644L33.6385 0H32.0546L28.2993 5.44644L28.2744 5.51397V5.69732H28.9194L32.8465 1.38013L36.7737 5.69732H37.4186V5.51397L37.3937 5.44644Z" fill="#352F2D"></path>
<path d="M212.57 12.7208C212.862 12.9489 212.961 13.2962 212.961 14.0977V22.4663C212.961 23.2677 212.862 23.615 212.569 23.8437L212.217 24.1192H215.358L215.006 23.8437C214.704 23.6076 214.597 23.247 214.597 22.4663V18.2639H217.524C218.363 18.2639 218.811 18.3453 219.07 18.5452L219.314 18.733V17.0976L219.07 17.2854C218.811 17.4848 218.363 17.5662 217.524 17.5662H214.597V13.1431H218.027C218.906 13.1431 220.052 13.4324 220.291 13.8477L220.335 13.9238H220.628V12.4453H212.217L212.57 12.7208Z" fill="#352F2D"></path>
<path d="M231.441 12.7208C231.77 12.9771 231.904 13.3765 231.904 14.0977V19.7344C231.904 22.236 230.74 23.4523 228.347 23.4523C225.875 23.4523 224.772 22.2573 224.772 19.5785L224.79 14.0977C224.79 13.376 224.924 12.9771 225.252 12.7208L225.606 12.4453H222.32L222.673 12.7208C223.002 12.9771 223.136 13.376 223.136 14.0977V19.5626C223.136 22.7019 224.799 24.2282 228.221 24.2282C231.109 24.2282 232.765 22.5961 232.765 19.7498V14.0977C232.765 13.3765 232.899 12.9771 233.228 12.7208L233.581 12.4453H231.088L231.441 12.7208Z" fill="#352F2D"></path>
<path d="M235.932 12.713C236.234 12.9486 236.341 13.3086 236.341 14.0898V21.6937C236.341 22.8866 236.004 23.8205 235.502 24.0167L235.336 24.0821L235.548 24.4501L235.648 24.4379C237.106 24.2544 237.977 23.1647 237.977 21.522V14.0898C237.977 13.2884 238.076 12.9406 238.367 12.713L238.721 12.4375H235.579L235.932 12.713Z" fill="#352F2D"></path>
<path d="M243.635 12.713L243.988 12.4375H240.847L241.2 12.713C241.492 12.9411 241.591 13.2884 241.591 14.0898V22.4581C241.591 23.2595 241.492 23.6067 241.199 23.8354L240.847 24.1109H243.987L243.635 23.8354C243.333 23.5998 243.226 23.2393 243.226 22.4581V14.0898C243.226 13.3086 243.333 12.948 243.635 12.713Z" fill="#352F2D"></path>
</g>
<defs>
<clippath id="clip0_7202_14065">
<rect fill="white" height="28" width="244"></rect>
</clippath>
</defs>
</svg>
</div>
<p class="js-f-info-i font-abc text-more-14 mt-[1em] lg:text-more-14 lg:mt-[2.4em]" style="filter: blur(4px); opacity: 0; visibility: hidden;">富士</p>
<p class="text-more-13 mt-[1.4em] js-f-info-i lg:text-more-11 lg:mt-[1.4em]" style="filter: blur(4px); opacity: 0; visibility: hidden;">〒410-1431<br/>静岡県駿東郡小山町須走110-1<br/>0550-75-5551</p>
<div class="js-f-info-i grid grid-cols-5 gap-x-[4px] mt-[32px] lg:mt-more-60" style="filter: blur(4px); opacity: 0; visibility: hidden;">
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer is-active" data-id="0">
<img alt="gora-hakone-01" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb711200004354shC7DA.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="1">
<img alt="gora-hakone-02" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3i1200004354sp51A5.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="2">
<img alt="gora-hakone-03" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb0u1200004354sj3AC8.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="3">
<img alt="gora-hakone-04" class="w-full h-full object-cover pointer-events-none" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb4e1200004354srBF01.webp"/>
</div>
<div class="js-hotel-thumbnail-item aspect-[4/7] overflow-hidden cursor-pointer" data-id="4">
<img alt="gora-hakone-05" class="w-full h-full object-cover pointer-events-none" src="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/img/fuji-05.webp"/>
</div>
</div>
<a class="js-f-info-i link-underLine mt-[2em] -short lg:mt-more-60" href="https://www.gorakadan.com/fuji/" rel="noopener noreferrer" style="filter: blur(4px); opacity: 0; visibility: hidden;" target="_blank">
<span class="link-underLine__txt lg:text-more-14">詳細を見る</span>
<span class="link-underLine__line -light"></span>
</a>
</div>
</div>
</div>
<div class="line-color hidden grid-cols-1 grid-rows-[1fr_3fr] absolute top-[calc(var(--s-more-20)/2)] left-0 h-[10vh] w-[0.5vw] min-w-[6px] lg:grid" id="line-color">
<div class="line-color__top"></div>
<div class="line-color__bottom"></div>
</div>
</div>
</div></div>
<!-- NEWS -->
<div class="relative mt-[160px] bg-white01 z-20 lg:mt-0 lg:mx-more-60" data-name="news-area" style="opacity: 1; visibility: inherit;">
<h3 class="absolute top-[-2em] left-[8px] [writing-mode:vertical-lr] lg:top-0 lg:left-0 text-more-12 tracking-[0.4em] lg:text-more-14">お知らせ</h3>
<div class="lg:pl-[6%] lg:pt-more-30 grid grid-cols-1 w-sp-con mx-auto border-t lg:w-full">
<a class="line-link relative text-more-12 py-[2em] lg:grid lg:grid-cols-[1fr_3fr] lg:py-[1.6em] first:[border-top:1px_solid_var(--c-gray-02)]" href="https://prtimes.jp/main/html/rd/p/000000015.000079599.html" rel="noopener noreferrer" target="_blank">
<div class="line-link__line absolute left-0 bottom-0 w-full h-[1px] bg-gray02"></div>
<span class="grid grid-cols-[2fr_5fr] gap-x-[8px] items-center lg:grid-cols-[4fr_7fr]">
<time class="font-abc text-more-13 leading-none lg:text-more-16" datetime!="2025.07.18">2025.07.18</time>
<p class="text-more-13 whitespace-nowrap lg:text-more-14">GORA KADAN FUJI GOLF</p>
</span>
<p class="line-link__ttl text-more-14 mt-[0.6em] leading-relaxed line-clamp-2 lg:text-more-14 lg:mt-0">あらたなゴルフ場GORA KADAN FUJI GOLF今秋開業予定</p>
</a>
<a class="line-link relative text-more-12 py-[2em] lg:grid lg:grid-cols-[1fr_3fr] lg:py-[1.6em] first:[border-top:1px_solid_var(--c-gray-02)]" href="https://www.gorakadan.com/fuji/dining/takumi-fuji/" rel="noopener noreferrer" target="_blank">
<div class="line-link__line absolute left-0 bottom-0 w-full h-[1px] bg-gray02"></div>
<span class="grid grid-cols-[2fr_5fr] gap-x-[8px] items-center lg:grid-cols-[4fr_7fr]">
<time class="font-abc text-more-13 leading-none lg:text-more-16" datetime!="2025.07.18">2025.07.18</time>
<p class="text-more-13 whitespace-nowrap lg:text-more-14">GORA KADAN FUJI</p>
</span>
<p class="line-link__ttl text-more-14 mt-[0.6em] leading-relaxed line-clamp-2 lg:text-more-14 lg:mt-0">GORA KADAN FUJIに鮨の名店「すし匠」による「富士 匠」がオープン</p>
</a>
<a class="line-link relative text-more-12 py-[2em] lg:grid lg:grid-cols-[1fr_3fr] lg:py-[1.6em] first:[border-top:1px_solid_var(--c-gray-02)]" href="https://www.gorakadan.com/fuji/dining/fuji-kanda/" rel="noopener noreferrer" target="_blank">
<div class="line-link__line absolute left-0 bottom-0 w-full h-[1px] bg-gray02"></div>
<span class="grid grid-cols-[2fr_5fr] gap-x-[8px] items-center lg:grid-cols-[4fr_7fr]">
<time class="font-abc text-more-13 leading-none lg:text-more-16" datetime!="2025.07.18">2025.07.18</time>
<p class="text-more-13 whitespace-nowrap lg:text-more-14">GORA KADAN FUJI</p>
</span>
<p class="line-link__ttl text-more-14 mt-[0.6em] leading-relaxed line-clamp-2 lg:text-more-14 lg:mt-0">ミシュラン３ツ星１８年連続獲得の「日本料理かんだ」による鉄板焼きがGORA KADAN FUJIにオープン</p>
</a>
<a class="line-link relative text-more-12 py-[2em] lg:grid lg:grid-cols-[1fr_3fr] lg:py-[1.6em] first:[border-top:1px_solid_var(--c-gray-02)]" href="https://www.gorakadan.com/fuji/news_post/644/" rel="noopener noreferrer" target="_blank">
<div class="line-link__line absolute left-0 bottom-0 w-full h-[1px] bg-gray02"></div>
<span class="grid grid-cols-[2fr_5fr] gap-x-[8px] items-center lg:grid-cols-[4fr_7fr]">
<time class="font-abc text-more-13 leading-none lg:text-more-16" datetime!="2025.07.18">2025.07.18</time>
<p class="text-more-13 whitespace-nowrap lg:text-more-14">GORA KADAN FUJI</p>
</span>
<p class="line-link__ttl text-more-14 mt-[0.6em] leading-relaxed line-clamp-2 lg:text-more-14 lg:mt-0">7月20日 GORA KADAN FUJI 開業</p>
</a>
<a class="line-link relative text-more-12 py-[2em] lg:grid lg:grid-cols-[1fr_3fr] lg:py-[1.6em] first:[border-top:1px_solid_var(--c-gray-02)]" href="https://www.gorakadan.com/hakone/news/1454/" rel="noopener noreferrer" target="_blank">
<div class="line-link__line absolute left-0 bottom-0 w-full h-[1px] bg-gray02"></div>
<span class="grid grid-cols-[2fr_5fr] gap-x-[8px] items-center lg:grid-cols-[4fr_7fr]">
<time class="font-abc text-more-13 leading-none lg:text-more-16" datetime!="2025.04.28">2025.04.28</time>
<p class="text-more-13 whitespace-nowrap lg:text-more-14">強羅花壇</p>
</span>
<p class="line-link__ttl text-more-14 mt-[0.6em] leading-relaxed line-clamp-2 lg:text-more-14 lg:mt-0">強羅花壇のサスティナビリティへの取組み</p>
</a>
</div>
</div>
</div>
<footer class="footer relative flex justify-between items-end w-3/4 mt-[120px] mx-auto pb-[17px] z-20 lg:w-auto lg:mx-more-60 lg:mt-more-120 lg:pb-[18px]">
<div class="grid grid-cols-1 gap-y-[14px] lg:grid-cols-2 lg:gap-x-more-30">
<div class="flex items-center gap-x-[16px]">
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.facebook.com/gorakadan" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="22" viewbox="0 0 23 22" width="23" xmlns="http://www.w3.org/2000/svg">
<path d="M23 11.0444C23 4.94472 17.8513 0 11.5 0C5.14872 0 0 4.94472 0 11.0444C0 16.5568 4.20536 21.126 9.70312 21.9545V14.2369H6.7832V11.0444H9.70312V8.61115C9.70312 5.84316 11.42 4.3142 14.0468 4.3142C15.3051 4.3142 16.6211 4.52991 16.6211 4.52991V7.24786H15.171C13.7425 7.24786 13.2969 8.09927 13.2969 8.97268V11.0444H16.4863L15.9765 14.2369H13.2969V21.9545C18.7946 21.126 23 16.557 23 11.0444Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[20px] lg:w-more-20 -instagram" href="https://www.instagram.com/gorakadan/" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="21" viewbox="0 0 21 21" width="21" xmlns="http://www.w3.org/2000/svg">
<path d="M10.4844 2.23245C13.1722 2.23245 13.491 2.24252 14.553 2.29117C17.281 2.41532 18.5552 3.7097 18.6794 6.41758C18.728 7.47876 18.7373 7.79753 18.7373 10.4853C18.7373 13.1739 18.7272 13.4918 18.6794 14.553C18.5544 17.2583 17.2835 18.5552 14.553 18.6794C13.491 18.728 13.1739 18.7381 10.4844 18.7381C7.79669 18.7381 7.47792 18.728 6.41675 18.6794C3.68202 18.5544 2.41449 17.2541 2.29033 14.5521C2.24168 13.491 2.23161 13.173 2.23161 10.4844C2.23161 7.79669 2.24252 7.47876 2.29033 6.41675C2.41532 3.7097 3.68622 2.41449 6.41675 2.29033C7.47876 2.24252 7.79669 2.23245 10.4844 2.23245ZM10.4844 0.417969C7.75055 0.417969 7.40829 0.429713 6.33454 0.478368C2.67873 0.646142 0.646981 2.67454 0.479206 6.3337C0.429713 7.40829 0.417969 7.75055 0.417969 10.4844C0.417969 13.2183 0.429713 13.5614 0.478368 14.6352C0.646142 18.291 2.67454 20.3227 6.3337 20.4905C7.40829 20.5392 7.75055 20.5509 10.4844 20.5509C13.2183 20.5509 13.5614 20.5392 14.6352 20.4905C18.2876 20.3227 20.3244 18.2943 20.4897 14.6352C20.5392 13.5614 20.5509 13.2183 20.5509 10.4844C20.5509 7.75055 20.5392 7.40829 20.4905 6.33454C20.3261 2.68209 18.2952 0.646981 14.636 0.479206C13.5614 0.429713 13.2183 0.417969 10.4844 0.417969ZM10.4844 5.31531C7.62975 5.31531 5.31531 7.62975 5.31531 10.4844C5.31531 13.3391 7.62975 15.6544 10.4844 15.6544C13.3391 15.6544 15.6536 13.34 15.6536 10.4844C15.6536 7.62975 13.3391 5.31531 10.4844 5.31531ZM10.4844 13.8399C8.63137 13.8399 7.12895 12.3383 7.12895 10.4844C7.12895 8.63137 8.63137 7.12895 10.4844 7.12895C12.3375 7.12895 13.8399 8.63137 13.8399 10.4844C13.8399 12.3383 12.3375 13.8399 10.4844 13.8399ZM15.8583 3.90348C15.1905 3.90348 14.6494 4.44456 14.6494 5.11146C14.6494 5.77836 15.1905 6.31944 15.8583 6.31944C16.5252 6.31944 17.0654 5.77836 17.0654 5.11146C17.0654 4.44456 16.5252 3.90348 15.8583 3.90348Z" fill="#111111"></path>
</svg>
</a>
<a class="link-icon w-[24px] lg:w-more-24 -rc" href="https://www.relaischateaux.com/us/japan/gora-hakone-hakone" rel="noopener noreferrer" target="_blank">
<svg fill="none" height="28" viewbox="0 0 27 28" width="27" xmlns="http://www.w3.org/2000/svg">
<path d="M11.6072 11.5435C10.3255 11.9304 8.78743 12.5753 8.33884 13.9941C8.14658 14.7679 8.40292 15.5096 9.01172 15.9932C9.68461 16.5414 10.71 16.7026 11.4469 16.3479C11.9276 16.1222 12.3121 15.7353 12.4723 15.1549C11.9917 15.5096 11.2226 15.5096 10.742 15.1549C10.3575 14.8324 10.1332 14.381 10.1332 13.8651C10.2293 12.8978 11.2547 12.1561 12.0878 11.8982C12.3762 11.8015 12.6966 11.737 13.017 11.737C12.8248 12.7043 11.9917 13.7039 11.1265 14.1553C11.1265 14.1553 11.8314 14.4455 12.9209 14.0908C12.9209 14.0908 13.0811 15.2194 13.6258 16.2512C14.2026 15.2194 14.3307 14.0908 14.3307 14.0908C15.4202 14.4455 16.1251 14.1553 16.1251 14.1553C15.26 13.6716 14.4269 12.6721 14.2346 11.737C14.555 11.737 14.8755 11.8337 15.1638 11.8982C16.029 12.1561 17.0543 12.8655 17.1184 13.8651C17.1505 14.381 16.9262 14.8324 16.5096 15.1549C16.029 15.5096 15.26 15.5096 14.7793 15.1549C14.9395 15.7353 15.3241 16.1222 15.8047 16.3479C16.5417 16.7026 17.5991 16.5091 18.2399 15.9932C18.8487 15.5096 19.105 14.7679 18.9128 13.9941C18.4642 12.5753 16.9262 11.9304 15.6445 11.5435C17.6311 11.35 19.5216 10.8341 20.9635 9.38312C22.0529 8.22232 22.7258 6.6101 22.117 5.03013C21.7325 3.93382 20.6431 3.22444 19.5536 3.15995C16.9262 2.99873 15.7086 5.64277 15.5804 6.48112C15.5804 6.48112 16.9903 4.73993 18.9448 5.19135C19.5536 5.32033 20.1304 5.83624 20.3227 6.48112C20.611 7.64192 20.1304 8.73823 19.2653 9.54434C18.0156 10.5762 16.4776 10.9953 14.8755 11.1243C14.9075 10.2537 15.1318 9.47985 15.6765 8.83497C16.061 8.38355 16.6057 8.09335 17.1184 8.19008C17.5991 8.28681 17.7593 8.73823 17.7272 9.06068C17.5991 9.57659 17.1505 9.89903 17.1505 9.89903C18.0477 9.8023 18.9448 9.06068 19.1691 8.22232C19.4255 7.15826 18.5603 5.99746 17.1184 6.3199C15.0357 6.80357 14.3948 9.2219 14.2987 11.1888C14.2346 11.1888 14.1705 11.1888 14.0744 11.1888C13.5938 8.9317 14.7473 7.06152 15.1959 5.06237C15.6124 3.32117 14.6832 1.41875 13.5938 0C12.4723 1.38651 11.5751 3.28893 11.9596 5.06237C12.4082 7.06152 13.5617 8.9317 13.0811 11.1888C13.017 11.1888 12.9529 11.1888 12.8568 11.1888C12.7927 9.2219 12.1519 6.80357 10.0691 6.3199C8.62722 5.99746 7.73003 7.15826 8.01841 8.22232C8.24271 9.06068 9.13989 9.8023 10.0371 9.89903C10.0371 9.89903 9.62053 9.57659 9.55644 9.12517C9.5244 8.80272 9.71665 8.3513 10.1652 8.25457C10.6779 8.15783 11.2226 8.44803 11.6072 8.89946C12.1519 9.54434 12.3441 10.3182 12.4082 11.1888C10.8061 11.0598 9.26806 10.6407 8.01841 9.60883C7.15327 8.80272 6.70468 7.70641 6.96102 6.54561C7.12123 5.90073 7.73003 5.38482 8.33884 5.25584C10.2934 4.80442 11.7033 6.54561 11.7033 6.54561C11.5751 5.70726 10.3575 3.09546 7.73003 3.22444C6.64059 3.28893 5.55116 3.99831 5.16665 5.09462C4.5258 6.67459 5.23073 8.31906 6.32017 9.44761C7.73003 10.8341 9.62053 11.35 11.6072 11.5435Z" fill="black"></path>
<path d="M23.5576 21.8953L23.9742 22.508H23.077L22.9488 22.3145C22.6605 22.4757 22.34 22.5725 22.0196 22.5725C21.3467 22.5725 20.834 22.1533 20.834 21.5084C20.834 20.9603 21.1224 20.6701 21.6031 20.3799C21.3788 20.1219 21.2826 19.9284 21.2826 19.606C21.2826 19.0256 21.6992 18.7354 22.2439 18.7354C22.7566 18.7354 23.1411 19.0901 23.1411 19.606C23.1411 19.9607 22.8848 20.3154 22.5964 20.5088L23.1411 21.3149C23.3333 21.1537 23.4936 20.9603 23.6217 20.8313L24.0383 21.3149C23.9421 21.5407 23.7499 21.7341 23.5576 21.8953ZM22.5964 21.8309L21.9876 20.928C21.6992 21.0892 21.6031 21.2182 21.6031 21.5084C21.6031 21.7986 21.8274 21.9598 22.1798 21.9598C22.308 21.9598 22.4682 21.8953 22.5964 21.8309ZM22.0196 19.6382C22.0196 19.7672 22.1157 19.9284 22.2759 20.0897C22.4682 19.9607 22.5964 19.7995 22.5964 19.6382C22.5964 19.4448 22.5002 19.348 22.308 19.348C22.1157 19.348 22.0196 19.4448 22.0196 19.6382Z" fill="black"></path>
<path d="M5.13287 22.5449L4.10751 20.8037V22.5449H3.27441V18.8046H4.58815C5.35716 18.8046 5.86984 19.2882 5.86984 19.9009C5.86984 20.449 5.51737 20.836 5.03674 20.9649L6.03005 22.5127H5.13287V22.5449ZM4.62019 19.5139H4.10751V20.4168H4.62019C4.90857 20.4168 5.06878 20.2233 5.06878 19.9331C5.06878 19.6752 4.87653 19.5139 4.62019 19.5139Z" fill="black"></path>
<path d="M9.26246 22.5449V18.8046H10.0635V21.8355H11.3772V22.5449H9.26246Z" fill="black"></path>
<path d="M14.2314 22.5353L13.943 21.6002H12.8215L12.5331 22.5353H11.668L12.9817 18.8271H13.7507L15.0965 22.5353H14.2314ZM13.4623 19.9879C13.4303 19.859 13.3662 19.6977 13.3662 19.6977C13.3662 19.6977 13.3342 19.859 13.3021 19.9879L13.0137 20.8908H13.7507L13.4623 19.9879Z" fill="black"></path>
<path d="M16.3763 18.8047H15.5752V22.5128H16.3763V18.8047Z" fill="black"></path>
<path d="M18.0777 22.5656C17.5651 22.5656 17.1805 22.3721 16.8281 22.1142L17.2446 21.5338C17.4689 21.695 17.7894 21.8562 18.0777 21.8562C18.4622 21.8562 18.5584 21.7272 18.5584 21.5015C18.5584 21.1146 17.8855 21.0179 17.5651 20.8244C17.1805 20.6309 16.8922 20.373 16.8922 19.7926C16.8922 19.0187 17.5651 18.7285 18.2059 18.7285C18.5584 18.7285 18.9749 18.8252 19.2633 19.051L18.9108 19.6636C18.7506 19.5346 18.4622 19.4379 18.2059 19.4379C17.8214 19.4379 17.6932 19.6636 17.6932 19.7926C17.6932 20.115 18.1739 20.244 18.5904 20.4052C18.9429 20.5664 19.3274 20.8244 19.3274 21.437C19.3915 22.1787 18.8788 22.5656 18.0777 22.5656Z" fill="black"></path>
<path d="M2.50378 27.2375C1.3823 27.2375 0.613281 26.4959 0.613281 25.3028C0.613281 24.1743 1.44638 23.4004 2.47173 23.4004C3.01645 23.4004 3.433 23.5616 3.78547 23.9485L3.33688 24.4967C3.08054 24.2387 2.79216 24.1098 2.47173 24.1098C1.83089 24.1098 1.44638 24.6257 1.44638 25.3028C1.44638 26.0767 1.83089 26.5281 2.50378 26.5281C2.95237 26.5281 3.20871 26.3346 3.433 26.1089L3.91364 26.6571C3.56117 27.0118 3.14462 27.2375 2.50378 27.2375Z" fill="black"></path>
<path d="M10.2274 27.17L9.93907 26.2349H8.81758L8.5292 27.17H7.66406L8.9778 23.4619H9.74681L11.0926 27.17H10.2274ZM9.45843 24.655C9.42639 24.526 9.3623 24.3648 9.3623 24.3648C9.3623 24.3648 9.33026 24.526 9.29822 24.655L9.00984 25.5578H9.71477L9.45843 24.655Z" fill="black"></path>
<path d="M10.9951 23.4766V24.1859H11.7321V27.1847H12.5331V24.1859H13.2381V23.4766H10.9951Z" fill="black"></path>
<path d="M18.8466 27.1925L18.5582 26.2574H17.4367L17.1483 27.1925H16.2832L17.5969 23.4844H18.366L19.7117 27.1925H18.8466ZM18.0776 24.6774C18.0455 24.5484 17.9814 24.3872 17.9814 24.3872C17.9814 24.3872 17.9494 24.5484 17.9174 24.6774L17.629 25.5803H18.3339L18.0776 24.6774Z" fill="black"></path>
<path d="M21.442 27.257C20.6089 27.257 19.872 26.7088 19.872 25.677V23.4844H20.673V25.7092C20.673 26.2252 20.9935 26.5798 21.442 26.5798C21.8906 26.5798 22.2431 26.2252 22.2431 25.7092V23.5166H23.0442V25.7092C23.0442 26.7088 22.3072 27.257 21.442 27.257Z" fill="black"></path>
<path d="M25.4465 23.4766L24.9017 24.4439L24.357 23.4766H23.4598L24.4531 25.1855L23.2676 27.1847H24.1648L24.9017 25.8949L25.6708 27.1847H26.5679L25.3503 25.1855L26.3436 23.4766H25.4465Z" fill="black"></path>
<path d="M13.6582 23.4766V27.1847H15.9012V26.5075H14.4913V25.7014H15.805V24.9921H14.4913V24.1859H15.805V23.4766H13.6582Z" fill="black"></path>
<path d="M7.24735 21.8357V21.0296H8.59312V20.3202H7.24735V19.5141H8.59312V18.8047H6.44629V22.545H8.65721V21.8357H7.24735Z" fill="black"></path>
<path d="M6.4478 23.4766V24.9921H5.16611V23.4766H4.33301V27.1847H5.16611V25.7014H6.4478V27.1847H7.24886V23.4766H6.4478Z" fill="black"></path>
</svg>
</a>
</div>
<a class="link-arrow text-more-12 lg:text-more-13 flex items-center gap-x-[.4em]" href="https://www.gorakadan.com/recruit">
<span>採用について</span>
<span class="link-arrow_icon block w-5 h-5 leading-[0] overflow-hidden">
<svg fill="none" height="14" viewbox="0 0 14 14" width="14" xmlns="http://www.w3.org/2000/svg">
<path d="M2.16602 11.5234L11.166 2.52344" stroke="black"></path>
<path d="M7.11523 2.25391L11.4828 2.2596L11.4885 6.62718" stroke="black"></path>
</svg>
</span>
</a> </div>
<p class="font-abc text-more-10 lg:text-more-13">© GORAKADAN</p>
</footer>
</main>
<script type="speculationrules">
{"prefetch":[{"source":"document","where":{"and":[{"href_matches":"\/*"},{"not":{"href_matches":["\/wp-*.php","\/wp-admin\/*","\/wp-content\/uploads\/*","\/wp-content\/*","\/wp-content\/plugins\/*","\/wp-content\/themes\/gorakadan-brand\/*","\/*\\?(.+)"]}},{"not":{"selector_matches":"a[rel~=\"nofollow\"]"}},{"not":{"selector_matches":".no-prefetch, .no-prefetch a"}}]},"eagerness":"conservative"}]}
</script>
<iframe allow="join-ad-interest-group" data-load-time="1754400160730" data-tagging-id="AW-337705360" height="0" src="https://td.doubleclick.net/td/rul/337705360?random=1754400160688&amp;cv=11&amp;fst=1754400160688&amp;fmt=3&amp;bg=ffffff&amp;guid=ON&amp;async=1&amp;en=gtag.config&amp;gtm=45be5811v879190198z8846561558za200zb846561558zd846561558xec&amp;gcd=13l3l3l3l1l1&amp;dma=0&amp;tag_exp=101509157~103116026~103200004~103233427~104527906~104528500~104684208~104684211~104948813~105087538~105087540~105103161~105103163&amp;u_w=1920&amp;u_h=1080&amp;url=https%3A%2F%2Fwww.gorakadan.com%2F%3Futm_source%3Dgoogle%26utm_medium%3Dmaps&amp;hn=www.googleadservices.com&amp;frm=0&amp;tiba=%E5%BC%B7%E7%BE%85%E8%8A%B1%E5%A3%87%20%7C%20GORA%20KADAN%20%5B%E3%83%96%E3%83%A9%E3%83%B3%E3%83%89%E3%82%B5%E3%82%A4%E3%83%88%5D&amp;npa=0&amp;pscdl=noapi&amp;auid=1447106691.**********&amp;uaa=x64&amp;uab=64&amp;uafvl=Not)A%253BBrand%3B8.0.0.0%7CChromium%3B138.0.7204.97%7CGoogle%2520Chrome%3B138.0.7204.97&amp;uamb=0&amp;uam=&amp;uap=Windows&amp;uapv=10.0&amp;uaw=0&amp;fledge=1&amp;data=event%3Dgtag.config" style="display: none; visibility: hidden;" width="0"></iframe><iframe height="0" style="display: none; visibility: hidden;" width="0"></iframe><script id="theme-scripts-js" src="https://www.gorakadan.com/wp-content/themes/gorakadan-brand/dist/js/app.js?ver=1754400140" type="text/javascript"></script>
</body></html>