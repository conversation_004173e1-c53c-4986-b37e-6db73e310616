<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5fc5d6e9-c98f-4255-815a-eca6a95bcc73" name="Changes" comment="fix:修复数据库字段official_website在只有官网名称为空的问题">
      <change beforePath="$PROJECT_DIR$/config/settings.json" beforeDir="false" afterPath="$PROJECT_DIR$/config/settings.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/models/url_analyzer.py" beforeDir="false" afterPath="$PROJECT_DIR$/models/url_analyzer.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/sql_upload.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/sql_upload.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/utils/capture_page.py" beforeDir="false" afterPath="$PROJECT_DIR$/utils/capture_page.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="FlaskConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" module-name="ailinksage23" is-module-sdk="true">
    <envs>
      <env key="FLASK_APP" value="app" />
    </envs>
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform)); sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;from flask.cli import ScriptInfo, NoAppException&#10;for module in [&quot;main.py&quot;, &quot;wsgi.py&quot;, &quot;app.py&quot;]:&#10;    try: locals().update(ScriptInfo(app_import_path=module, create_app=None).load_app().make_shell_context()); print(&quot;\nFlask App: %s&quot; % app.import_name); break&#10;    except NoAppException: pass" />
    <option name="myUseModuleSdk" value="true" />
    <option name="myModuleName" value="ailinksage23" />
    <option name="myEnvs">
      <map>
        <entry key="FLASK_APP" value="app" />
      </map>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;wenbochen&quot;,
      &quot;fullname&quot;: &quot;wenbochen&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;http://git.dev.sh.ctripcorp.com/octopus/ailinksage.git&quot;,
    &quot;second&quot;: &quot;8a4a4ae0-79b1-4c4b-a91b-c68b550d28ad&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2zaERPZ6BLfdi7kXbaLa0esHdsw" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Flask server.Flask (app.py).executor&quot;: &quot;Run&quot;,
    &quot;Flask server.ailinksage23.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python tests.Doctest upload_images_to_nephele_v1.executor&quot;: &quot;Debug&quot;,
    &quot;Python tests.Doctests in file_upload.executor&quot;: &quot;Run&quot;,
    &quot;Python.ailinksage23.executor&quot;: &quot;Run&quot;,
    &quot;Python.file_upload.executor&quot;: &quot;Run&quot;,
    &quot;Python.gemini_search.executor&quot;: &quot;Run&quot;,
    &quot;Python.hotel_data_fusion.executor&quot;: &quot;Debug&quot;,
    &quot;Python.img_upload (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.img_upload.executor&quot;: &quot;Run&quot;,
    &quot;Python.retry_failed_hotels.executor&quot;: &quot;Debug&quot;,
    &quot;Python.select_rows.executor&quot;: &quot;Run&quot;,
    &quot;Python.send_req.executor&quot;: &quot;Run&quot;,
    &quot;Python.send_req22.executor&quot;: &quot;Run&quot;,
    &quot;Python.send_req_aiohttp (1).executor&quot;: &quot;Run&quot;,
    &quot;Python.send_req_aiohttp.executor&quot;: &quot;Run&quot;,
    &quot;Python.send_req_aiohttp2.executor&quot;: &quot;Run&quot;,
    &quot;Python.serpapi_search.executor&quot;: &quot;Debug&quot;,
    &quot;Python.shuttle_url_finder.executor&quot;: &quot;Debug&quot;,
    &quot;Python.simple_upload.executor&quot;: &quot;Run&quot;,
    &quot;Python.sql_upload.executor&quot;: &quot;Run&quot;,
    &quot;Python.start.executor&quot;: &quot;Run&quot;,
    &quot;Python.testpy.executor&quot;: &quot;Run&quot;,
    &quot;Python.分析.executor&quot;: &quot;Run&quot;,
    &quot;Python.更改csv格式.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev__cwb&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/python_projects/local_python/ailinksage23/data&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.intellij.python.pro.flask.configuration.FlaskConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\python_projects\local_python\ailinksage23\data" />
      <recent name="D:\python_projects\local_python\ailinksage23\has_website_access3\access" />
      <recent name="D:\python_projects\local_python\ailinksage23\data\test" />
      <recent name="D:\python_projects\local_python\ailinksage23\utils\TEST" />
      <recent name="D:\python_projects\local_python\ailinksage23" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\python_projects\local_python\ailinksage23\all_result" />
      <recent name="D:\python_projects\local_python\ailinksage23\utils\TEST\kuriyasuizan_com\html\homepage" />
      <recent name="D:\python_projects\local_python\ailinksage23\utils\TEST\kuriyasuizan_com\html\access" />
      <recent name="D:\python_projects\local_python\ailinksage23\utils\TEST\kuriyasuizan_com\image" />
      <recent name="D:\python_projects\local_python\ailinksage23\utils\TEST\kuriyasuizan_com\access" />
    </key>
  </component>
  <component name="RunManager" selected="Python.serpapi_search">
    <configuration name="ailinksage23" type="PythonConfigurationType" factoryName="Python">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ailinksage23" type="PythonConfigurationType" factoryName="Python">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/app.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="hotel_data_fusion" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/data/hotel_data_fusion.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="send_req_aiohttp" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/data" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/data/send_req_aiohttp.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="serpapi_search" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/utils" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/utils/serpapi_search.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ailinksage23" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/start.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="Flask (app.py)" type="Python.FlaskServer" temporary="true" nameIsGenerated="true">
      <module name="ailinksage23" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="ailinksage23" type="Python.FlaskServer">
      <module name="ailinksage23" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="ailinksage23" type="Python.FlaskServer">
      <module name="ailinksage23" />
      <option name="target" value="$PROJECT_DIR$/app.py" />
      <option name="targetType" value="PATH" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.serpapi_search" />
        <item itemvalue="Python.send_req_aiohttp" />
        <item itemvalue="Python.start" />
        <item itemvalue="Flask server.Flask (app.py)" />
        <item itemvalue="Python.hotel_data_fusion" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-PY-251.26094.141" />
        <option value="bundled-python-sdk-9f8e2b94138c-36ea0e71a18c-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26094.141" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5fc5d6e9-c98f-4255-815a-eca6a95bcc73" name="Changes" comment="" />
      <created>1751960774970</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751960774970</updated>
      <workItem from="1751960776097" duration="679000" />
      <workItem from="1751962771004" duration="1872000" />
      <workItem from="1751983104428" duration="24112000" />
      <workItem from="1752071220841" duration="4229000" />
      <workItem from="1752077256628" duration="4187000" />
      <workItem from="1752126418214" duration="6206000" />
      <workItem from="1752220204303" duration="2748000" />
      <workItem from="1752232433403" duration="14634000" />
      <workItem from="1752459144959" duration="18017000" />
      <workItem from="1752631525363" duration="23000" />
      <workItem from="1752760527339" duration="1281000" />
      <workItem from="1752808395289" duration="1435000" />
      <workItem from="1752831142593" duration="8063000" />
      <workItem from="1753023261517" duration="1552000" />
      <workItem from="1753063535624" duration="28311000" />
      <workItem from="1753158626320" duration="33777000" />
      <workItem from="1753252541751" duration="2774000" />
      <workItem from="1753257057162" duration="642000" />
      <workItem from="1753331872103" duration="4342000" />
      <workItem from="1753363228266" duration="7719000" />
      <workItem from="1753419149072" duration="695000" />
      <workItem from="1753720030650" duration="610000" />
      <workItem from="1753755218091" duration="46000" />
      <workItem from="1753866204023" duration="464000" />
      <workItem from="1753934908231" duration="10785000" />
      <workItem from="1753964604446" duration="237000" />
      <workItem from="1754039492079" duration="11000" />
      <workItem from="1754305893675" duration="4278000" />
      <workItem from="1754310857367" duration="787000" />
      <workItem from="1754320234914" duration="1069000" />
      <workItem from="1754360497440" duration="14269000" />
      <workItem from="1754377589873" duration="3892000" />
      <workItem from="1754381523117" duration="14263000" />
      <workItem from="1754448964383" duration="15534000" />
      <workItem from="1754483647892" duration="496000" />
      <workItem from="1754486052430" duration="361000" />
      <workItem from="1754620680045" duration="458000" />
      <workItem from="1754622082324" duration="810000" />
    </task>
    <task id="LOCAL-00001" summary="feat:修改提取官网的模式,兼顾效率和速度">
      <option name="closed" value="true" />
      <created>1751962899709</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751962899709</updated>
    </task>
    <task id="LOCAL-00002" summary="add:添加图片的参考依据">
      <option name="closed" value="true" />
      <created>1752045252494</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752045252494</updated>
    </task>
    <task id="LOCAL-00003" summary="add:添加图片的参考依据2">
      <option name="closed" value="true" />
      <created>1752045451089</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752045451089</updated>
    </task>
    <task id="LOCAL-00004" summary="add:修复酒店房型查询的Bug">
      <option name="closed" value="true" />
      <created>1752050356547</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752050356547</updated>
    </task>
    <task id="LOCAL-00005" summary="fix:优化房型查询，修复部分bug">
      <option name="closed" value="true" />
      <created>1752080918117</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752080918117</updated>
    </task>
    <task id="LOCAL-00006" summary="fix:修改浏览器位置">
      <option name="closed" value="true" />
      <created>1752081347863</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752081347863</updated>
    </task>
    <task id="LOCAL-00007" summary="ADD:修改前端请求发送策略,从两次请求，降低到1次请求">
      <option name="closed" value="true" />
      <created>1752249172121</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752249172121</updated>
    </task>
    <task id="LOCAL-00008" summary="ADD:修改缓存策略，用户可以在前端实时控制缓存">
      <option name="closed" value="true" />
      <created>1752251841036</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752251841036</updated>
    </task>
    <task id="LOCAL-00009" summary="ADD:添加前端实时控制多模态策略">
      <option name="closed" value="true" />
      <created>1752293966722</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752293966722</updated>
    </task>
    <task id="LOCAL-00010" summary="ADD:添加前端实时控制多模态策略2">
      <option name="closed" value="true" />
      <created>1752303123200</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752303123200</updated>
    </task>
    <task id="LOCAL-00011" summary="add:暂时禁用页面缓存">
      <option name="closed" value="true" />
      <created>1752459816690</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752459816690</updated>
    </task>
    <task id="LOCAL-00012" summary="add:暂时禁用页面缓存">
      <option name="closed" value="true" />
      <created>1752468168932</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752468168932</updated>
    </task>
    <task id="LOCAL-00013" summary="add:严格区分自营接送机和第三方服务，减少误判，修改提示词">
      <option name="closed" value="true" />
      <created>1752474664545</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752474664545</updated>
    </task>
    <task id="LOCAL-00014" summary="feat:打开页面访问策略设置">
      <option name="closed" value="true" />
      <created>1752483514046</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752483514046</updated>
    </task>
    <task id="LOCAL-00015" summary="feat:修复房型查询的bug">
      <option name="closed" value="true" />
      <created>1752485306218</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752485306218</updated>
    </task>
    <task id="LOCAL-00016" summary="添加接送接查找页面并截图的逻辑">
      <option name="closed" value="true" />
      <created>1753024163811</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753024163811</updated>
    </task>
    <task id="LOCAL-00017" summary="add:添加http请求访问逻辑">
      <option name="closed" value="true" />
      <created>1753067765755</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1753067765755</updated>
    </task>
    <task id="LOCAL-00018" summary="fix:修复非法注入的错误">
      <option name="closed" value="true" />
      <created>1753073622695</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1753073622695</updated>
    </task>
    <task id="LOCAL-00019" summary="add:添加接口选择时地域的支持">
      <option name="closed" value="true" />
      <created>1753101633243</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753101633243</updated>
    </task>
    <task id="LOCAL-00020" summary="fix:修正不正常的过滤词">
      <option name="closed" value="true" />
      <created>1753104966311</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753104966311</updated>
    </task>
    <task id="LOCAL-00021" summary="fix:增加接口接送机直接落库的功能">
      <option name="closed" value="true" />
      <created>1754309955219</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1754309955219</updated>
    </task>
    <task id="LOCAL-00022" summary="fix:修复chrome路径">
      <option name="closed" value="true" />
      <created>1754310061783</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1754310061783</updated>
    </task>
    <task id="LOCAL-00023" summary="fix:修复保存和入库的字段问题，添加locale字段">
      <option name="closed" value="true" />
      <created>1754362841609</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1754362841609</updated>
    </task>
    <task id="LOCAL-00024" summary="fix:改进官网的搜索逻辑">
      <option name="closed" value="true" />
      <created>1754372279148</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1754372279148</updated>
    </task>
    <task id="LOCAL-00025" summary="fix:修改漏洞注入，精简关于官网Url查找的冗余代码">
      <option name="closed" value="true" />
      <created>1754374791928</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1754374791928</updated>
    </task>
    <task id="LOCAL-00026" summary="fix:修复url匹配和替换的bug">
      <option name="closed" value="true" />
      <created>1754380571708</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1754380571708</updated>
    </task>
    <task id="LOCAL-00027" summary="add:增加url处理的类">
      <option name="closed" value="true" />
      <created>1754381588592</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1754381588592</updated>
    </task>
    <task id="LOCAL-00028" summary="fix:修复数据库字段official_website在只有官网名称为空的问题">
      <option name="closed" value="true" />
      <created>1754387789951</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1754387789951</updated>
    </task>
    <option name="localTasksCounter" value="29" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="add:添加图片的参考依据2" />
    <MESSAGE value="add:修复酒店房型查询的Bug" />
    <MESSAGE value="fix:优化房型查询，修复部分bug" />
    <MESSAGE value="fix:修改浏览器位置" />
    <MESSAGE value="ADD:修改前端请求发送策略,从两次请求，降低到1次请求" />
    <MESSAGE value="ADD:修改缓存策略，用户可以在前端实时控制缓存" />
    <MESSAGE value="ADD:添加前端实时控制多模态策略" />
    <MESSAGE value="ADD:添加前端实时控制多模态策略2" />
    <MESSAGE value="add:暂时禁用页面缓存" />
    <MESSAGE value="add:严格区分自营接送机和第三方服务，减少误判，修改提示词" />
    <MESSAGE value="feat:打开页面访问策略设置" />
    <MESSAGE value="feat:修复房型查询的bug" />
    <MESSAGE value="添加接送接查找页面并截图的逻辑" />
    <MESSAGE value="add:添加http请求访问逻辑" />
    <MESSAGE value="fix:修复非法注入的错误" />
    <MESSAGE value="add:添加接口选择时地域的支持" />
    <MESSAGE value="fix:修正不正常的过滤词" />
    <MESSAGE value="fix:增加接口接送机直接落库的功能" />
    <MESSAGE value="fix:修复chrome路径" />
    <MESSAGE value="fix:修复保存和入库的字段问题，添加locale字段" />
    <MESSAGE value="fix:改进官网的搜索逻辑" />
    <MESSAGE value="fix:修改漏洞注入，精简关于官网Url查找的冗余代码" />
    <MESSAGE value="fix:修复url匹配和替换的bug" />
    <MESSAGE value="add:增加url处理的类" />
    <MESSAGE value="fix:修复数据库字段official_website在只有官网名称为空的问题" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:修复数据库字段official_website在只有官网名称为空的问题" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/url_analyzer.py</url>
          <line>564</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/url_analyzer.py</url>
          <line>522</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/url_analyzer.py</url>
          <line>533</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/models/url_analyzer.py</url>
          <line>539</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/utils/shuttle_url_finder.py</url>
          <line>1063</line>
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/sql_upload.py</url>
          <line>134</line>
          <option name="timeStamp" value="64" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/scripts/sql_upload.py</url>
          <line>98</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data/send_req_aiohttp.py</url>
          <line>342</line>
          <option name="timeStamp" value="66" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data/hotel_data_fusion.py</url>
          <line>333</line>
          <option name="timeStamp" value="69" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data/sql_upload.py</url>
          <line>134</line>
          <option name="timeStamp" value="70" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/data/sql_upload.py</url>
          <line>112</line>
          <option name="timeStamp" value="71" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/utils/serpapi_search.py</url>
          <line>270</line>
          <option name="timeStamp" value="72" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/ailinksage23$send_req_aiohttp2.coverage" NAME="send_req_aiohttp2 Coverage Results" MODIFIED="1754387477192" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$start.coverage" NAME="start Coverage Results" MODIFIED="1754621119867" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/ailinksage23$ailinksage23.coverage" NAME="ailinksage23 Coverage Results" MODIFIED="1754377350815" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/ailinksage23$csv.coverage" NAME="更改csv格式 Coverage Results" MODIFIED="1753182555631" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$send_req.coverage" NAME="send_req Coverage Results" MODIFIED="1753947303791" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$Flask__app_py_.coverage" NAME="Flask (app.py) Coverage Results" MODIFIED="1754620726758" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="" />
    <SUITE FILE_PATH="coverage/ailinksage23$gemini_search.coverage" NAME="gemini_search Coverage Results" MODIFIED="1753023282600" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/ailinksage23$serpapi_search.coverage" NAME="serpapi_search Coverage Results" MODIFIED="1754622138097" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/ailinksage23$testpy.coverage" NAME="testpy Coverage Results" MODIFIED="1753412818049" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/html" />
    <SUITE FILE_PATH="coverage/ailinksage23$img_upload__1_.coverage" NAME="img_upload (1) Coverage Results" MODIFIED="1753946733050" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$hotel_data_fusion.coverage" NAME="hotel_data_fusion Coverage Results" MODIFIED="1754483931045" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$Doctest_upload_images_to_nephele_v1.coverage" NAME="Doctest upload_images_to_nephele_v1 Coverage Results" MODIFIED="1753935891492" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$Doctests_in_file_upload.coverage" NAME="Doctests in file_upload Coverage Results" MODIFIED="1753937381836" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$sql_upload.coverage" NAME="sql_upload Coverage Results" MODIFIED="1754446339268" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$send_req_aiohttp.coverage" NAME="send_req_aiohttp Coverage Results" MODIFIED="1754621123978" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$send_req22.coverage" NAME="send_req22 Coverage Results" MODIFIED="1753180968008" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$select_rows.coverage" NAME="select_rows Coverage Results" MODIFIED="1753102299867" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$shuttle_url_finder.coverage" NAME="shuttle_url_finder Coverage Results" MODIFIED="1754473010584" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/utils" />
    <SUITE FILE_PATH="coverage/ailinksage23$file_upload.coverage" NAME="file_upload Coverage Results" MODIFIED="1753952724258" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$retry_failed_hotels.coverage" NAME="retry_failed_hotels Coverage Results" MODIFIED="1754469091326" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$.coverage" NAME="分析 Coverage Results" MODIFIED="1753194195433" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$simple_upload.coverage" NAME="simple_upload Coverage Results" MODIFIED="1753946594264" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
    <SUITE FILE_PATH="coverage/ailinksage23$send_req_aiohttp__1_.coverage" NAME="send_req_aiohttp (1) Coverage Results" MODIFIED="1754449731673" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/data" />
    <SUITE FILE_PATH="coverage/ailinksage23$img_upload.coverage" NAME="img_upload Coverage Results" MODIFIED="1753952707322" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/scripts" />
  </component>
</project>