<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1" name="viewport"/>
<title>All you need | shimoda</title>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<meta content="Studio.Design" name="generator"/>
<meta content="all" name="robots"/>
<meta content="ALL YOU NEED" property="og:site_name"/>
<meta content="All you need | shimoda" property="og:title"/>
<meta content="https://storage.googleapis.com/production-os-assets/assets/813c5d80-f312-4656-999b-d3cd0c6e6c24" property="og:image"/>
<meta content="We want to create a place where people who love the sea, nature and travel can come together here in Izu Shimoda! To make that dream come true, we renovated and finally completed it!" property="og:description"/>
<meta content="website" property="og:type"/>
<meta content="We want to create a place where people who love the sea, nature and travel can come together here in Izu Shimoda! To make that dream come true, we renovated and finally completed it!" name="description"/>
<meta content="summary_large_image" property="twitter:card"/>
<meta content="https://storage.googleapis.com/production-os-assets/assets/813c5d80-f312-4656-999b-d3cd0c6e6c24" property="twitter:image"/>
<meta content="All you need | shimoda" name="apple-mobile-web-app-title"/>
<meta content="telephone=no,email=no,address=no" name="format-detection"/>
<meta content="nointentdetection" name="chrome"/>
<meta content="https://allyouneed.studio.site/home-english" property="og:url"/>
<link data-hid="2c9d455" href="https://storage.googleapis.com/production-os-assets/assets/78f8ae36-821b-4b19-811c-b1edbb4c1b0f" rel="icon" type="image/png"/>
<link data-hid="74ef90c" href="https://storage.googleapis.com/production-os-assets/assets/78f8ae36-821b-4b19-811c-b1edbb4c1b0f" rel="apple-touch-icon" type="image/png"/><link as="script" crossorigin="" href="/_nuxt/entry.5e63065d.js" rel="modulepreload"/><link as="style" href="/_nuxt/entry.ccdb2b3a.css" rel="preload"/><link as="image" href="/_nuxt/close_circle.c7480f3c.svg" rel="prefetch" type="image/svg+xml"/><link as="image" href="/_nuxt/round_check.0ebac23f.svg" rel="prefetch" type="image/svg+xml"/><link as="script" crossorigin="" href="/_nuxt/LottieRenderer.895ce2d4.js" rel="prefetch"/><link as="script" crossorigin="" href="/_nuxt/error-404.88821c6d.js" rel="prefetch"/><link as="script" crossorigin="" href="/_nuxt/error-500.aa050b04.js" rel="prefetch"/><link href="/_nuxt/entry.ccdb2b3a.css" rel="stylesheet"/><style>.page-enter-active{transition:.6s cubic-bezier(.4,.4,0,1)}.page-leave-active{transition:.3s cubic-bezier(.4,.4,0,1)}.page-enter-from,.page-leave-to{opacity:0}</style><style>:root{--rebranding-loading-bg:#e5e5e5;--rebranding-loading-bar:#222}</style><style>.app[data-v-d12de11f]{align-items:center;flex-direction:column;height:100%;justify-content:center;width:100%}.title[data-v-d12de11f]{font-size:34px;font-weight:300;letter-spacing:2.45px;line-height:30px;margin:30px}</style><style>/*! * Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) * Copyright 2023 Fonticons, Inc. */.fa-brands,.fa-solid{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:var(--fa-display,inline-block);font-style:normal;font-variant:normal;line-height:1;text-rendering:auto}.fa-solid{font-family:Font Awesome\ 6 Free;font-weight:900}.fa-brands{font-family:Font Awesome\ 6 Brands;font-weight:400}:host,:root{--fa-style-family-classic:"Font Awesome 6 Free";--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";--fa-style-family-brands:"Font Awesome 6 Brands";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands"}@font-face{font-display:block;font-family:Font Awesome\ 6 Free;font-style:normal;font-weight:900;src:url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-solid-900.woff2) format("woff2"),url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-solid-900.ttf) format("truetype")}@font-face{font-display:block;font-family:Font Awesome\ 6 Brands;font-style:normal;font-weight:400;src:url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-brands-400.woff2) format("woff2"),url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-brands-400.ttf) format("truetype")}</style><style>.spinner[data-v-36413753]{animation:loading-spin-36413753 1s linear infinite;height:16px;pointer-events:none;width:16px}.spinner[data-v-36413753]:before{border-bottom:2px solid transparent;border-right:2px solid transparent;border-color:transparent currentcolor currentcolor transparent;border-style:solid;border-width:2px;opacity:.2}.spinner[data-v-36413753]:after,.spinner[data-v-36413753]:before{border-radius:50%;box-sizing:border-box;content:"";height:100%;position:absolute;width:100%}.spinner[data-v-36413753]:after{border-left:2px solid transparent;border-top:2px solid transparent;border-color:currentcolor transparent transparent currentcolor;border-style:solid;border-width:2px;opacity:1}@keyframes loading-spin-36413753{0%{transform:rotate(0deg)}to{transform:rotate(1turn)}}</style><style>.design-canvas__modal{height:100%;pointer-events:none;position:fixed;transition:none;width:100%;z-index:2}.design-canvas__modal:focus{outline:none}.design-canvas__modal.v-enter-active .studio-canvas,.design-canvas__modal.v-leave-active,.design-canvas__modal.v-leave-active .studio-canvas{transition:.4s cubic-bezier(.4,.4,0,1)}.design-canvas__modal.v-enter-active .studio-canvas *,.design-canvas__modal.v-leave-active .studio-canvas *{transition:none!important}.design-canvas__modal.isNone{transition:none}.design-canvas__modal .design-canvas__modal__base{height:100%;left:0;pointer-events:auto;position:fixed;top:0;transition:.4s cubic-bezier(.4,.4,0,1);width:100%;z-index:-1}.design-canvas__modal .studio-canvas{height:100%;pointer-events:none}.design-canvas__modal .studio-canvas>*{background:none!important;pointer-events:none}</style><style>.LoadMoreAnnouncer[data-v-4f7a7294]{height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;clip:rect(0,0,0,0);border-width:0;white-space:nowrap}</style><style>.TitleAnnouncer[data-v-692a2727]{height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px;clip:rect(0,0,0,0);border-width:0;white-space:nowrap}</style><style>.publish-studio-style[data-v-5a0c3720]{transition:.4s cubic-bezier(.4,.4,0,1)}</style><style>.product-font-style[data-v-51f515bd]{transition:.4s cubic-bezier(.4,.4,0,1)}</style><style>@font-face{font-family:grandam;font-style:normal;font-weight:400;src:url(https://storage.googleapis.com/studio-front/fonts/grandam.ttf) format("truetype")}@font-face{font-family:Material Icons;font-style:normal;font-weight:400;src:url(https://storage.googleapis.com/production-os-assets/assets/material-icons/1629704621943/MaterialIcons-Regular.eot);src:local("Material Icons"),local("MaterialIcons-Regular"),url(https://storage.googleapis.com/production-os-assets/assets/material-icons/1629704621943/MaterialIcons-Regular.woff2) format("woff2"),url(https://storage.googleapis.com/production-os-assets/assets/material-icons/1629704621943/MaterialIcons-Regular.woff) format("woff"),url(https://storage.googleapis.com/production-os-assets/assets/material-icons/1629704621943/MaterialIcons-Regular.ttf) format("truetype")}.StudioCanvas{display:flex;height:auto;min-height:100dvh}.StudioCanvas>.sd{min-height:100dvh;overflow:clip}a,abbr,address,article,aside,audio,b,blockquote,body,button,canvas,caption,cite,code,dd,del,details,dfn,div,dl,dt,em,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,input,ins,kbd,label,legend,li,main,mark,menu,nav,object,ol,p,pre,q,samp,section,select,small,span,strong,sub,summary,sup,table,tbody,td,textarea,tfoot,th,thead,time,tr,ul,var,video{border:0;font-family:sans-serif;line-height:1;list-style:none;margin:0;padding:0;text-decoration:none;-webkit-font-smoothing:antialiased;-webkit-backface-visibility:hidden;box-sizing:border-box;color:#333;transition:.3s cubic-bezier(.4,.4,0,1);word-spacing:1px}a:focus:not(:focus-visible),button:focus:not(:focus-visible),summary:focus:not(:focus-visible){outline:none}nav ul{list-style:none}blockquote,q{quotes:none}blockquote:after,blockquote:before,q:after,q:before{content:none}a,button{background:transparent;font-size:100%;margin:0;padding:0;vertical-align:baseline}ins{text-decoration:none}ins,mark{background-color:#ff9;color:#000}mark{font-style:italic;font-weight:700}del{text-decoration:line-through}abbr[title],dfn[title]{border-bottom:1px dotted;cursor:help}table{border-collapse:collapse;border-spacing:0}hr{border:0;border-top:1px solid #ccc;display:block;height:1px;margin:1em 0;padding:0}input,select{vertical-align:middle}textarea{resize:none}.clearfix:after{clear:both;content:"";display:block}[slot=after] button{overflow-anchor:none}</style><style>.sd{flex-wrap:nowrap;max-width:100%;pointer-events:all;z-index:0;-webkit-overflow-scrolling:touch;align-content:center;align-items:center;display:flex;flex:none;flex-direction:column;position:relative}.sd::-webkit-scrollbar{display:none}.sd,.sd.richText *{transition-property:all,--g-angle,--g-color-0,--g-position-0,--g-color-1,--g-position-1,--g-color-2,--g-position-2,--g-color-3,--g-position-3,--g-color-4,--g-position-4,--g-color-5,--g-position-5,--g-color-6,--g-position-6,--g-color-7,--g-position-7,--g-color-8,--g-position-8,--g-color-9,--g-position-9,--g-color-10,--g-position-10,--g-color-11,--g-position-11}input.sd,textarea.sd{align-content:normal}.sd[tabindex]:focus{outline:none}.sd[tabindex]:focus-visible{outline:1px solid;outline-color:Highlight;outline-color:-webkit-focus-ring-color}input[type=email],input[type=tel],input[type=text],select,textarea{-webkit-appearance:none}select{cursor:pointer}.frame{display:block;overflow:hidden}.frame>iframe{height:100%;width:100%}.frame .formrun-embed>iframe:not(:first-child){display:none!important}.image{position:relative}.image:before{background-position:50%;background-size:cover;border-radius:inherit;content:"";height:100%;left:0;pointer-events:none;position:absolute;top:0;transition:inherit;width:100%;z-index:-2}.sd.file{cursor:pointer;flex-direction:row;outline:2px solid transparent;outline-offset:-1px;overflow-wrap:anywhere;word-break:break-word}.sd.file:focus-within{outline-color:Highlight;outline-color:-webkit-focus-ring-color}.file>input[type=file]{opacity:0;pointer-events:none;position:absolute}.sd.icon,.sd.text{align-content:center;align-items:center;display:flex;flex-direction:row;justify-content:center;overflow:visible;overflow-wrap:anywhere;word-break:break-word}.material-icons{display:inline-block;font-family:Material Icons;font-size:24px;font-style:normal;font-weight:400;letter-spacing:normal;line-height:1;text-transform:none;white-space:nowrap;word-wrap:normal;direction:ltr;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased}.sd.material-symbols{font-style:normal;font-variation-settings:"FILL" var(--symbol-fill,0),"wght" var(--symbol-weight,400)}.sd.material-symbols.font-loading{height:24px;opacity:0;overflow:hidden;width:24px}.sd.material-symbols-outlined{font-family:Material Symbols Outlined}.sd.material-symbols-rounded{font-family:Material Symbols Rounded}.sd.material-symbols-sharp{font-family:Material Symbols Sharp}.sd.material-symbols-weight-100{--symbol-weight:100}.sd.material-symbols-weight-200{--symbol-weight:200}.sd.material-symbols-weight-300{--symbol-weight:300}.sd.material-symbols-weight-400{--symbol-weight:400}.sd.material-symbols-weight-500{--symbol-weight:500}.sd.material-symbols-weight-600{--symbol-weight:600}.sd.material-symbols-weight-700{--symbol-weight:700}.sd.material-symbols-fill{--symbol-fill:1}a,a.icon,a.text{-webkit-tap-highlight-color:rgba(0,0,0,.15)}.fixed{z-index:2}.sticky{z-index:1}.button{transition:.4s cubic-bezier(.4,.4,0,1)}.button,.link{cursor:pointer}.submitLoading{opacity:.5!important;pointer-events:none!important}.richText{display:block;word-break:break-word}.richText [data-thread],.richText a,.richText blockquote,.richText em,.richText h1,.richText h2,.richText h3,.richText h4,.richText li,.richText ol,.richText p,.richText p>code,.richText pre,.richText pre>code,.richText s,.richText strong,.richText table tbody,.richText table tbody tr,.richText table tbody tr>td,.richText table tbody tr>th,.richText u,.richText ul{backface-visibility:visible;color:inherit;font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;text-align:inherit}.richText p{display:block;margin:10px 0}.richText>p{min-height:1em}.richText img,.richText video{height:auto;max-width:100%;vertical-align:bottom}.richText h1{display:block;font-size:3em;font-weight:700;margin:20px 0}.richText h2{font-size:2em}.richText h2,.richText h3{display:block;font-weight:700;margin:10px 0}.richText h3{font-size:1em}.richText h4,.richText h5{font-weight:600}.richText h4,.richText h5,.richText h6{display:block;font-size:1em;margin:10px 0}.richText h6{font-weight:500}.richText [data-type=table]{overflow-x:auto}.richText [data-type=table] p{white-space:pre-line;word-break:break-all}.richText table{border:1px solid #f2f2f2;border-collapse:collapse;border-spacing:unset;color:#1a1a1a;font-size:14px;line-height:1.4;margin:10px 0;table-layout:auto}.richText table tr th{background:hsla(0,0%,96%,.5)}.richText table tr td,.richText table tr th{border:1px solid #f2f2f2;max-width:240px;min-width:100px;padding:12px}.richText table tr td p,.richText table tr th p{margin:0}.richText blockquote{border-left:3px solid rgba(0,0,0,.15);font-style:italic;margin:10px 0;padding:10px 15px}.richText [data-type=embed_code]{margin:20px 0;position:relative}.richText [data-type=embed_code]>.height-adjuster>.wrapper{position:relative}.richText [data-type=embed_code]>.height-adjuster>.wrapper[style*=padding-top] iframe{height:100%;left:0;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-sandbox=true]{display:block;overflow:hidden}.richText [data-type=embed_code][data-embed-code-type=instagram]>.height-adjuster>.wrapper[style*=padding-top]{padding-top:100%}.richText [data-type=embed_code][data-embed-code-type=instagram]>.height-adjuster>.wrapper[style*=padding-top] blockquote{height:100%;left:0;overflow:hidden;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-code-type=codepen]>.height-adjuster>.wrapper{padding-top:50%}.richText [data-type=embed_code][data-embed-code-type=codepen]>.height-adjuster>.wrapper iframe{height:100%;left:0;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-code-type=slideshare]>.height-adjuster>.wrapper{padding-top:56.25%}.richText [data-type=embed_code][data-embed-code-type=slideshare]>.height-adjuster>.wrapper iframe{height:100%;left:0;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-code-type=speakerdeck]>.height-adjuster>.wrapper{padding-top:56.25%}.richText [data-type=embed_code][data-embed-code-type=speakerdeck]>.height-adjuster>.wrapper iframe{height:100%;left:0;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-code-type=snapwidget]>.height-adjuster>.wrapper{padding-top:30%}.richText [data-type=embed_code][data-embed-code-type=snapwidget]>.height-adjuster>.wrapper iframe{height:100%;left:0;position:absolute;top:0;width:100%}.richText [data-type=embed_code][data-embed-code-type=firework]>.height-adjuster>.wrapper fw-embed-feed{-webkit-user-select:none;-moz-user-select:none;user-select:none}.richText [data-type=embed_code_empty]{display:none}.richText ul{margin:0 0 0 20px}.richText ul li{list-style:disc;margin:10px 0}.richText ul li p{margin:0}.richText ol{margin:0 0 0 20px}.richText ol li{list-style:decimal;margin:10px 0}.richText ol li p{margin:0}.richText hr{border-top:1px solid #ccc;margin:10px 0}.richText p>code{background:#eee;border:1px solid rgba(0,0,0,.1);border-radius:6px;display:inline;margin:2px;padding:0 5px}.richText pre{background:#eee;border-radius:6px;font-family:Menlo,Monaco,Courier New,monospace;margin:20px 0;padding:25px 35px;white-space:pre-wrap}.richText pre code{border:none;padding:0}.richText strong{color:inherit;display:inline;font-family:inherit;font-weight:900}.richText em{font-style:italic}.richText a,.richText u{text-decoration:underline}.richText a{color:#007cff;display:inline}.richText s{text-decoration:line-through}.richText [data-type=table_of_contents]{background-color:#f5f5f5;border-radius:2px;color:#616161;font-size:16px;list-style:none;margin:0;padding:24px 24px 8px;text-decoration:underline}.richText [data-type=table_of_contents] .toc_list{margin:0}.richText [data-type=table_of_contents] .toc_item{color:currentColor;font-size:inherit!important;font-weight:inherit;list-style:none}.richText [data-type=table_of_contents] .toc_item>a{border:none;color:currentColor;font-size:inherit!important;font-weight:inherit;text-decoration:none}.richText [data-type=table_of_contents] .toc_item>a:hover{opacity:.7}.richText [data-type=table_of_contents] .toc_item--1{margin:0 0 16px}.richText [data-type=table_of_contents] .toc_item--2{margin:0 0 16px;padding-left:2rem}.richText [data-type=table_of_contents] .toc_item--3{margin:0 0 16px;padding-left:4rem}.sd.section{align-content:center!important;align-items:center!important;flex-direction:column!important;flex-wrap:nowrap!important;height:auto!important;max-width:100%!important;padding:0!important;width:100%!important}.sd.section-inner{position:static!important}@property --g-angle{syntax:"<angle>";inherits:false;initial-value:180deg}@property --g-color-0{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-0{syntax:"<percentage>";inherits:false;initial-value:.01%}@property --g-color-1{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-1{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-2{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-2{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-3{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-3{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-4{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-4{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-5{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-5{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-6{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-6{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-7{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-7{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-8{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-8{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-9{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-9{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-10{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-10{syntax:"<percentage>";inherits:false;initial-value:100%}@property --g-color-11{syntax:"<color>";inherits:false;initial-value:transparent}@property --g-position-11{syntax:"<percentage>";inherits:false;initial-value:100%}</style><style>.snackbar[data-v-3129703d]{align-items:center;background:#fff;border:1px solid #ededed;border-radius:6px;box-shadow:0 16px 48px -8px rgba(0,0,0,.08),0 10px 25px -5px rgba(0,0,0,.11);display:flex;flex-direction:row;gap:8px;justify-content:space-between;left:50%;max-width:90vw;padding:16px 20px;position:fixed;top:32px;transform:translateX(-50%);-webkit-user-select:none;-moz-user-select:none;user-select:none;width:480px;z-index:9999}.snackbar.v-enter-active[data-v-3129703d],.snackbar.v-leave-active[data-v-3129703d]{transition:.4s cubic-bezier(.4,.4,0,1)}.snackbar.v-enter-from[data-v-3129703d],.snackbar.v-leave-to[data-v-3129703d]{opacity:0;transform:translate(-50%,-10px)}.snackbar .convey[data-v-3129703d]{align-items:center;display:flex;flex-direction:row;gap:8px;padding:0}.snackbar .convey .icon[data-v-3129703d]{background-position:50%;background-repeat:no-repeat;flex-shrink:0;height:24px;width:24px}.snackbar .convey .message[data-v-3129703d]{font-size:14px;font-style:normal;font-weight:400;line-height:20px;white-space:pre-line}.snackbar .convey.error .icon[data-v-3129703d]{background-image:url(/_nuxt/close_circle.c7480f3c.svg)}.snackbar .convey.error .message[data-v-3129703d]{color:#f84f65}.snackbar .convey.success .icon[data-v-3129703d]{background-image:url(/_nuxt/round_check.0ebac23f.svg)}.snackbar .convey.success .message[data-v-3129703d]{color:#111}.snackbar .button[data-v-3129703d]{align-items:center;border-radius:40px;color:#4b9cfb;display:flex;flex-shrink:0;font-family:Inter;font-size:12px;font-style:normal;font-weight:700;justify-content:center;line-height:16px;padding:4px 8px}.snackbar .button[data-v-3129703d]:hover{background:#f5f5f5}</style><style>a[data-v-60d33773]{align-items:center;border-radius:4px;bottom:20px;height:20px;justify-content:center;left:20px;perspective:300px;position:fixed;transition:0s linear;width:84px;z-index:2000}@media (hover:hover){a[data-v-60d33773]{transition:.4s cubic-bezier(.4,.4,0,1)}a[data-v-60d33773]:hover{height:32px;width:200px}}[data-v-60d33773] .custom-fill path{fill:var(--01abf230)}.fade-enter-active[data-v-60d33773],.fade-leave-active[data-v-60d33773]{transition:opacity .2s cubic-bezier(.4,.4,0,1)}.fade-enter[data-v-60d33773],.fade-leave-to[data-v-60d33773]{opacity:0}</style><link href="https://allyouneed.studio.site/home-english" rel="canonical"/><style id="fontawesome-styles">/*! * Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) * Copyright 2023 Fonticons, Inc. */.fa-brands,.fa-solid{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:var(--fa-display,inline-block);font-style:normal;font-variant:normal;line-height:1;text-rendering:auto}.fa-solid{font-family:Font Awesome\ 6 Free;font-weight:900}.fa-brands{font-family:Font Awesome\ 6 Brands;font-weight:400}:host,:root{--fa-style-family-classic:"Font Awesome 6 Free";--fa-font-solid:normal 900 1em/1 "Font Awesome 6 Free";--fa-style-family-brands:"Font Awesome 6 Brands";--fa-font-brands:normal 400 1em/1 "Font Awesome 6 Brands"}@font-face{font-display:block;font-family:Font Awesome\ 6 Free;font-style:normal;font-weight:900;src:url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-solid-900.woff2) format("woff2"),url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-solid-900.ttf) format("truetype")}@font-face{font-display:block;font-family:Font Awesome\ 6 Brands;font-style:normal;font-weight:400;src:url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-brands-400.woff2) format("woff2"),url(https://storage.googleapis.com/production-os-assets/assets/fontawesome/1629704621943/6.4.2/webfonts/fa-brands-400.ttf) format("truetype")}
.fa-instagram:before { content: "\f16d"; }
.fa-square-facebook:before { content: "\f082"; }
</style><link href="https://fonts.googleapis.com/css?display=swap&amp;family=Poppins%3A600" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?display=swap&amp;family=Poppins%3A400" rel="stylesheet"/><link href="https://fonts.googleapis.com/css?display=swap&amp;family=Lato%3A400" rel="stylesheet"/><script async="" data-h-load="" data-onload="true" defer="" onload="this.dataset.onload = true" src="//typesquare.com/3/tsst/script/ja/typesquare.js?5ad00062415c46a09fd67fd7ac1e024a&amp;fadein=-1"></script><style name="modal-test"></style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W3 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2538&fonts[str]=IiYoKSosLS4wMTIzNDU2Nzg5OkBBQ0RFRkdJTE1OT1BTVFVXWWFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6fuOAkg%3D%3D);
}</style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W6 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2539&fonts[str]=ISYnLC4wQUJDREVGR0lKTE1OT1BSU1RVV1lhYmNkZWZnaGlrbG1ub3BxcnN0dXZ3eXrjgajjgqLjgq%2Fjgrnjgrvjgr%2Fjg4vjg6Xjg6vjg6zjg7Pjg7zkuojkvJrlgpnlsYvmlr3mpoLnpL7ntITopoHoqK3pg6g%3D);
}</style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W3 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2538&fonts[str]=IiYoKSosLS4wMTIzNDU2Nzg5OkBBQ0RFRkdJTE1OT1BTVFVXWWFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6fuOAkg%3D%3D);
}</style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W6 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2539&fonts[str]=ISYnLC4wQUJDREVGR0lKTE1OT1BSU1RVV1lhYmNkZWZnaGlrbG1ub3BxcnN0dXZ3eXrjgajjgqLjgq%2Fjgrnjgrvjgr%2Fjg4vjg6Xjg6vjg6zjg7Pjg7zkuojkvJrlgpnlsYvmlr3mpoLnpL7ntITopoHoqK3pg6g%3D);
}</style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W3 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2538&fonts[str]=IiYoKSosLS4wMTIzNDU2Nzg5OkBBQ0RFRkdJTE1OT1BSU1RVV1lhYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5en7jgIHjgJLjgYTjgYjjgYvjgYzjgZ%2Fjgafjgavjga7jgonjg4jjg4njg5Xjg7zkuovku4rkvLTloLTlopflpI%2FlraPlrprlubTou4rpmZDpp5A%3D);
}</style><style class="ts-font" rel="stylesheet" type="text/css">@font-face {
  font-family: 'こぶりなゴシック W6 JIS2004';
  font-weight: Bold;
  src: url(//wf.typesquare.com/3/tsst/dist/ja/ts?condition=c2782c232e23952b6640b54451d03fa5&onetime_condition=&eid=5ad00062415c46a09fd67fd7ac1e024a&bw[name]=Chrome&bw[ftf]=0&bw[os]=Windows&location=allyouneed.studio.site%2Fhome-english&fonts[id]=2539&fonts[str]=ISYnLC4wQUJDREVGR0lKTE1OT1BSU1RVV1lhYmNkZWZnaGlrbG1ub3BxcnN0dXZ3eXrjgYTjgYrjgZfjgZnjgZvjgaTjgabjgajjgavjga7jgb7jgonjgpLjgqLjgq%2FjgrXjgrnjgrvjgr%2Fjg4Pjg4jjg4njg4vjg5Xjg5fjg6Xjg6njg6vjg6zjg7Pjg7zkuojkvJrlgpnli5%2FlpI%2FlraPlrprlsYvmlr3mpoLnn6XnpL7ntITopoHoqK3pg6jpmZDpm4Y%3D);
}</style></head>
<body><div id="__nuxt"><div><div class="container"><style data-v-5a0c3720="">.sd[data-s-626b3c93-8e37-4376-a7e7-2bff845e6631] { align-content: flex-start; align-items: flex-start; background: rgba(255, 255, 255, 0.74); display: flex; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: 153px; justify-content: space-between; left: 0; margin: 0 0 0 0; padding: 10px 40px 0px 40px; position: fixed; top: 0; width: 100%; --gap-h-626b3c93-8e37-4376-a7e7-2bff845e6631: 40px; --gap-v-626b3c93-8e37-4376-a7e7-2bff845e6631: 0px; --gap-uuid: 626b3c93-8e37-4376-a7e7-2bff845e6631; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-626b3c93-8e37-4376-a7e7-2bff845e6631] { align-content: center; align-items: center; display: flex; flex: none; height: 130px; justify-content: space-between; --gap-h-626b3c93-8e37-4376-a7e7-2bff845e6631: 40px; --gap-v-626b3c93-8e37-4376-a7e7-2bff845e6631: 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-626b3c93-8e37-4376-a7e7-2bff845e6631] { bottom: auto; display: flex; height: 86px; padding: 0px 20px 0px 30px; top: 0px; --gap-h-626b3c93-8e37-4376-a7e7-2bff845e6631: 40px; --gap-v-626b3c93-8e37-4376-a7e7-2bff845e6631: 0px; width: 100%; max-width: 100%; }
}.sd[data-s-30815b26-c41c-4d54-abaa-e35a4df5dbd9] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: calc(100% - (var(--gap-v-626b3c93-8e37-4376-a7e7-2bff845e6631) * 0)); justify-content: center; width: auto; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-30815b26-c41c-4d54-abaa-e35a4df5dbd9] { flex: none; height: auto; margin: 16px 0px 0px 0px; width: 192px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-30815b26-c41c-4d54-abaa-e35a4df5dbd9] { flex: none; height: 73px; width: 116px; max-width: 100%; }
}.sd[data-s-03c83ec7-0569-4ba4-9546-8056e67597af] { height: auto; width: 192px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-03c83ec7-0569-4ba4-9546-8056e67597af] { flex: none; height: auto; width: 148px; max-width: 100%; }
}.sd[data-s-e1eb933e-c0af-409a-b579-4bd72b30cfbe] { align-content: center; align-items: center; background: #ea3c3c; border-radius: 50px; display: none; flex-direction: column; flex-wrap: nowrap; height: 98px; justify-content: center; width: 106px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-e1eb933e-c0af-409a-b579-4bd72b30cfbe] { background: #474bff; display: flex; flex: none; height: 60px; width: 60px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-e1eb933e-c0af-409a-b579-4bd72b30cfbe] { display: flex; flex: none; height: 50px; width: 50px; max-width: 100%; }
}.sd[data-s-7b0957c6-20b4-4994-bba9-d672493efb22] { color: #FFFFFF; font-size: 24px; }.sd[data-s-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: calc(100% - (var(--gap-v-626b3c93-8e37-4376-a7e7-2bff845e6631) * 0)); justify-content: center; width: auto; --gap-h-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 40px; --gap-v-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 0px; --gap-uuid: e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f] { display: none; --gap-h-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 40px; --gap-v-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f] { display: none; --gap-h-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 40px; --gap-v-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f: 0px; }
}.sd[data-s-83c708ff-55d6-4f4e-b20b-a6331d0106c1] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }
.sd[data-s-83c708ff-55d6-4f4e-b20b-a6331d0106c1]:hover { opacity: 1; }.sd[data-s-21c66407-c485-4439-a19e-cc1a13d9b0a1] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: 40px; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-bab849cf-7b8f-4ecf-8671-55c3c07377dd] { background: #000000; bottom: 0px; flex: none; height: 2px; left: 0px; margin: 0 0 0 0; position: absolute; right: 0px; top: auto; width: 0%; max-width: 0%; }
.sd[data-s-83c708ff-55d6-4f4e-b20b-a6331d0106c1]:hover .sd[data-s-bab849cf-7b8f-4ecf-8671-55c3c07377dd] { flex: none; width: 100%; max-width: 100%; }.sd[data-s-978c4cba-2f93-481b-951f-79e15e92f8b5] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }
.sd[data-s-978c4cba-2f93-481b-951f-79e15e92f8b5]:hover { opacity: 1; }.sd[data-s-60e95b71-261b-4f3e-8296-e617ab7e91f1] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: 40px; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-dd2b0e84-71f6-4f6c-9f94-7a35cc56a6fd] { background: #000000; bottom: 0px; flex: none; height: 2px; left: 0px; margin: 0 0 0 0; position: absolute; right: 0px; top: auto; width: 0%; max-width: 0%; }
.sd[data-s-978c4cba-2f93-481b-951f-79e15e92f8b5]:hover .sd[data-s-dd2b0e84-71f6-4f6c-9f94-7a35cc56a6fd] { flex: none; width: 100%; max-width: 100%; }.sd[data-s-f7d69582-1678-478b-8e1d-662e0e7e3687] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }
.sd[data-s-f7d69582-1678-478b-8e1d-662e0e7e3687]:hover { opacity: 1; }.sd[data-s-c306ca52-b071-446a-952d-9879ea938334] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: 40px; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-4055dbd9-8d5a-4ba4-af8c-9f0a3da13b76] { background: #000000; bottom: 0px; flex: none; height: 2px; left: 0px; margin: 0 0 0 0; position: absolute; right: 0px; top: auto; width: 0%; max-width: 0%; }
.sd[data-s-f7d69582-1678-478b-8e1d-662e0e7e3687]:hover .sd[data-s-4055dbd9-8d5a-4ba4-af8c-9f0a3da13b76] { flex: none; width: 100%; max-width: 100%; }.sd[data-s-27a49796-792c-4254-9fb7-8e0186d7a13f] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }
.sd[data-s-27a49796-792c-4254-9fb7-8e0186d7a13f]:hover { opacity: 1; }.sd[data-s-abea6909-3545-411a-af4c-7a5e54dfac48] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: 40px; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-cf19597e-d899-4080-8883-3d55f8562c78] { background: #000000; bottom: 0px; flex: none; height: 2px; left: 0px; margin: 0 0 0 0; position: absolute; right: 0px; top: auto; width: 0%; max-width: 0%; }
.sd[data-s-27a49796-792c-4254-9fb7-8e0186d7a13f]:hover .sd[data-s-cf19597e-d899-4080-8883-3d55f8562c78] { flex: none; width: 100%; max-width: 100%; }.sd[data-s-87295b61-5055-431b-bf4c-f4e9a8f005e7] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }
.sd[data-s-87295b61-5055-431b-bf4c-f4e9a8f005e7]:hover { opacity: 1; }.sd[data-s-5cbc9a2f-1ecf-4ef1-b4ba-9e0232a80b63] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: 40px; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-99bef3db-273e-44be-ab58-66584ed9bbf3] { background: #000000; bottom: 0px; flex: none; height: 2px; left: 0px; margin: 0 0 0 0; position: absolute; right: 0px; top: auto; width: 0%; max-width: 0%; }
.sd[data-s-87295b61-5055-431b-bf4c-f4e9a8f005e7]:hover .sd[data-s-99bef3db-273e-44be-ab58-66584ed9bbf3] { flex: none; width: 100%; max-width: 100%; }.sd[data-s-46dfe9c8-a935-4e53-8d1e-b601eda36234] { align-content: center; align-items: center; background: #474bff; border-radius: 25px; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: center; padding: 0px 20px 0px 20px; width: auto; max-width: 100%; }
.sd[data-s-46dfe9c8-a935-4e53-8d1e-b601eda36234]:hover { background: #FFFFFF; }.sd[data-s-db83e8a1-ca0c-492f-a9fb-c0ff47dbeaca] { color: #FFFFFF; font-size: 24px; }
.sd[data-s-46dfe9c8-a935-4e53-8d1e-b601eda36234]:hover .sd[data-s-db83e8a1-ca0c-492f-a9fb-c0ff47dbeaca] { color: #474bff; }.sd[data-s-b53dac1c-6dc0-4088-9ff6-2f49b032f0c9] { color: #FFFFFF; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; margin: 0px 0px 0px 5px; text-align: left; width: auto; max-width: calc(100% - 5px); justify-content: flex-start; }
.sd[data-s-46dfe9c8-a935-4e53-8d1e-b601eda36234]:hover .sd[data-s-b53dac1c-6dc0-4088-9ff6-2f49b032f0c9] { color: #474bff; }</style><!-- --><style data-v-5a0c3720="">.sd[data-s-9af3a759-a25a-4214-86ca-c180325bf7bf] { align-content: center; align-items: center; background: #FFFFFF; border-radius: 0px 150px 0px 0px; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 40px; height: auto; justify-content: flex-start; padding: 59px 0px; width: 100%; --gap-h-9af3a759-a25a-4214-86ca-c180325bf7bf: 0px; --gap-v-9af3a759-a25a-4214-86ca-c180325bf7bf: 40px; --gap-uuid: 9af3a759-a25a-4214-86ca-c180325bf7bf; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-9af3a759-a25a-4214-86ca-c180325bf7bf] { border-radius: 0px 100px 0px 0px; --gap-h-9af3a759-a25a-4214-86ca-c180325bf7bf: 0px; --gap-v-9af3a759-a25a-4214-86ca-c180325bf7bf: 40px; }
}
@media screen and (max-width: 540px){
.sd[data-s-9af3a759-a25a-4214-86ca-c180325bf7bf] { border-radius: 0px 60px 0px 0px; --gap-h-9af3a759-a25a-4214-86ca-c180325bf7bf: 0px; --gap-v-9af3a759-a25a-4214-86ca-c180325bf7bf: 40px; }
}.sd[data-s-d9ae5ca7-bde8-4590-94be-d9c99412ad33] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: auto; justify-content: flex-start; padding: 0px 40px; width: calc(100% - (var(--gap-h-9af3a759-a25a-4214-86ca-c180325bf7bf) * 0)); --gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33: 40px; --gap-v-d9ae5ca7-bde8-4590-94be-d9c99412ad33: 0px; --gap-uuid: d9ae5ca7-bde8-4590-94be-d9c99412ad33; max-width: calc(100% - (var(--gap-h-9af3a759-a25a-4214-86ca-c180325bf7bf) * 0)); }
@media screen and (max-width: 540px){
.sd[data-s-d9ae5ca7-bde8-4590-94be-d9c99412ad33] { flex-direction: column; flex-wrap: nowrap; padding: 0px 20px; --gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33: 0px; --gap-v-d9ae5ca7-bde8-4590-94be-d9c99412ad33: 40px; }
}.sd[data-s-45b6e9c9-22cc-4a65-89b2-ed74fc3abb2a] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-start; width: calc(50% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0.5)); max-width: calc(50% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0.5)); }
@media screen and (max-width: 540px){
.sd[data-s-45b6e9c9-22cc-4a65-89b2-ed74fc3abb2a] { flex: none; width: calc(100% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0)); max-width: calc(100% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0)); }
}.sd[data-s-e813169e-f643-4de0-8998-1e9ff3a3e005] { background: rgba(0,0,0,0.0); height: auto; width: 160px; max-width: 100%; }.sd[data-s-0f28ac89-c417-4660-a4cb-1e13a05d1503] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 16px; font-weight: 400; height: auto; line-height: 1.4; margin: 20px 0px 0px 0px; text-align: center; width: 100%; max-width: 100%; justify-content: center; }.sd[data-s-187071e0-52db-4c0b-8f18-75b8fcd031d2] { background: rgb(251, 31, 31); color: #333; flex: none; font-family: var(--s-font-bd94a845); font-size: 20px; font-weight: 400; height: auto; line-height: 1.4; margin: 5px 0px 0px 0px; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }.sd[data-s-90a13d6f-a5ec-46c7-b882-f218536971fd] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; margin: 5px 0px 0px 0px; text-align: center; width: 100%; max-width: 100%; justify-content: center; }.sd[data-s-f1fa17e4-7fcd-4045-a474-1a895b7998b1] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: calc(50% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0.5)); max-width: calc(50% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0.5)); }
@media screen and (max-width: 540px){
.sd[data-s-f1fa17e4-7fcd-4045-a474-1a895b7998b1] { flex: none; width: calc(100% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0)); max-width: calc(100% - (var(--gap-h-d9ae5ca7-bde8-4590-94be-d9c99412ad33) * 0)); }
}.sd[data-s-37486066-2caa-4f3a-8cd1-d0e84d9131a2] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; padding: 0px; width: 100%; max-width: 100%; }
.sd[data-s-37486066-2caa-4f3a-8cd1-d0e84d9131a2]:hover { opacity: 1; }.sd[data-s-6ea98dce-ba6d-43b1-b414-fa555c61e2f2] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-312e643c-b41d-4716-a93e-25821ff789ac] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-37486066-2caa-4f3a-8cd1-d0e84d9131a2]:hover .sd[data-s-312e643c-b41d-4716-a93e-25821ff789ac] { transform: translate(10px, 0px); }.sd[data-s-5f9ce921-c340-43a7-bc24-20261364cf69] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-5f9ce921-c340-43a7-bc24-20261364cf69]:hover { opacity: 1; }.sd[data-s-5c380498-96d7-4554-a4dc-0512baec7785] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-e6e7c0b4-6c02-436d-b19f-10c05c6fbb23] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-5f9ce921-c340-43a7-bc24-20261364cf69]:hover .sd[data-s-e6e7c0b4-6c02-436d-b19f-10c05c6fbb23] { transform: translate(10px, 0px); }.sd[data-s-5d1a0ce5-1930-4562-8174-7e0f7a43f4da] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-5d1a0ce5-1930-4562-8174-7e0f7a43f4da]:hover { opacity: 1; }.sd[data-s-6f79c737-59d1-4572-bfc5-08c8c9ee706b] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-2eb17631-c3b1-4b78-a899-68bbe7360984] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-5d1a0ce5-1930-4562-8174-7e0f7a43f4da]:hover .sd[data-s-2eb17631-c3b1-4b78-a899-68bbe7360984] { transform: translate(10px, 0px); }.sd[data-s-17ff5003-7f21-416e-8f43-170dd19bf7ef] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-17ff5003-7f21-416e-8f43-170dd19bf7ef]:hover { opacity: 1; }.sd[data-s-3c7a9639-aaaf-41c3-992a-0dfbfef5ee63] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-a74d958c-6498-4621-85f3-0f7ac7951c65] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-17ff5003-7f21-416e-8f43-170dd19bf7ef]:hover .sd[data-s-a74d958c-6498-4621-85f3-0f7ac7951c65] { transform: translate(10px, 0px); }.sd[data-s-48826842-862b-4217-b700-886f2d5c348e] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-48826842-862b-4217-b700-886f2d5c348e]:hover { opacity: 1; }.sd[data-s-7f462786-0876-43f5-91c7-de6ca5870c77] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-0b117aeb-dc80-420a-87b8-f61e623d7bb1] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; padding: 0px; }
.sd[data-s-48826842-862b-4217-b700-886f2d5c348e]:hover .sd[data-s-0b117aeb-dc80-420a-87b8-f61e623d7bb1] { transform: translate(10px, 0px); }.sd[data-s-d5da77f6-39dc-4094-90e9-4a2f9344b61c] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-d5da77f6-39dc-4094-90e9-4a2f9344b61c]:hover { opacity: 1; }.sd[data-s-ca61848e-30a7-4395-bc57-403f91676d7e] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-9c9ca779-720a-41db-b9ad-bbc7190f41dd] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-d5da77f6-39dc-4094-90e9-4a2f9344b61c]:hover .sd[data-s-9c9ca779-720a-41db-b9ad-bbc7190f41dd] { transform: translate(10px, 0px); }.sd[data-s-11e7f0ce-b4ea-4fde-95ae-8cab5b14e1e0] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: 50px; justify-content: space-between; width: 100%; max-width: 100%; }
.sd[data-s-11e7f0ce-b4ea-4fde-95ae-8cab5b14e1e0]:hover { opacity: 1; }.sd[data-s-8b0b8f15-36a0-4306-8063-e6c7deb420d6] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-410924c2-0f9d-49fa-8267-cf0ee8006a2a] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; }
.sd[data-s-11e7f0ce-b4ea-4fde-95ae-8cab5b14e1e0]:hover .sd[data-s-410924c2-0f9d-49fa-8267-cf0ee8006a2a] { transform: translate(10px, 0px); }.sd[data-s-23c87a91-0838-42ec-8fc4-f8ce1a81e6e6] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: space-between; padding: 0px 40px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-23c87a91-0838-42ec-8fc4-f8ce1a81e6e6] { padding: 0px 20px; }
}.sd[data-s-5feffac3-1485-4915-bfd1-e5081d9f330c] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-bd94a845); font-size: 13px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-4b0de1a8-a816-43f7-a4c8-30b1500ce689] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; gap: 20px; height: auto; justify-content: center; width: auto; --gap-h-4b0de1a8-a816-43f7-a4c8-30b1500ce689: 20px; --gap-v-4b0de1a8-a816-43f7-a4c8-30b1500ce689: 0px; --gap-uuid: 4b0de1a8-a816-43f7-a4c8-30b1500ce689; max-width: 100%; }.sd[data-s-88de538a-d2d2-4a52-a06e-40a229852f72] { background: rgba(0,0,0,0.0); color: #000000; font-size: 24px; margin: 0px 0px 0px 0px; max-width: 100%; }.sd[data-s-73b51265-5e29-492d-857f-6dd0236c6fe0] { background: rgba(0,0,0,0.0); color: #333; font-size: 24px; margin: 0px 0px 0px 0px; max-width: 100%; }</style><!-- --><style data-v-5a0c3720="">.sd[data-s-b9f0bc5b-657c-4067-9226-82c94e86d992] { align-content: center; align-items: center; background: #FFFFFF; flex-direction: column; flex-wrap: nowrap; height: 100%; justify-content: flex-start; width: 100%; --gap-uuid: b9f0bc5b-657c-4067-9226-82c94e86d992; --gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992: 0px; --gap-v-b9f0bc5b-657c-4067-9226-82c94e86d992: 0px; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-b9f0bc5b-657c-4067-9226-82c94e86d992] { gap: 0px; --gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992: 0px; --gap-v-b9f0bc5b-657c-4067-9226-82c94e86d992: 0px; }
}.sd[data-s-8e34e82f-86d9-461d-a287-c40165610ddd] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: row; flex-wrap: wrap; height: 100vh; justify-content: center; margin: 0px 0px 0px 0px; padding: 0 0; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }.sd[data-s-section-inner-8e34e82f-86d9-461d-a287-c40165610ddd] { padding: 0 0; width: 100%; height: 100vh; flex-direction: row; flex-wrap: wrap; align-content: center; align-items: center; justify-content: center; max-width: 100%; }.sd[data-s-bc8ad9fe-3dac-48a7-b7cd-fc9d29892062] { background: rgba(0,0,0,0.0); flex: none; height: 100%; width: 30%; max-width: 30%; }
@media screen and (max-width: 840px){
.sd[data-s-bc8ad9fe-3dac-48a7-b7cd-fc9d29892062] { flex: none; width: 40%; max-width: 40%; height: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-bc8ad9fe-3dac-48a7-b7cd-fc9d29892062] { background: rgba(0,0,0,0.0); }
}.sd[data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1] { background: rgba(0,0,0,0.0); border-radius: 0px 0px 0px 150px; flex: none; height: 100%; width: 70%; max-width: 70%; }
.sd[data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1]:before {  }
@media screen and (max-width: 840px){
.sd[data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1] { flex: none; width: 60%; max-width: 60%; height: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1]:before { background-size: cover; }
.sd[data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1] { margin: 0px 0px 0px 0px; width: 60%; max-width: 60%; height: 100%; }
}.sd[data-s-fa94f79a-18ef-4159-ae3a-2872ecb101eb] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); display: flex; flex: none; flex-direction: column; flex-wrap: nowrap; height: 100%; justify-content: center; left: 120px; margin: 0 0 0 0; opacity: 1; position: absolute; right: auto; top: 0; width: 752px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-fa94f79a-18ef-4159-ae3a-2872ecb101eb] { border-radius: 0px 0px 0px 100px; display: flex; left: 40px; margin: 0 0 0 0; position: absolute; top: 0; }
}
@media screen and (max-width: 540px){
.sd[data-s-fa94f79a-18ef-4159-ae3a-2872ecb101eb] { background: rgba(0,0,0,0.0); display: flex; left: 20px; }
}.sd[data-s-2b6974ae-3964-413f-b1ea-ac5c6eadadc0] { align-content: center; align-items: center; flex-direction: column; flex-wrap: nowrap; height: 154px; justify-content: center; padding: 0px; transform: skew(0deg, -8deg); width: 100%; max-width: 100%; }.sd[data-s-afd551a5-cfc8-4823-a88f-398434bf74fc] { color: #333; flex: none; font-family: var(--s-font-7d8084a9); font-size: 110px; font-weight: 600; height: 110px; letter-spacing: -0.07em; line-height: 1; text-align: left; width: 94%; max-width: 94%; justify-content: flex-start; }
.sd[data-s-afd551a5-cfc8-4823-a88f-398434bf74fc].appear { opacity: 0; transition-delay: 800ms; }
.sd[data-s-afd551a5-cfc8-4823-a88f-398434bf74fc].appear-active { transition-delay: 800ms; }
@media screen and (max-width: 840px){
.sd[data-s-afd551a5-cfc8-4823-a88f-398434bf74fc] { flex: none; font-size: 72px; width: 706.88px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-afd551a5-cfc8-4823-a88f-398434bf74fc] { flex: none; font-size: 48px; width: 706.88px; }
}.sd[data-s-22479ede-b76e-408e-bbd8-4a9b4733253f] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: 110px; justify-content: flex-start; margin: -110px 0px 0px 0px; padding: 0px; width: 660px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-22479ede-b76e-408e-bbd8-4a9b4733253f] { height: 132px; width: 727px; max-width: 100%; }
}.sd[data-s-93512e98-babe-440b-866c-2d3c9d3eb06a] { align-content: center; align-items: center; flex: none; flex-direction: column; flex-wrap: nowrap; height: 154px; justify-content: center; margin: -30px 0px 0px 0px; padding: 0px; transform: skew(0deg, -8deg); width: 691.2px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-93512e98-babe-440b-866c-2d3c9d3eb06a] { flex: none; margin: -60px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-caa2e456-c0cf-485d-8562-7439ee6b345d] { color: #333; flex: none; font-family: var(--s-font-7d8084a9); font-size: 75px; font-weight: 600; height: 100%; letter-spacing: 0.08em; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
.sd[data-s-caa2e456-c0cf-485d-8562-7439ee6b345d].appear { opacity: 0; transition-delay: 800ms; }
.sd[data-s-caa2e456-c0cf-485d-8562-7439ee6b345d].appear-active { transition-delay: 800ms; }
@media screen and (max-width: 840px){
.sd[data-s-caa2e456-c0cf-485d-8562-7439ee6b345d] { flex: none; font-size: 50px; width: 70%; max-width: 70%; height: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-caa2e456-c0cf-485d-8562-7439ee6b345d] { font-size: 30px; }
}.sd[data-s-c0b01087-faf6-459a-9302-d6d103eb2ba8] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: 110px; justify-content: flex-start; margin: -110px 0px 0px 0px; padding: 0px; width: 440px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-c0b01087-faf6-459a-9302-d6d103eb2ba8] { margin: -80px 0px 0px 0px; max-width: 100%; }
}.sd[data-s-5703992c-eb95-4648-b164-935f951ecac6] { background: rgba(23, 249, 135, 0); flex: none; height: 100%; width: 660px; max-width: 100%; }
.sd[data-s-5703992c-eb95-4648-b164-935f951ecac6].appear { flex: none; transition-delay: 800ms; transition-duration: 400ms; width: 0%; }
.sd[data-s-5703992c-eb95-4648-b164-935f951ecac6].appear-active { transition-delay: 800ms; transition-duration: 400ms; }.sd[data-s-381515d9-e7ca-4e30-919d-a420fee64f8d] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: row; flex-wrap: wrap; height: auto; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 50px 40px; width: 1280px; --gap-uuid: 381515d9-e7ca-4e30-919d-a420fee64f8d; --gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; --gap-v-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-381515d9-e7ca-4e30-919d-a420fee64f8d] { flex: none; flex-direction: column; flex-wrap: nowrap; gap: 0px; height: auto; padding: 0px 40px; --gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; --gap-v-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-381515d9-e7ca-4e30-919d-a420fee64f8d] { flex: none; height: auto; padding: 0px 20px; }
}.sd[data-s-section-inner-381515d9-e7ca-4e30-919d-a420fee64f8d] { padding: 50px 40px; width: 1280px; height: auto; flex-direction: row; flex-wrap: wrap; align-content: center; align-items: center; justify-content: flex-start; --gap-uuid: 381515d9-e7ca-4e30-919d-a420fee64f8d; --gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; --gap-v-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-381515d9-e7ca-4e30-919d-a420fee64f8d] { padding: 0px 40px; height: auto; flex-direction: column; flex-wrap: nowrap; gap: 0px; --gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; --gap-v-381515d9-e7ca-4e30-919d-a420fee64f8d: 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-381515d9-e7ca-4e30-919d-a420fee64f8d] { padding: 0px 20px; height: auto; flex-direction: column; }
}.sd[data-s-28b29881-0054-4096-bf1e-0a74f973486c] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; gap: 70px; height: auto; justify-content: center; margin: 0px 40px 47px 0px; width: calc(50% - 40px - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.5)); --gap-h-28b29881-0054-4096-bf1e-0a74f973486c: 0px; --gap-v-28b29881-0054-4096-bf1e-0a74f973486c: 70px; --gap-uuid: 28b29881-0054-4096-bf1e-0a74f973486c; max-width: calc(50% - 40px - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.5)); }
@media screen and (max-width: 840px){
.sd[data-s-28b29881-0054-4096-bf1e-0a74f973486c] { flex: none; margin: 0px 0px 80px 0px; width: calc(100% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0)); --gap-h-28b29881-0054-4096-bf1e-0a74f973486c: 0px; --gap-v-28b29881-0054-4096-bf1e-0a74f973486c: 70px; max-width: calc(100% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0)); }
}.sd[data-s-7946b935-7fbb-4405-a21b-de6fa2dc0240] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 48px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: calc(100% - (var(--gap-h-28b29881-0054-4096-bf1e-0a74f973486c) * 0)); max-width: calc(100% - (var(--gap-h-28b29881-0054-4096-bf1e-0a74f973486c) * 0)); justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-7946b935-7fbb-4405-a21b-de6fa2dc0240] { font-size: 28px; }
}.sd[data-s-47ecc7c9-5825-4d2e-a745-ecc1a1cee871] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 18px; font-weight: 400; height: auto; line-height: 2; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-47ecc7c9-5825-4d2e-a745-ecc1a1cee871] { font-size: 16px; }
}.sd[data-s-e0444394-7ff4-4ee1-a7bc-87b94f1a92e7] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 0px 0px 0px 40px; width: calc(50% - 40px - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.5)); max-width: calc(50% - 40px - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.5)); }
@media screen and (max-width: 840px){
.sd[data-s-e0444394-7ff4-4ee1-a7bc-87b94f1a92e7] { flex: none; margin: 0px 0px 0px 0px; width: calc(75% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.24999999999999994)); max-width: calc(75% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.24999999999999994)); }
}
@media screen and (max-width: 540px){
.sd[data-s-e0444394-7ff4-4ee1-a7bc-87b94f1a92e7] { flex: none; width: calc(100% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0)); max-width: calc(100% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0)); }
}.sd[data-s-2e0cc15a-66eb-4dc0-b20f-4038707e8753] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 0px solid #000; border-left: 0px solid #000; border-radius: 24px; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: column; flex-wrap: nowrap; height: 200px; justify-content: center; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-2e0cc15a-66eb-4dc0-b20f-4038707e8753] { border-radius: 30px; }
}.sd[data-s-e67c0cce-4225-4551-9d50-484f198d7fe5] { border-radius: 50px; flex: none; height: 100%; width: 50%; max-width: 50%; }
.sd[data-s-e67c0cce-4225-4551-9d50-484f198d7fe5].appear { transform: scale(0, 0); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-e67c0cce-4225-4551-9d50-484f198d7fe5].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-e67c0cce-4225-4551-9d50-484f198d7fe5]:before { background-size: 320px; }.sd[data-s-6a9564b4-5e7e-4471-85bb-fd64af0e2337] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: column; flex-wrap: nowrap; height: 250px; justify-content: center; margin: -70px 0px 0px 0px; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-6a9564b4-5e7e-4471-85bb-fd64af0e2337] { border-radius: 0px; }
}.sd[data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43] { border-radius: 50px; flex: none; height: 100%; width: 60%; max-width: 60%; }
.sd[data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43].appear { transform: scale(0, 0); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43]:before { background-position: center top; background-size: 390px; }
@media screen and (max-width: 540px){
.sd[data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43] { border-radius: 30px; }
}.sd[data-s-6eaac50e-5e34-4209-af24-51e87a273ce3] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: column; flex-wrap: nowrap; height: 250px; justify-content: center; margin: -70px 0px 0px 0px; width: 100%; max-width: 100%; }.sd[data-s-bec2f327-a55c-4629-8122-d02980b594f8] { border-radius: 50px; flex: none; height: 100%; width: 317px; max-width: 100%; }
.sd[data-s-bec2f327-a55c-4629-8122-d02980b594f8].appear { transform: scale(0, 0); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-bec2f327-a55c-4629-8122-d02980b594f8].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-bec2f327-a55c-4629-8122-d02980b594f8]:before { background-position: center top; background-size: 320px; }
@media screen and (max-width: 540px){
.sd[data-s-bec2f327-a55c-4629-8122-d02980b594f8] { border-radius: 30px; }
}.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19] { align-content: center; align-items: center; background: #474bff; border-radius: 32px; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 40px; justify-content: center; width: calc(41% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.59)); --gap-h-38e936a7-88a2-4694-97fb-8acc50156d19: 10px; --gap-v-38e936a7-88a2-4694-97fb-8acc50156d19: 0px; --gap-uuid: 38e936a7-88a2-4694-97fb-8acc50156d19; max-width: calc(41% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.59)); }
.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19].appear { background: rgba(71, 75, 255, 0.61); --gap-h-38e936a7-88a2-4694-97fb-8acc50156d19: 10px; --gap-v-38e936a7-88a2-4694-97fb-8acc50156d19: 0px; }
.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19].appear-active {  }
.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19]:hover { background: rgba(71, 75, 255, 0.65); --gap-h-38e936a7-88a2-4694-97fb-8acc50156d19: 10px; --gap-v-38e936a7-88a2-4694-97fb-8acc50156d19: 0px; }
@media screen and (max-width: 840px){
.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19] { margin: 60px 0px 60px 0px; --gap-h-38e936a7-88a2-4694-97fb-8acc50156d19: 10px; --gap-v-38e936a7-88a2-4694-97fb-8acc50156d19: 0px; width: calc(41% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.59)); max-width: calc(41% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.59)); }
}
@media screen and (max-width: 540px){
.sd[data-s-38e936a7-88a2-4694-97fb-8acc50156d19] { flex: none; height: auto; margin: 60px 0px 60px 0px; width: calc(93% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.06999999999999994)); --gap-h-38e936a7-88a2-4694-97fb-8acc50156d19: 10px; --gap-v-38e936a7-88a2-4694-97fb-8acc50156d19: 0px; max-width: calc(93% - (var(--gap-h-381515d9-e7ca-4e30-919d-a420fee64f8d) * 0.06999999999999994)); }
}.sd[data-s-d1cbc4d5-3931-41e6-a907-3f4d2f56b60c] { color: #FFFFFF; font-size: 28px; }
@media screen and (max-width: 540px){
.sd[data-s-d1cbc4d5-3931-41e6-a907-3f4d2f56b60c] { font-size: 24px; }
}.sd[data-s-6aa5013b-07ee-4341-b8cd-20f77599fdf6] { color: #FFFFFF; font-family: var(--s-font-d39b4375); font-size: 28px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-6aa5013b-07ee-4341-b8cd-20f77599fdf6] { font-size: 24px; }
}.sd[data-s-ebe6b342-9750-4c20-af59-4afb68f1decb] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 80px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-ebe6b342-9750-4c20-af59-4afb68f1decb] { padding: 0px 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-ebe6b342-9750-4c20-af59-4afb68f1decb] { padding: 0px 0px; }
}.sd[data-s-section-inner-ebe6b342-9750-4c20-af59-4afb68f1decb] { padding: 80px 0px; width: 100%; height: auto; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: center; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-ebe6b342-9750-4c20-af59-4afb68f1decb] { padding: 0px 0px; flex-direction: column; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-ebe6b342-9750-4c20-af59-4afb68f1decb] { padding: 0px 0px; flex-direction: column; }
}.sd[data-s-0da8802e-ace1-46fe-96bd-ecfc6ac1ea31] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-0da8802e-ace1-46fe-96bd-ecfc6ac1ea31] { padding: 0px 20px; }
}.sd[data-s-44ba734f-fb9c-4a69-9fb2-ecbd17881ff0] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-44ba734f-fb9c-4a69-9fb2-ecbd17881ff0] { font-size: 20px; }
}.sd[data-s-ecd0eed9-546a-46a9-9e83-86cf12dce6a0] { background: rgba(0,0,0,0.0); color: #333; font-family: var(--s-font-d39b4375); font-size: 144px; font-weight: 400; height: auto; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -20px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-ecd0eed9-546a-46a9-9e83-86cf12dce6a0] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-ecd0eed9-546a-46a9-9e83-86cf12dce6a0] { font-size: 55px; margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-cf27b130-760f-4c90-bb5c-9023c574397d] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-end; width: 100%; max-width: 100%; }.sd[data-s-8dbe8b45-3ac7-4ebd-8401-dd2d65d15312] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: 100%; max-width: 100%; }.sd[data-s-250ae657-bb76-4f54-b5f6-dddd2a095d00] { align-content: flex-start; align-items: flex-start; background: #fff3ce; border-radius: 0px 50px 0px 50px; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 0px -300px 0px 0px; padding: 80px 300px 80px 0px; width: calc(50% - -300px); max-width: calc(50% - -300px); }
@media screen and (max-width: 840px){
.sd[data-s-250ae657-bb76-4f54-b5f6-dddd2a095d00] { align-content: flex-end; align-items: flex-end; flex: none; justify-content: center; margin: 0px 120px 0px 0px; padding: 80px 0px 240px 0px; width: calc(100% - 120px); max-width: calc(100% - 120px); }
}
@media screen and (max-width: 540px){
.sd[data-s-250ae657-bb76-4f54-b5f6-dddd2a095d00] { background: #fff3ce; border-radius: 0px 30px 0px 30px; margin: 0px 40px 0px 0px; width: calc(100% - 40px); max-width: calc(100% - 40px); }
}.sd[data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: auto; max-width: 100%; }
.sd[data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd] { flex: none; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd] { padding: 0px 20px; }
}.sd[data-s-99844f01-a017-44e4-b200-9f7b2e43eb03] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 18px; font-weight: 400; height: auto; line-height: 1; margin: 0px 0px 20px 0px; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }.sd[data-s-1b30ef1d-cb77-4379-b3a3-22e909bd0c34] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 36px; font-weight: 400; height: auto; line-height: 1.3; margin: 0px 0px 40px 0px; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-1b30ef1d-cb77-4379-b3a3-22e909bd0c34] { font-size: 28px; }
}.sd[data-s-112ca6f5-e628-42f4-bc6e-64b7794a38ba] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-0d83b48e); font-size: 20px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-42f9cc28-5359-4772-97da-f6e47776a691] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: flex-start; margin: -300px 0px 0px 0px; opacity: 0.9; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-42f9cc28-5359-4772-97da-f6e47776a691] { align-content: center; align-items: center; justify-content: flex-end; margin: -200px 0px 0px 0px; padding: 0px 40px 0px 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-42f9cc28-5359-4772-97da-f6e47776a691] { padding: 0px 0px 0px 0px; }
}.sd[data-s-4bff5899-65f5-489d-ac26-82611a23c802] { background: rgba(0,0,0,0.0); flex: none; height: 160px; width: 50%; max-width: 50%; }.sd[data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81] { border-radius: 50px; flex: none; height: 400px; width: 400px; max-width: 100%; }
.sd[data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81].appear { transform: scale(0, 0); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81]:before { background-position: left center; }
@media screen and (max-width: 540px){
.sd[data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81] { border-radius: 30px; flex: none; height: 300px; width: 300px; max-width: 100%; }
}.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee] { align-content: center; align-items: center; background: #474bff; border-radius: 32px; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 60px; justify-content: center; margin: 50px 0px 0px 0px; width: 50%; --gap-h-4aa07de4-521f-4250-bcf0-6229321c09ee: 10px; --gap-v-4aa07de4-521f-4250-bcf0-6229321c09ee: 0px; --gap-uuid: 4aa07de4-521f-4250-bcf0-6229321c09ee; max-width: 50%; }
.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee].appear { background: rgba(71, 75, 255, 0.61); --gap-h-4aa07de4-521f-4250-bcf0-6229321c09ee: 10px; --gap-v-4aa07de4-521f-4250-bcf0-6229321c09ee: 0px; }
.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee].appear-active {  }
.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee]:hover { background: rgba(71, 75, 255, 0.65); --gap-h-4aa07de4-521f-4250-bcf0-6229321c09ee: 10px; --gap-v-4aa07de4-521f-4250-bcf0-6229321c09ee: 0px; }
@media screen and (max-width: 840px){
.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee] { margin: 60px 0px; --gap-h-4aa07de4-521f-4250-bcf0-6229321c09ee: 10px; --gap-v-4aa07de4-521f-4250-bcf0-6229321c09ee: 0px; width: 50%; max-width: 50%; }
}
@media screen and (max-width: 540px){
.sd[data-s-4aa07de4-521f-4250-bcf0-6229321c09ee] { flex: none; height: auto; margin: 60px 0px 60px 0px; width: 80%; --gap-h-4aa07de4-521f-4250-bcf0-6229321c09ee: 10px; --gap-v-4aa07de4-521f-4250-bcf0-6229321c09ee: 0px; max-width: 80%; }
}.sd[data-s-c39709b0-8cda-42a5-8e8c-ab9cfb4cfb1f] { color: #FFFFFF; font-size: 28px; }
@media screen and (max-width: 540px){
.sd[data-s-c39709b0-8cda-42a5-8e8c-ab9cfb4cfb1f] { font-size: 24px; }
}.sd[data-s-dbfe7c79-de52-41fd-8702-07c99a1af240] { color: #FFFFFF; font-family: var(--s-font-d39b4375); font-size: 28px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-dbfe7c79-de52-41fd-8702-07c99a1af240] { font-size: 24px; }
}.sd[data-s-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 80px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { padding: 0px 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { align-content: center; align-items: center; flex: none; height: auto; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}.sd[data-s-section-inner-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { padding: 80px 0px; width: 100%; height: auto; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: center; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { padding: 0px 0px; flex-direction: column; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-7ae503bb-ee2d-4b16-930d-656444dbd3fa] { padding: 0px 0px; height: auto; flex-direction: column; align-content: center; align-items: center; justify-content: flex-start; }
}.sd[data-s-c9ed9a2d-a307-4f83-b45b-ef843e98a286] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-c9ed9a2d-a307-4f83-b45b-ef843e98a286] { margin: 70px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-fc09918d-f338-46ee-bf89-e114f37f03bf] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-98a7436a-9122-40fb-aac0-1b4932252045] { background: rgba(0,0,0,0.0); color: #333; font-family: var(--s-font-d39b4375); font-size: 144px; font-weight: 400; height: auto; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -20px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-98a7436a-9122-40fb-aac0-1b4932252045] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-98a7436a-9122-40fb-aac0-1b4932252045] { font-size: 53px; margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-1b827ff3-e35b-46f3-8c82-71eabfbafbd4] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: 718px; justify-content: flex-start; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-1b827ff3-e35b-46f3-8c82-71eabfbafbd4] { flex: none; height: auto; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-1b827ff3-e35b-46f3-8c82-71eabfbafbd4] { flex: none; height: auto; margin: -22px 0px 0px 0px; padding: 0px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-a63e3dac-0af1-496b-bc3e-345c17ac4f14] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-a63e3dac-0af1-496b-bc3e-345c17ac4f14] { margin: 0px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-d4606c2c-6b9a-464b-8c24-a70c4a789e4f] { align-content: flex-end; align-items: flex-end; background: #c7e9ff; border-radius: 50px 0px 50px 0px; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 0px 0px 0px -300px; padding: 80px 0px 80px 300px; width: calc(50% - -300px); max-width: calc(50% - -300px); }
@media screen and (max-width: 840px){
.sd[data-s-d4606c2c-6b9a-464b-8c24-a70c4a789e4f] { align-content: flex-start; align-items: flex-start; flex: none; justify-content: center; margin: 0px 0px 0px 120px; padding: 80px 0px 240px 0px; width: calc(100% - 120px); max-width: calc(100% - 120px); }
}
@media screen and (max-width: 540px){
.sd[data-s-d4606c2c-6b9a-464b-8c24-a70c4a789e4f] { border-radius: 30px 0px 30px 0px; margin: 30px 0px 0px 40px; padding: 40px 0px 240px 0px; width: calc(100% - 40px); max-width: calc(100% - 40px); }
}.sd[data-s-ee3332e5-650f-4c45-8229-26a230486bee] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: auto; max-width: 100%; }
.sd[data-s-ee3332e5-650f-4c45-8229-26a230486bee].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-ee3332e5-650f-4c45-8229-26a230486bee].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-ee3332e5-650f-4c45-8229-26a230486bee] { flex: none; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-ee3332e5-650f-4c45-8229-26a230486bee] { padding: 0px 20px; }
}.sd[data-s-0983143b-9e1b-4c82-afd1-2db89981c87f] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 18px; font-weight: 400; height: auto; line-height: 1; margin: 0px 0px 20px 0px; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-0983143b-9e1b-4c82-afd1-2db89981c87f] { margin: 0px 0px 20px 0px; padding: 0px 0px 0px 0px; }
}.sd[data-s-e05275b4-8d42-4644-b6d9-9c8b5601832f] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 36px; font-weight: 400; height: auto; line-height: 1.3; margin: 0px 0px 40px 0px; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-e05275b4-8d42-4644-b6d9-9c8b5601832f] { font-size: 28px; padding: 30px 30px 30px 0px; }
}.sd[data-s-70ac57fe-ffb9-4a9b-a68c-a5cf38bfaf9b] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-0d83b48e); font-size: 20px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-28bc8e7e-4406-4014-b19b-a1456037080a] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: flex-end; margin: -300px 0px 0px 0px; opacity: 0.9; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-28bc8e7e-4406-4014-b19b-a1456037080a] { align-content: center; align-items: center; justify-content: flex-start; margin: -200px 0px 0px 0px; padding: 0px 0px 0px 40px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-28bc8e7e-4406-4014-b19b-a1456037080a] { flex: none; height: 298px; padding: 0px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca] { border-radius: 50px; flex: none; height: 400px; width: 400px; max-width: 100%; }
.sd[data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca].appear { transform: scale(0, 0); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca]:before { background-position: left center; }
@media screen and (max-width: 540px){
.sd[data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca] { border-radius: 30px; flex: none; height: 300px; width: 300px; max-width: 100%; }
}.sd[data-s-8cbb496b-07a0-4b06-9359-bfeb03b6d84e] { background: rgba(0,0,0,0.0); flex: none; height: auto; width: 50%; max-width: 50%; }.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23] { align-content: center; align-items: center; background: #474bff; border-radius: 32px; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 10px; height: 60px; justify-content: center; margin: 50px 0px 0px 0px; width: 50%; --gap-h-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 10px; --gap-v-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 0px; --gap-uuid: 78be8712-a4cd-4890-8ed5-80e9f7efcc23; max-width: 50%; }
.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23].appear { background: rgba(71, 75, 255, 0.61); --gap-h-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 10px; --gap-v-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 0px; }
.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23].appear-active {  }
.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23]:hover { background: rgba(71, 75, 255, 0.65); --gap-h-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 10px; --gap-v-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 0px; }
@media screen and (max-width: 840px){
.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23] { margin: 60px 0px; --gap-h-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 10px; --gap-v-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 0px; width: 50%; max-width: 50%; }
}
@media screen and (max-width: 540px){
.sd[data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23] { flex: none; height: auto; margin: 60px 0px 60px 0px; width: 80%; --gap-h-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 10px; --gap-v-78be8712-a4cd-4890-8ed5-80e9f7efcc23: 0px; max-width: 80%; }
}.sd[data-s-e9f46706-0ba6-4d71-b30a-8ff9c9a817d3] { color: #FFFFFF; font-size: 28px; }
@media screen and (max-width: 540px){
.sd[data-s-e9f46706-0ba6-4d71-b30a-8ff9c9a817d3] { font-size: 24px; }
}.sd[data-s-7ef1a4b1-8f60-42be-82f8-12427f687739] { color: #FFFFFF; font-family: var(--s-font-d39b4375); font-size: 28px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-7ef1a4b1-8f60-42be-82f8-12427f687739] { font-size: 24px; }
}.sd[data-s-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-start; padding: 80px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { padding: 0px 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { margin: 0px 0px 0px 0px; padding: 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}.sd[data-s-section-inner-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { padding: 80px 0px; width: 100%; height: auto; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: flex-start; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { padding: 0px 0px; flex-direction: column; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-b5420ef4-bff7-47f0-9ef8-7e9a295e9878] { padding: 0px 0px; flex-direction: column; }
}.sd[data-s-237e9257-ad59-4d8f-be67-469525293df4] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-237e9257-ad59-4d8f-be67-469525293df4] { flex: none; height: 135px; margin: 42px 0px 0px 0px; padding: 0px 20px; max-width: 100%; }
}.sd[data-s-606842d7-f4cd-43ac-bcaf-4fb8a8b521df] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-4a7c4d0a-cf8d-42e1-8f9e-4119aa44c3af] { background: rgba(0,0,0,0.0); color: #f5f5f5; flex: none; font-family: var(--s-font-7d8084a9); font-size: 144px; font-weight: 400; height: 144px; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -22px 0px; padding: 0px 10px 0px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-4a7c4d0a-cf8d-42e1-8f9e-4119aa44c3af] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-4a7c4d0a-cf8d-42e1-8f9e-4119aa44c3af] { font-size: 64px; height: calc(50% - -10px); margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-c110c662-efbd-49b0-868a-4baab1051363] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: 100%; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-c110c662-efbd-49b0-868a-4baab1051363] { background: rgba(0,0,0,0.0); }
}.sd[data-s-96df9cb6-914a-4726-8789-667730aba2e0] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: 1280px; max-width: 100%; }.sd[data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 50%; max-width: 50%; }
.sd[data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c] { flex: none; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c] { padding: 0px 20px; }
}.sd[data-s-2fa77ee0-da6a-4015-8e33-cbe2dfe65d81] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-f45c883d-188a-49af-b7f6-e69f58c359ba] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-f45c883d-188a-49af-b7f6-e69f58c359ba] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-af48d012-8268-417d-a34b-04e8c084707c] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-6e46a44a-f234-4f52-9b3e-9ff58001c4ae] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-11bf299c-5149-479b-b5fb-fb60ce33cf39] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-11bf299c-5149-479b-b5fb-fb60ce33cf39] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-2a782afc-b61c-45e1-9558-91f1a0898f33] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-83365c62-e20e-45ed-b568-00cb47890261] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-40f4c0ea-c314-41f6-928d-e5758a11a2b3] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-40f4c0ea-c314-41f6-928d-e5758a11a2b3] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-d3226a1c-8736-47b0-9602-cd9e74e268ca] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-f47dd45c-35fe-45d9-9988-7b82bcbdac9a] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-9797c98e-1675-4f3b-a0d7-cef867f20e0b] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-9797c98e-1675-4f3b-a0d7-cef867f20e0b] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-ea95dc6f-343d-4d2d-ae4d-a350fc38ad5f] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-f61be54b-6d6c-420a-a4c7-ffea9f87f026] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 0px 0px 0px 0px; padding: 0px 40px 0px 0px; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-f61be54b-6d6c-420a-a4c7-ffea9f87f026] { margin: 40px 0px 0px 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-f61be54b-6d6c-420a-a4c7-ffea9f87f026] { padding: 0px 20px 0px 0px; }
}.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13] { border-radius: 0px 50px 50px 0px; flex: none; height: auto; margin: -300px 0px 0px 0px; width: 50%; max-width: 50%; }
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13].appear { transition-delay: 300ms; transition-duration: 1200ms; }
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13].appear-active { transition-delay: 300ms; transition-duration: 1200ms; }
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13].appear:before { filter:  blur(20px); }
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13].appear-active:before {  }
@media screen and (max-width: 840px){
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13] { flex: none; margin: 0px 0px 0px 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13] { border-radius: 0px 30px 30px 0px; flex: none; height: auto; margin: -30px 0px 0px 0px; width: 80%; max-width: 80%; }
}.sd[data-s-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { padding: 60px 0px; }
}
@media screen and (max-width: 540px){
.sd[data-s-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { padding: 60px 0px 0px 0px; }
}.sd[data-s-section-inner-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { padding: 0px 0px; width: 100%; height: auto; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: flex-start; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { padding: 60px 0px; flex-direction: column; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-7f9a707c-b1d1-40eb-9c2f-717623ef3be9] { padding: 60px 0px 0px 0px; flex-direction: column; }
}.sd[data-s-e062f0d5-5024-4c13-8590-001afed40692] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-e062f0d5-5024-4c13-8590-001afed40692] { flex: none; height: 138px; padding: 0px 20px; max-width: 100%; }
}.sd[data-s-993927b0-0eb4-4f57-975a-87fb50e8629f] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-393c2280-d000-42fb-9f97-3b8d0b0611cf] { background: rgba(0,0,0,0.0); color: #f5f5f5; flex: none; font-family: var(--s-font-7d8084a9); font-size: 144px; font-weight: 400; height: 144px; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -22px 0px; padding: 0px 10px 0px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-393c2280-d000-42fb-9f97-3b8d0b0611cf] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-393c2280-d000-42fb-9f97-3b8d0b0611cf] { font-size: 64px; height: calc(50% - -10px); margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-bc073913-4020-45fc-ac46-47e91cf86da4] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-bc073913-4020-45fc-ac46-47e91cf86da4] { flex-direction: column; flex-wrap: nowrap; }
}.sd[data-s-c8da2f54-41d4-4561-a1f3-c25ffbc8f2a6] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; width: 1280px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-c8da2f54-41d4-4561-a1f3-c25ffbc8f2a6] { flex: none; flex-direction: column; flex-wrap: nowrap; height: 943px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-c8da2f54-41d4-4561-a1f3-c25ffbc8f2a6] { height: 699px; max-width: 100%; }
}.sd[data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 100px 0px 0px 0px; padding: 0px 40px; width: 50%; max-width: 50%; }
.sd[data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8] { flex: none; margin: 15px 0px 0px 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8] { padding: 0px 20px; }
}.sd[data-s-b2a6bd52-62a1-4105-ab8f-734cee333e39] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-f79b54f7-6355-4ced-a6ec-c40001f956d8] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-f79b54f7-6355-4ced-a6ec-c40001f956d8] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-ee64fc93-9ef4-4d0c-b031-68047e0c789b] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-a1e8dfd7-486c-4ce5-9b0d-ce8f03e75bfc] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-d85acbc0-4760-4110-bb24-211ac65325bf] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-d85acbc0-4760-4110-bb24-211ac65325bf] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-abf4c63f-5021-4f1d-bf29-ed6ec0cc581e] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-6996ae51-0caf-4246-a4a9-100e90359ce6] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px 0px; width: 100%; max-width: 100%; }.sd[data-s-c998b0a5-8252-445b-b55c-528e8802245c] { background: rgba(0,0,0,0.0); color: #000000; flex: none; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: 120px; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-c998b0a5-8252-445b-b55c-528e8802245c] { flex: none; width: 100px; max-width: 100%; }
}.sd[data-s-8f4e5c2c-3b78-49d4-9e3e-0f5af369ed1d] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-2f953087-f6b6-4e5e-98fc-af8df44bcf09] { align-content: flex-start; align-items: flex-start; background: #EEEEEE; flex: none; flex-direction: column; flex-wrap: nowrap; height: 473px; justify-content: center; width: 50%; max-width: 50%; }
@media screen and (max-width: 840px){
.sd[data-s-2f953087-f6b6-4e5e-98fc-af8df44bcf09] { height: 569px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-2f953087-f6b6-4e5e-98fc-af8df44bcf09] { flex: none; height: calc(45% - 23px); margin: 23px 0px 0px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-ad0743f8-ff76-4085-9ba3-5b34deef4cb7] { flex: none; height: 100%; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-ad0743f8-ff76-4085-9ba3-5b34deef4cb7] { flex: none; height: 100%; width: 100%; }
}.sd[data-s-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { align-content: center; align-items: center; background: #FFFFFF; border-radius: 0px 0px 0px 0px; flex: none; flex-direction: column; flex-wrap: nowrap; height: 894px; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 80px 0px 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { align-content: center; align-items: center; height: 599px; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 0px 0px 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { border-radius: 0px 0px 0px 60px; flex: none; height: 568px; margin: 0px 0px 0px 0px; padding: 39px 0px 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}.sd[data-s-section-inner-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { padding: 80px 0px 0px 0px; width: 100%; height: 894px; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: flex-start; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { padding: 0px 0px 0px 0px; height: 599px; flex-direction: column; align-content: center; align-items: center; justify-content: flex-start; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-eec6c358-8e75-44e2-84ff-9b94cbf18c2e] { padding: 39px 0px 0px 0px; height: 568px; flex-direction: column; width: 100%; max-width: 100%; }
}.sd[data-s-fd84fe9b-3821-42d2-bf88-560e11664c4c] { align-content: flex-end; align-items: flex-end; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-fd84fe9b-3821-42d2-bf88-560e11664c4c] { margin: 0px 0px 0px 0px; padding: 0px 40px 0px 40px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-fd84fe9b-3821-42d2-bf88-560e11664c4c] { flex: none; height: auto; padding: 0px 20px; }
}.sd[data-s-d0c280c7-da68-47b9-9c92-2160fb2b1e22] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-c7d223f4-b90b-4036-9e2d-8110a6ed0da0] { background: rgba(0,0,0,0.0); color: #f5f5f5; font-family: var(--s-font-7d8084a9); font-size: 144px; font-weight: 600; height: auto; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -22px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-c7d223f4-b90b-4036-9e2d-8110a6ed0da0] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-c7d223f4-b90b-4036-9e2d-8110a6ed0da0] { font-size: 64px; margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-60232db1-4264-402d-a322-d030c263f6cb] { align-content: stretch; align-items: stretch; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: auto; justify-content: center; width: 100%; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-60232db1-4264-402d-a322-d030c263f6cb] { height: 492px; padding: 0px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-60232db1-4264-402d-a322-d030c263f6cb] { flex: none; height: 413px; padding: 0px 0px 20px 0px; width: 100%; max-width: 100%; }
}.sd[data-s-12e5f71b-3844-4312-bd65-479c54a1ef96] { background: rgba(0,0,0,0.0); flex: 1; height: 51px; width: auto; max-width: 100%; }.sd[data-s-53048a58-73bd-4bf7-9366-c66369a419be] { align-content: stretch; align-items: stretch; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; height: 492px; justify-content: center; padding: 0px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-53048a58-73bd-4bf7-9366-c66369a419be] { height: 496px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-53048a58-73bd-4bf7-9366-c66369a419be] { height: 774px; max-width: 100%; }
}.sd[data-s-eb915849-d94b-4d03-94fe-6b341938395a] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-start; padding: 40px 0px 0px 40px; width: 140px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-eb915849-d94b-4d03-94fe-6b341938395a] { padding: 40px 0px 0px 70px; }
}
@media screen and (max-width: 540px){
.sd[data-s-eb915849-d94b-4d03-94fe-6b341938395a] { flex: none; padding: 40px 0px 0px 20px; width: 80px; max-width: 100%; }
}.sd[data-s-7154acfd-bc22-4ba5-afbf-0c8a74132727] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; writing-mode: vertical-rl; -ms-writing-mode: vertical-rl; -webkit-writing-mode: vertical-rl; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-7154acfd-bc22-4ba5-afbf-0c8a74132727] { font-size: 20px; padding: 5px; }
}.sd[data-s-4e8b7176-7a94-4c96-9246-25e0d1b165e9] { align-content: flex-end; align-items: flex-end; flex: none; flex-direction: column-reverse; flex-wrap: nowrap; justify-content: flex-end; padding: 0px; width: 90%; max-width: 90%; }
@media screen and (max-width: 840px){
.sd[data-s-4e8b7176-7a94-4c96-9246-25e0d1b165e9] { align-content: center; align-items: center; flex: none; height: 466px; justify-content: flex-start; padding: 0px; width: 90%; max-width: 90%; }
}
@media screen and (max-width: 540px){
.sd[data-s-4e8b7176-7a94-4c96-9246-25e0d1b165e9] { align-content: center; align-items: center; flex: none; height: 100%; justify-content: flex-end; width: 90%; max-width: 90%; }
}.sd[data-s-9c777532-a93d-454a-8ca5-684d81bc942c] { align-content: center; align-items: center; background: #f5f5f5; border-radius: 50px 0px 0px 50px; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 100px 80px 100px 80px; width: auto; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-9c777532-a93d-454a-8ca5-684d81bc942c] { flex: none; flex-direction: column-reverse; flex-wrap: nowrap; height: auto; margin: 0px 0px 0px 0px; padding: 80px 40px 80px 40px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-9c777532-a93d-454a-8ca5-684d81bc942c] { align-content: center; align-items: center; border-radius: 30px 0px 0px 30px; flex: none; height: auto; justify-content: flex-end; margin: 0px 0px 0px 0px; padding: 30px 20px; }
}.sd[data-s-adc05688-73ca-471b-8242-b30b98aca0f0] { background: rgba(0,0,0,0.0); flex: none; gap: 80px; height: auto; padding: 10px; width: 100%; --gap-h-adc05688-73ca-471b-8242-b30b98aca0f0: 0px; --gap-v-adc05688-73ca-471b-8242-b30b98aca0f0: 80px; --gap-uuid: adc05688-73ca-471b-8242-b30b98aca0f0; max-width: 100%; }
.sd[data-s-adc05688-73ca-471b-8242-b30b98aca0f0].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; --gap-h-adc05688-73ca-471b-8242-b30b98aca0f0: 0px; --gap-v-adc05688-73ca-471b-8242-b30b98aca0f0: 80px; }
.sd[data-s-adc05688-73ca-471b-8242-b30b98aca0f0].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-adc05688-73ca-471b-8242-b30b98aca0f0] { align-content: flex-start; align-items: flex-start; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 60px; height: auto; justify-content: center; width: 629px; --gap-h-adc05688-73ca-471b-8242-b30b98aca0f0: 0px; --gap-v-adc05688-73ca-471b-8242-b30b98aca0f0: 60px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-adc05688-73ca-471b-8242-b30b98aca0f0] { flex: none; gap: 60px; height: auto; padding: 0px; width: 475px; --gap-h-adc05688-73ca-471b-8242-b30b98aca0f0: 0px; --gap-v-adc05688-73ca-471b-8242-b30b98aca0f0: 60px; max-width: 100%; }
}.sd[data-s-3915dd87-3485-40c9-be32-8d3162df58c5] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; gap: 40px; height: calc(50% - (var(--gap-v-adc05688-73ca-471b-8242-b30b98aca0f0) * 0.5)); justify-content: center; width: calc(100% - (var(--gap-h-adc05688-73ca-471b-8242-b30b98aca0f0) * 0)); --gap-h-3915dd87-3485-40c9-be32-8d3162df58c5: 40px; --gap-v-3915dd87-3485-40c9-be32-8d3162df58c5: 0px; --gap-uuid: 3915dd87-3485-40c9-be32-8d3162df58c5; max-width: calc(100% - (var(--gap-h-adc05688-73ca-471b-8242-b30b98aca0f0) * 0)); }
.sd[data-s-3915dd87-3485-40c9-be32-8d3162df58c5]:hover { opacity: 1; --gap-h-3915dd87-3485-40c9-be32-8d3162df58c5: 40px; --gap-v-3915dd87-3485-40c9-be32-8d3162df58c5: 0px; }
@media screen and (max-width: 840px){
.sd[data-s-3915dd87-3485-40c9-be32-8d3162df58c5] { align-content: flex-start; align-items: flex-start; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; justify-content: center; padding: 0px; width: auto; --gap-h-3915dd87-3485-40c9-be32-8d3162df58c5: 0px; --gap-v-3915dd87-3485-40c9-be32-8d3162df58c5: 20px; max-width: 100%; height: calc(50% - (var(--gap-v-adc05688-73ca-471b-8242-b30b98aca0f0) * 0.5)); }
}
@media screen and (max-width: 540px){
.sd[data-s-3915dd87-3485-40c9-be32-8d3162df58c5] { flex: none; height: calc(45% - (var(--gap-v-adc05688-73ca-471b-8242-b30b98aca0f0) * 0.55)); --gap-h-3915dd87-3485-40c9-be32-8d3162df58c5: 40px; --gap-v-3915dd87-3485-40c9-be32-8d3162df58c5: 0px; max-width: 100%; }
}.sd[data-s-23530ee2-5ad9-4fc4-ac0c-49023defadc7] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: 1; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: flex-start; width: auto; --gap-h-23530ee2-5ad9-4fc4-ac0c-49023defadc7: 0px; --gap-v-23530ee2-5ad9-4fc4-ac0c-49023defadc7: 10px; --gap-uuid: 23530ee2-5ad9-4fc4-ac0c-49023defadc7; max-width: 100%; }.sd[data-s-4306a94c-d7f2-48ff-bd40-75b31b7476ef] { align-content: center; align-items: center; background: #eca41c; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 10px; height: auto; justify-content: center; width: auto; --gap-h-4306a94c-d7f2-48ff-bd40-75b31b7476ef: 0px; --gap-v-4306a94c-d7f2-48ff-bd40-75b31b7476ef: 10px; --gap-uuid: 4306a94c-d7f2-48ff-bd40-75b31b7476ef; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-4306a94c-d7f2-48ff-bd40-75b31b7476ef] { background: rgba(0,0,0,0.0); height: auto; --gap-h-4306a94c-d7f2-48ff-bd40-75b31b7476ef: 0px; --gap-v-4306a94c-d7f2-48ff-bd40-75b31b7476ef: 10px; }
}.sd[data-s-c5817b81-fb8a-45b2-a9ca-d89722aedc72] { background: rgb(159, 159, 159); color: #000000; font-family: var(--s-font-7d8084a9); font-size: 15px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-c5817b81-fb8a-45b2-a9ca-d89722aedc72] { background: rgba(0,0,0,0.0); font-size: 12px; }
}.sd[data-s-11b71a83-75c7-40c4-adf6-f9587cb436e2] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-d39b4375); font-size: 18px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-1304ef54-3598-404b-a6e9-e1dfc45298cd] { background: rgba(0,0,0,0.0); color: #000000; font-family: var(--s-font-3a8e7aa2); font-size: 20px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-9955cdc1-9828-48bb-8cc4-101c33cc95a5] { align-content: center; align-items: center; background: #f5f5f5; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: -200px 20px; width: auto; max-width: 100%; }
@media screen and (max-width: 540px){
.sd[data-s-9955cdc1-9828-48bb-8cc4-101c33cc95a5] { flex: none; height: auto; padding: 0px 20px; width: 54px; max-width: 100%; }
}.sd[data-s-53278b6a-0558-4ea2-932f-c1741065364d] { background: rgba(0,0,0,0.0); flex: none; height: 100%; width: 100%; max-width: 100%; }.sd[data-s-7ef36464-efff-4b51-adc7-8eeaa26447c2] { flex: none; height: 100vh; left: 0; margin: 0 0 0 0; position: fixed; top: 0; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); z-index: -3; max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
.sd[data-s-7ef36464-efff-4b51-adc7-8eeaa26447c2]:before { filter:  brightness(0.3)     grayscale(0.1); }.sd[data-s-f7009d78-f595-467f-bb0d-f586fb3db9ce] { align-content: flex-start; align-items: flex-start; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; margin: 0px 0px -15px 0px; padding: 34px 40px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); --gap-uuid: f7009d78-f595-467f-bb0d-f586fb3db9ce; --gap-h-f7009d78-f595-467f-bb0d-f586fb3db9ce: 0px; --gap-v-f7009d78-f595-467f-bb0d-f586fb3db9ce: 0px; max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-f7009d78-f595-467f-bb0d-f586fb3db9ce] { margin: 0px 0px 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-f7009d78-f595-467f-bb0d-f586fb3db9ce] { flex: none; gap: 0px; height: 116px; margin: 71px 0px 0px 0px; padding: 0px 20px; width: 481px; --gap-h-f7009d78-f595-467f-bb0d-f586fb3db9ce: 0px; --gap-v-f7009d78-f595-467f-bb0d-f586fb3db9ce: 0px; max-width: 100%; }
}.sd[data-s-8c665269-c7a9-4963-8079-af47f57bdf58] { background: #FFFFFF; color: #000000; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-7a1341f6-63b7-4f1c-a87e-a673e157205a] { background: rgba(0,0,0,0.0); color: #f5f5f5; font-family: var(--s-font-7d8084a9); font-size: 144px; font-weight: 600; height: auto; letter-spacing: -0.07em; line-height: 1; margin: 0px 0px -22px 0px; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-7a1341f6-63b7-4f1c-a87e-a673e157205a] { font-size: 90px; margin: 0px 0px -14px 0px; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-7a1341f6-63b7-4f1c-a87e-a673e157205a] { font-size: 62px; margin: 0px 0px -10px 0px; max-width: 100%; }
}.sd[data-s-e727b092-6dd0-43d4-9145-4fbb2e711d06] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: 1538px; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 0 0; width: 1280px; z-index: 1; max-width: 100%; }
.sd[data-s-e727b092-6dd0-43d4-9145-4fbb2e711d06]:hover { transform: ; }
@media screen and (max-width: 540px){
.sd[data-s-e727b092-6dd0-43d4-9145-4fbb2e711d06] { flex: none; height: auto; margin: 0px 0px 0px 0px; width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); max-width: calc(100% - (var(--gap-h-b9f0bc5b-657c-4067-9226-82c94e86d992) * 0)); }
}.sd[data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06] { padding: 0 0; width: 1280px; height: 1538px; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: flex-start; max-width: 100%; }
.sd[data-s-e727b092-6dd0-43d4-9145-4fbb2e711d06]:hover .sd[data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06]:hover { flex-direction: column; }
@media screen and (max-width: 540px){
.sd[data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06] { width: 100%; height: auto; flex-direction: column; max-width: 100%; }
}.sd[data-s-8576d803-f474-4678-a7f2-6cbe0b2e98f2] { align-content: center; align-items: center; background: #FFFFFF; border-bottom: 0px solid #000; border-left: 0px solid #000; border-radius: 50px; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: column; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: flex-start; margin: 0px 0px 0px 0px; padding: 40px 40px; width: 100%; --gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 0px; --gap-v-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 15px; --gap-uuid: 8576d803-f474-4678-a7f2-6cbe0b2e98f2; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-8576d803-f474-4678-a7f2-6cbe0b2e98f2] { flex: none; gap: 20px; height: auto; padding: 50px 50px 30px 50px; --gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 0px; --gap-v-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 20px; }
}
@media screen and (max-width: 540px){
.sd[data-s-8576d803-f474-4678-a7f2-6cbe0b2e98f2] { flex: none; gap: 15px; height: auto; margin: 0px 0px 0px 0px; padding: 37px 37px 25px 37px; --gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 0px; --gap-v-8576d803-f474-4678-a7f2-6cbe0b2e98f2: 15px; }
}.sd[data-s-268a50c4-0f2e-4445-b28f-657cbd76c877] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; width: auto; max-width: 100%; }.sd[data-s-fd91d9c8-a502-4dcf-b0ed-0fc6b04e9580] { color: #000000; font-family: var(--s-font-d39b4375); font-size: 36px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-fd91d9c8-a502-4dcf-b0ed-0fc6b04e9580] { font-size: 28px; }
}.sd[data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be] { padding: 0px 20px; }
}.sd[data-s-cd00290f-f286-4b48-b272-670c2285e0c6] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-cd00290f-f286-4b48-b272-670c2285e0c6: 15px; --gap-v-cd00290f-f286-4b48-b272-670c2285e0c6: 0px; --gap-uuid: cd00290f-f286-4b48-b272-670c2285e0c6; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-cd00290f-f286-4b48-b272-670c2285e0c6] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-cd00290f-f286-4b48-b272-670c2285e0c6: 15px; --gap-v-cd00290f-f286-4b48-b272-670c2285e0c6: 0px; }
}.sd[data-s-e4c199ee-99ea-4994-af87-ca089b8a1895] { color: #333; font-size: 24px; }.sd[data-s-7c17e287-52db-45e7-b317-36d092640433] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c] { padding: 0px 20px; }
}.sd[data-s-93f99ba8-97d0-468e-a8f3-e71dce03555d] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-93f99ba8-97d0-468e-a8f3-e71dce03555d: 15px; --gap-v-93f99ba8-97d0-468e-a8f3-e71dce03555d: 0px; --gap-uuid: 93f99ba8-97d0-468e-a8f3-e71dce03555d; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-93f99ba8-97d0-468e-a8f3-e71dce03555d] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-93f99ba8-97d0-468e-a8f3-e71dce03555d: 15px; --gap-v-93f99ba8-97d0-468e-a8f3-e71dce03555d: 0px; }
}.sd[data-s-5a550853-0fa4-4aaa-bfcc-8c7561a9234c] { color: #333; font-size: 24px; }.sd[data-s-b304effa-968a-4178-9ac8-1158291dab55] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-acbadb01-52d4-4fbb-9a35-747b672b7276] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-acbadb01-52d4-4fbb-9a35-747b672b7276].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-acbadb01-52d4-4fbb-9a35-747b672b7276].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-acbadb01-52d4-4fbb-9a35-747b672b7276] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-acbadb01-52d4-4fbb-9a35-747b672b7276] { padding: 0px 20px; }
}.sd[data-s-956677d1-c72b-4d5b-a78b-04c7450bf659] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-956677d1-c72b-4d5b-a78b-04c7450bf659: 15px; --gap-v-956677d1-c72b-4d5b-a78b-04c7450bf659: 0px; --gap-uuid: 956677d1-c72b-4d5b-a78b-04c7450bf659; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-956677d1-c72b-4d5b-a78b-04c7450bf659] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-956677d1-c72b-4d5b-a78b-04c7450bf659: 15px; --gap-v-956677d1-c72b-4d5b-a78b-04c7450bf659: 0px; }
}.sd[data-s-0c1fb2ff-8d8c-4f85-b0b4-824083d66bab] { color: #333; font-size: 24px; }.sd[data-s-218b172b-5961-49de-8167-e91b54510167] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-*************-4268-88d5-6f2e64406af7] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-*************-4268-88d5-6f2e64406af7].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-*************-4268-88d5-6f2e64406af7].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-*************-4268-88d5-6f2e64406af7] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-*************-4268-88d5-6f2e64406af7] { padding: 0px 20px; }
}.sd[data-s-2ccf4a5a-299e-4add-99ba-53296f23254a] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-2ccf4a5a-299e-4add-99ba-53296f23254a: 15px; --gap-v-2ccf4a5a-299e-4add-99ba-53296f23254a: 0px; --gap-uuid: 2ccf4a5a-299e-4add-99ba-53296f23254a; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-2ccf4a5a-299e-4add-99ba-53296f23254a] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-2ccf4a5a-299e-4add-99ba-53296f23254a: 15px; --gap-v-2ccf4a5a-299e-4add-99ba-53296f23254a: 0px; }
}.sd[data-s-cbe04342-8cf8-4c05-af45-1239e5873715] { color: #333; font-size: 24px; }.sd[data-s-cfde33da-0326-43f7-988f-da112dab8a46] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-8093f99c-07b6-4a54-9a01-1f2532137044] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-8093f99c-07b6-4a54-9a01-1f2532137044].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-8093f99c-07b6-4a54-9a01-1f2532137044].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-8093f99c-07b6-4a54-9a01-1f2532137044] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-8093f99c-07b6-4a54-9a01-1f2532137044] { padding: 0px 20px; }
}.sd[data-s-8749ca00-acb8-495f-8467-9a159d1c25a8] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-8749ca00-acb8-495f-8467-9a159d1c25a8: 15px; --gap-v-8749ca00-acb8-495f-8467-9a159d1c25a8: 0px; --gap-uuid: 8749ca00-acb8-495f-8467-9a159d1c25a8; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-8749ca00-acb8-495f-8467-9a159d1c25a8] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-8749ca00-acb8-495f-8467-9a159d1c25a8: 15px; --gap-v-8749ca00-acb8-495f-8467-9a159d1c25a8: 0px; }
}.sd[data-s-2f54a351-b913-4f6c-9fcd-bc07db148d61] { color: #333; font-size: 24px; }.sd[data-s-56a77424-2733-4a52-8fa0-8f2c69390939] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-49317c6f-a9e3-4a5b-a512-960766fa5021] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-49317c6f-a9e3-4a5b-a512-960766fa5021].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-49317c6f-a9e3-4a5b-a512-960766fa5021].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-49317c6f-a9e3-4a5b-a512-960766fa5021] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-49317c6f-a9e3-4a5b-a512-960766fa5021] { padding: 0px 20px; }
}.sd[data-s-7b557116-ab41-40d3-b408-7d78eba4cc75] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-7b557116-ab41-40d3-b408-7d78eba4cc75: 15px; --gap-v-7b557116-ab41-40d3-b408-7d78eba4cc75: 0px; --gap-uuid: 7b557116-ab41-40d3-b408-7d78eba4cc75; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-7b557116-ab41-40d3-b408-7d78eba4cc75] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-7b557116-ab41-40d3-b408-7d78eba4cc75: 15px; --gap-v-7b557116-ab41-40d3-b408-7d78eba4cc75: 0px; }
}.sd[data-s-c6912d00-3f61-4e9f-97e4-59c7e92265d8] { color: #333; font-size: 24px; }.sd[data-s-8516a921-7250-4b19-be18-42e3c14ef4fb] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7] { padding: 0px 20px; }
}.sd[data-s-9413258a-14cb-4d40-92a9-b0b5709ef25f] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-9413258a-14cb-4d40-92a9-b0b5709ef25f: 15px; --gap-v-9413258a-14cb-4d40-92a9-b0b5709ef25f: 0px; --gap-uuid: 9413258a-14cb-4d40-92a9-b0b5709ef25f; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-9413258a-14cb-4d40-92a9-b0b5709ef25f] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-9413258a-14cb-4d40-92a9-b0b5709ef25f: 15px; --gap-v-9413258a-14cb-4d40-92a9-b0b5709ef25f: 0px; }
}.sd[data-s-895fc7e3-d8e0-411f-9256-63951b03a86d] { color: #333; font-size: 24px; }.sd[data-s-293169e0-b947-4311-a5d6-677dbb666654] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6] { padding: 0px 20px; }
}.sd[data-s-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997: 15px; --gap-v-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997: 0px; --gap-uuid: 9d1dd2a8-ba00-4a0b-91c0-6139dca0e997; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997: 15px; --gap-v-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997: 0px; }
}.sd[data-s-c680230f-7395-4246-ae2d-282c53ee3fbf] { color: #333; font-size: 24px; }.sd[data-s-76ae00d5-bc03-444f-9f97-dd447bed71dc] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14] { align-content: center; align-items: center; background: #FFFFFF; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 0px 40px; width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); max-width: calc(70% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0.3)); }
.sd[data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; }
.sd[data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14] { border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); max-width: calc(100% - (var(--gap-h-8576d803-f474-4678-a7f2-6cbe0b2e98f2) * 0)); }
}
@media screen and (max-width: 540px){
.sd[data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14] { padding: 0px 20px; }
}.sd[data-s-8718a896-bd05-4cbd-bfc5-47f901cb1ada] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); border-bottom: 1px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 15px; height: auto; justify-content: center; padding: 30px 0px; width: 100%; --gap-h-8718a896-bd05-4cbd-bfc5-47f901cb1ada: 15px; --gap-v-8718a896-bd05-4cbd-bfc5-47f901cb1ada: 0px; --gap-uuid: 8718a896-bd05-4cbd-bfc5-47f901cb1ada; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-8718a896-bd05-4cbd-bfc5-47f901cb1ada] { border-bottom: 0px solid #000; border-left: 0px solid #000; border-right: 0px solid #000; border-top: 0px solid #000; --gap-h-8718a896-bd05-4cbd-bfc5-47f901cb1ada: 15px; --gap-v-8718a896-bd05-4cbd-bfc5-47f901cb1ada: 0px; }
}.sd[data-s-1f756f70-0f8c-4a56-8dc9-d82da46326a2] { color: #333; font-size: 24px; }.sd[data-s-d2037801-58ff-4a7c-a80b-ae5a6583aa18] { background: rgba(0,0,0,0.0); color: #000000; flex: 1; font-family: var(--s-font-3a8e7aa2); font-size: 15px; font-weight: 400; height: auto; line-height: 1.8; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }.sd[data-s-46555ff5-91fb-4c8d-b718-7eb11dd14eca] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 30px; width: auto; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-46555ff5-91fb-4c8d-b718-7eb11dd14eca] { padding: 15px; }
}.sd[data-s-e1149f6d-520e-4765-a356-978738d401bb] { color: #333; font-family: var(--s-font-d39b4375); font-size: 24px; font-weight: 400; height: auto; line-height: 1; padding: 0px 0px; text-align: center; width: auto; max-width: 100%; justify-content: center; }
@media screen and (max-width: 840px){
.sd[data-s-e1149f6d-520e-4765-a356-978738d401bb] { font-size: 20px; }
}
@media screen and (max-width: 540px){
.sd[data-s-e1149f6d-520e-4765-a356-978738d401bb] { font-size: 16px; text-align: center; justify-content: center; }
}.sd[data-s-0fb5ef8e-783d-447b-a470-49519f1d49b2] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; gap: 20px; height: auto; justify-content: center; padding: 0px 0px 30px 0px; width: 100%; --gap-h-0fb5ef8e-783d-447b-a470-49519f1d49b2: 0px; --gap-v-0fb5ef8e-783d-447b-a470-49519f1d49b2: 20px; --gap-uuid: 0fb5ef8e-783d-447b-a470-49519f1d49b2; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-0fb5ef8e-783d-447b-a470-49519f1d49b2] { height: 201px; --gap-h-0fb5ef8e-783d-447b-a470-49519f1d49b2: 0px; --gap-v-0fb5ef8e-783d-447b-a470-49519f1d49b2: 20px; width: 100%; max-width: 100%; }
}
@media screen and (max-width: 540px){
.sd[data-s-0fb5ef8e-783d-447b-a470-49519f1d49b2] { height: 160px; --gap-h-0fb5ef8e-783d-447b-a470-49519f1d49b2: 0px; --gap-v-0fb5ef8e-783d-447b-a470-49519f1d49b2: 20px; width: 100%; max-width: 100%; }
}.sd[data-s-8bdd81b2-845b-4a3b-abb8-40c049301a60] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: flex-end; width: auto; max-width: 100%; }.sd[data-s-9e9ae698-d200-4bf7-af14-9634cc8b0a70] { align-content: center; align-items: center; background: #474bff; border-radius: 48px; flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 15px 300px; width: auto; max-width: 100%; }
.sd[data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06]:hover .sd[data-s-9e9ae698-d200-4bf7-af14-9634cc8b0a70]:hover { background: rgba(71, 75, 255, 0.7); }
@media screen and (max-width: 840px){
.sd[data-s-9e9ae698-d200-4bf7-af14-9634cc8b0a70] { padding: 15px 180px; }
}
@media screen and (max-width: 540px){
.sd[data-s-9e9ae698-d200-4bf7-af14-9634cc8b0a70] { padding: 15px 90px; }
}.sd[data-s-91e07e67-d483-460e-88d4-6795622dd089] { color: #FFFFFF; flex: none; font-family: var(--s-font-d39b4375); font-size: 36px; font-weight: 400; height: auto; line-height: 1; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-91e07e67-d483-460e-88d4-6795622dd089] { font-size: 18px; padding: 0px; }
}.sd[data-s-213a3507-ee21-49d2-9b69-61c6b2feeab5] { color: #333; font-size: 64px; }
.sd[data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06]:hover .sd[data-s-213a3507-ee21-49d2-9b69-61c6b2feeab5]:hover { color: #fb83ff; transform: scale(1.05, 1.05); }
@media screen and (max-width: 840px){
.sd[data-s-213a3507-ee21-49d2-9b69-61c6b2feeab5] { font-size: 48px; }
}.sd[data-s-00cf3d00-f69a-478c-a207-dc28048a3f44] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: auto; justify-content: center; padding: 120px 40px; width: 1280px; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-00cf3d00-f69a-478c-a207-dc28048a3f44] { background: rgba(0,0,0,0.0); }
}
@media screen and (max-width: 540px){
.sd[data-s-00cf3d00-f69a-478c-a207-dc28048a3f44] { background: rgba(0,0,0,0.0); padding: 80px 20px; }
}.sd[data-s-section-inner-00cf3d00-f69a-478c-a207-dc28048a3f44] { padding: 120px 40px; width: 1280px; height: auto; flex-direction: column; flex-wrap: nowrap; align-content: center; align-items: center; justify-content: center; max-width: 100%; }
@media screen and (max-width: 840px){
.sd[data-s-section-inner-00cf3d00-f69a-478c-a207-dc28048a3f44] { flex-direction: column; }
}
@media screen and (max-width: 540px){
.sd[data-s-section-inner-00cf3d00-f69a-478c-a207-dc28048a3f44] { padding: 80px 20px; flex-direction: column; }
}.sd[data-s-1721453a-de2e-4eea-b937-2864bdeca523] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: row; flex-wrap: nowrap; gap: 37px; height: auto; justify-content: center; width: 100%; --gap-h-1721453a-de2e-4eea-b937-2864bdeca523: 37px; --gap-v-1721453a-de2e-4eea-b937-2864bdeca523: 0px; --gap-uuid: 1721453a-de2e-4eea-b937-2864bdeca523; max-width: 100%; }
.sd[data-s-1721453a-de2e-4eea-b937-2864bdeca523].appear { opacity: 0; transform: translate(0px, 20px); transition-delay: 300ms; transition-duration: 600ms; --gap-h-1721453a-de2e-4eea-b937-2864bdeca523: 37px; --gap-v-1721453a-de2e-4eea-b937-2864bdeca523: 0px; }
.sd[data-s-1721453a-de2e-4eea-b937-2864bdeca523].appear-active { transition-delay: 300ms; transition-duration: 600ms; }
@media screen and (max-width: 840px){
.sd[data-s-1721453a-de2e-4eea-b937-2864bdeca523] { flex-direction: column; flex-wrap: nowrap; gap: 86px; --gap-h-1721453a-de2e-4eea-b937-2864bdeca523: 0px; --gap-v-1721453a-de2e-4eea-b937-2864bdeca523: 86px; }
}
@media screen and (max-width: 540px){
.sd[data-s-1721453a-de2e-4eea-b937-2864bdeca523] { gap: 43px; --gap-h-1721453a-de2e-4eea-b937-2864bdeca523: 43px; --gap-v-1721453a-de2e-4eea-b937-2864bdeca523: 0px; }
}.sd[data-s-4424b192-1fe7-4e8d-a75e-d9ce6bc7827c] { background: rgba(0,0,0,0.0); color: #fffefe; flex: none; font-family: var(--s-font-d39b4375); font-size: 36px; font-weight: 400; height: auto; line-height: 1.3; text-align: left; width: calc(50% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.5)); max-width: calc(50% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.5)); justify-content: flex-start; }
@media screen and (max-width: 840px){
.sd[data-s-4424b192-1fe7-4e8d-a75e-d9ce6bc7827c] { flex: none; text-align: center; width: calc(100% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0)); max-width: calc(100% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0)); justify-content: center; }
}
@media screen and (max-width: 540px){
.sd[data-s-4424b192-1fe7-4e8d-a75e-d9ce6bc7827c] { font-size: 24px; text-align: center; justify-content: center; }
}.sd[data-s-f82d0098-71aa-4e34-a5e4-acc2cae50aef] { align-content: flex-start; align-items: flex-start; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; gap: 30px; height: 157px; justify-content: flex-end; margin: 30px 0px 0px 0px; width: calc(48% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.52)); --gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef: 0px; --gap-v-f82d0098-71aa-4e34-a5e4-acc2cae50aef: 30px; --gap-uuid: f82d0098-71aa-4e34-a5e4-acc2cae50aef; max-width: calc(48% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.52)); }
@media screen and (max-width: 540px){
.sd[data-s-f82d0098-71aa-4e34-a5e4-acc2cae50aef] { align-content: center; align-items: center; flex: none; justify-content: flex-end; margin: 121px 0px 0px 0px; width: calc(70% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.3)); --gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef: 0px; --gap-v-f82d0098-71aa-4e34-a5e4-acc2cae50aef: 30px; max-width: calc(70% - (var(--gap-h-1721453a-de2e-4eea-b937-2864bdeca523) * 0.3)); }
}.sd[data-s-ab019cb8-0583-4795-ae7a-a08628f4396d] { align-content: center; align-items: center; background: rgba(0,0,0,0.0); flex: none; flex-direction: column; flex-wrap: nowrap; height: 98px; justify-content: center; width: calc(100% - (var(--gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef) * 0)); max-width: calc(100% - (var(--gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef) * 0)); }
@media screen and (max-width: 840px){
.sd[data-s-ab019cb8-0583-4795-ae7a-a08628f4396d] { flex: none; height: auto; width: calc(100% - (var(--gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef) * 0)); max-width: calc(100% - (var(--gap-h-f82d0098-71aa-4e34-a5e4-acc2cae50aef) * 0)); }
}.sd[data-s-91b4e424-f16e-4114-b2fd-d0c8f026865e] { color: #FFFFFF; flex: none; font-family: var(--s-font-3a8e7aa2); font-size: 20px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: 100%; max-width: 100%; justify-content: flex-start; }
@media screen and (max-width: 540px){
.sd[data-s-91b4e424-f16e-4114-b2fd-d0c8f026865e] { font-size: 18px; width: 100%; }
}.sd[data-s-93e51031-b746-465b-9d5d-f80f5a32c82b] { align-content: center; align-items: center; background: #474bff; border-radius: 25px; flex: none; flex-direction: row; flex-wrap: nowrap; gap: 5px; height: 50px; justify-content: center; padding: 0px 20px; width: auto; --gap-h-93e51031-b746-465b-9d5d-f80f5a32c82b: 5px; --gap-v-93e51031-b746-465b-9d5d-f80f5a32c82b: 0px; --gap-uuid: 93e51031-b746-465b-9d5d-f80f5a32c82b; max-width: 100%; }
.sd[data-s-93e51031-b746-465b-9d5d-f80f5a32c82b]:hover { background: #FFFFFF; --gap-h-93e51031-b746-465b-9d5d-f80f5a32c82b: 5px; --gap-v-93e51031-b746-465b-9d5d-f80f5a32c82b: 0px; }.sd[data-s-93da1d88-02c4-4259-b8d2-2016f81e27f5] { background: rgba(0,0,0,0.0); color: #FFFFFF; font-size: 24px; }
.sd[data-s-93e51031-b746-465b-9d5d-f80f5a32c82b]:hover .sd[data-s-93da1d88-02c4-4259-b8d2-2016f81e27f5] { color: #0c76ff; }.sd[data-s-5d835d35-0df4-4013-8330-61826104a804] { background: rgba(0,0,0,0.0); color: #FFFFFF; font-family: var(--s-font-d39b4375); font-size: 15px; font-weight: 400; height: auto; line-height: 1.4; text-align: left; width: auto; max-width: 100%; justify-content: flex-start; }
.sd[data-s-93e51031-b746-465b-9d5d-f80f5a32c82b]:hover .sd[data-s-5d835d35-0df4-4013-8330-61826104a804] { color: #0c76ff; }.sd[data-s-87e7b66c-a808-4870-941a-d23b8e59a1aa] {  }.sd[data-s-91965bb3-2b75-4195-9ff0-96848dad8475] {  }</style><style data-v-5a0c3720="">body { background: #FFFFFF; transition: background 0.5s cubic-bezier(0.4, 0.4, 0, 1); }</style><!-- --><style></style><div aria-live="assertive" class="TitleAnnouncer" data-v-692a2727="">All you need | shimoda has loaded</div><div aria-live="assertive" class="LoadMoreAnnouncer" data-v-4f7a7294=""></div><!-- --><div><div class="modals"></div><div aria-hidden="false" class="StudioCanvas"><div class="sd appear" data-s-b9f0bc5b-657c-4067-9226-82c94e86d992=""><section class="section sd appear" data-s-8e34e82f-86d9-461d-a287-c40165610ddd="" id="top-e"><div class="section-inner sd appear" data-s-section-inner-8e34e82f-86d9-461d-a287-c40165610ddd=""><div class="sd appear" data-s-bc8ad9fe-3dac-48a7-b7cd-fc9d29892062=""></div><div class="image sd" data-r-1_0_0_f605ec9a-a8e1-499c-ab86-1f1f983a0ac1="" data-s-f605ec9a-a8e1-499c-ab86-1f1f983a0ac1=""><style>.sd[data-r-1_0_0_f605ec9a-a8e1-499c-ab86-1f1f983a0ac1]:before { background-image: url("https://storage.googleapis.com/studio-design-asset-files/projects/BRO376opOD/s-2050x1536_v-frms_webp_8e0ca735-4d58-4687-a183-cce0041c72e8_regular.webp") }</style></div><div class="sd appear" data-s-fa94f79a-18ef-4159-ae3a-2872ecb101eb=""><div class="sd appear" data-s-2b6974ae-3964-413f-b1ea-ac5c6eadadc0=""><p class="text sd" data-r-0_0_2_0_0_afd551a5-cfc8-4823-a88f-398434bf74fc="" data-s-afd551a5-cfc8-4823-a88f-398434bf74fc="">ALL YOU NEED</p><div class="sd appear" data-s-22479ede-b76e-408e-bbd8-4a9b4733253f=""></div></div><div class="sd appear" data-s-93512e98-babe-440b-866c-2d3c9d3eb06a=""><p class="text sd" data-r-0_1_2_0_0_caa2e456-c0cf-485d-8562-7439ee6b345d="" data-s-caa2e456-c0cf-485d-8562-7439ee6b345d="">surf&amp;chill</p><div class="sd appear" data-s-c0b01087-faf6-459a-9302-d6d103eb2ba8=""><div class="sd" data-s-5703992c-eb95-4648-b164-935f951ecac6=""></div></div></div></div></div></section><section class="section sd appear" data-s-381515d9-e7ca-4e30-919d-a420fee64f8d=""><div class="section-inner sd appear" data-s-section-inner-381515d9-e7ca-4e30-919d-a420fee64f8d=""><div class="sd appear" data-s-28b29881-0054-4096-bf1e-0a74f973486c="" id="abouut-e"><p class="text sd appear" data-r-0_0_0_1_7946b935-7fbb-4405-a21b-de6fa2dc0240="" data-s-7946b935-7fbb-4405-a21b-de6fa2dc0240="" id="about">About us<br/></p><p class="text sd appear" data-r-1_0_0_1_47ecc7c9-5825-4d2e-a745-ecc1a1cee871="" data-s-47ecc7c9-5825-4d2e-a745-ecc1a1cee871="">We want to create a place where people who love the sea, nature and travel can come together here in Izu Shimoda!<br/>To make that dream come true, we renovated and finally completed it!<br/>I want to tell visitors from all over the world about the beauty of Shimoda, especially those who come to Japan from all over the world to see the ocean.<br/>Once you've visited, you'll want to come back again, in a guest house with that kind of space.<br/></p></div><div class="sd appear" data-s-e0444394-7ff4-4ee1-a7bc-87b94f1a92e7=""><div class="sd appear" data-s-2e0cc15a-66eb-4dc0-b20f-4038707e8753=""><div class="image sd appear" data-r-0_0_1_0_1_e67c0cce-4225-4551-9d50-484f198d7fe5="" data-s-e67c0cce-4225-4551-9d50-484f198d7fe5=""><style>.sd[data-r-0_0_1_0_1_e67c0cce-4225-4551-9d50-484f198d7fe5]:before { background-image: url("https://storage.googleapis.com/studio-design-asset-files/projects/BRO376opOD/s-1242x919_v-fms_webp_c201bf3f-3f09-4ea3-ae43-1134dfcdd1a2_small.webp") }</style></div></div><div class="sd appear" data-s-6a9564b4-5e7e-4471-85bb-fd64af0e2337=""><div class="image sd appear" data-r-0_1_1_0_1_502b8a46-45d8-43a6-b55c-d1a7d5a6ea43="" data-s-502b8a46-45d8-43a6-b55c-d1a7d5a6ea43=""><style>.sd[data-r-0_1_1_0_1_502b8a46-45d8-43a6-b55c-d1a7d5a6ea43]:before { background-image: url("https://storage.googleapis.com/studio-design-asset-files/projects/BRO376opOD/s-1125x833_v-fs_webp_b5f6dd61-e753-4470-8489-f5f2ea9317bc_small.webp") }</style></div></div><div class="sd appear" data-s-6eaac50e-5e34-4209-af24-51e87a273ce3=""><div class="image sd appear" data-r-0_2_1_0_1_bec2f327-a55c-4629-8122-d02980b594f8="" data-s-bec2f327-a55c-4629-8122-d02980b594f8=""><style>.sd[data-r-0_2_1_0_1_bec2f327-a55c-4629-8122-d02980b594f8]:before { background-image: url("https://storage.googleapis.com/studio-design-asset-files/projects/BRO376opOD/s-1774x2364_v-frms_webp_8cd9a2d0-fedc-42bb-aa3b-635fb4b7ac97_small.webp") }</style></div></div></div><a class="link sd appear" data-s-38e936a7-88a2-4694-97fb-8acc50156d19="" href="/about-english#top"><i class="icon material-icons sd appear" data-s-d1cbc4d5-3931-41e6-a907-3f4d2f56b60c="">done</i><p class="text sd appear" data-r-1_2_0_1_6aa5013b-07ee-4341-b8cd-20f77599fdf6="" data-s-6aa5013b-07ee-4341-b8cd-20f77599fdf6="">More</p></a></div></section><section class="section sd appear" data-s-ebe6b342-9750-4c20-af59-4afb68f1decb="" id="rental-e"><div class="section-inner sd appear" data-s-section-inner-ebe6b342-9750-4c20-af59-4afb68f1decb=""><div class="sd appear" data-s-0da8802e-ace1-46fe-96bd-ecfc6ac1ea31="" id="rental"><p class="text sd appear" data-r-0_0_0_2_44ba734f-fb9c-4a69-9fb2-ecbd17881ff0="" data-s-44ba734f-fb9c-4a69-9fb2-ecbd17881ff0="">レンタル</p><p class="text sd appear" data-r-1_0_0_2_ecd0eed9-546a-46a9-9e83-86cf12dce6a0="" data-s-ecd0eed9-546a-46a9-9e83-86cf12dce6a0="">Rental</p></div><div class="sd appear" data-s-cf27b130-760f-4c90-bb5c-9023c574397d=""><div class="sd appear" data-s-8dbe8b45-3ac7-4ebd-8401-dd2d65d15312=""><div class="sd appear" data-s-250ae657-bb76-4f54-b5f6-dddd2a095d00=""><div class="sd appear" data-s-f129e4cc-c5ee-4040-bea8-b8789cd232cd=""><p class="text sd appear" data-r-0_0_0_0_1_0_2_99844f01-a017-44e4-b200-9f7b2e43eb03="" data-s-99844f01-a017-44e4-b200-9f7b2e43eb03="">Lending<br/></p><p class="text sd appear" data-r-1_0_0_0_1_0_2_1b30ef1d-cb77-4379-b3a3-22e909bd0c34="" data-s-1b30ef1d-cb77-4379-b3a3-22e909bd0c34="">About the rentals from ALL YOU NEED<br/></p><p class="text sd appear" data-r-2_0_0_0_1_0_2_112ca6f5-e628-42f4-bc6e-64b7794a38ba="" data-s-112ca6f5-e628-42f4-bc6e-64b7794a38ba="">We offer rentals to ensure our visitors have an enjoyable experience. We provide rentals for surfboards and bicycles, and some items are available for free.<br/><br/>For more details, please check the link below.</p></div></div><div class="sd appear" data-s-42f9cc28-5359-4772-97da-f6e47776a691=""><div class="sd appear" data-s-4bff5899-65f5-489d-ac26-82611a23c802=""></div><div class="image sd appear" data-r-1_1_0_1_0_2_a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81="" data-s-a8b0f3a3-dac0-4bf5-8544-ac1fba3f3a81=""><style></style></div></div></div><a class="link sd appear" data-s-4aa07de4-521f-4250-bcf0-6229321c09ee="" href="/rental-english"><i class="icon material-icons sd appear" data-s-c39709b0-8cda-42a5-8e8c-ab9cfb4cfb1f="">done</i><p class="text sd appear" data-r-1_1_1_0_2_dbfe7c79-de52-41fd-8702-07c99a1af240="" data-s-dbfe7c79-de52-41fd-8702-07c99a1af240="">More</p></a></div></div></section><section class="section sd appear" data-s-7ae503bb-ee2d-4b16-930d-656444dbd3fa=""><div class="section-inner sd appear" data-s-section-inner-7ae503bb-ee2d-4b16-930d-656444dbd3fa=""><div class="sd appear" data-s-c9ed9a2d-a307-4f83-b45b-ef843e98a286=""><p class="text sd appear" data-r-0_0_0_3_fc09918d-f338-46ee-bf89-e114f37f03bf="" data-s-fc09918d-f338-46ee-bf89-e114f37f03bf="">部屋と施設設備</p><p class="text sd appear" data-r-1_0_0_3_98a7436a-9122-40fb-aac0-1b4932252045="" data-s-98a7436a-9122-40fb-aac0-1b4932252045="" id="room-e">Room&amp;Facility</p></div><div class="sd appear" data-s-1b827ff3-e35b-46f3-8c82-71eabfbafbd4=""><div class="sd appear" data-s-a63e3dac-0af1-496b-bc3e-345c17ac4f14=""><div class="sd appear" data-s-d4606c2c-6b9a-464b-8c24-a70c4a789e4f=""><div class="sd appear" data-s-ee3332e5-650f-4c45-8229-26a230486bee=""><p class="text sd appear" data-r-0_0_0_0_1_0_3_0983143b-9e1b-4c82-afd1-2db89981c87f="" data-s-0983143b-9e1b-4c82-afd1-2db89981c87f="">Guest rooms</p><p class="text sd appear" data-r-1_0_0_0_1_0_3_e05275b4-8d42-4644-b6d9-9c8b5601832f="" data-s-e05275b4-8d42-4644-b6d9-9c8b5601832f="">All rooms have an ocean view.<br/>We offer a total of nine rooms.<br/></p><p class="text sd appear" data-r-2_0_0_0_1_0_3_70ac57fe-ffb9-4a9b-a68c-a5cf38bfaf9b="" data-s-70ac57fe-ffb9-4a9b-a68c-a5cf38bfaf9b="">We offer a variety of rooms to ensure our guests have a comfortable stay.<br/>All rooms are spacious and offer an ocean view.<br/><br/>For more details, please check the link below.</p></div></div><div class="sd appear" data-s-28bc8e7e-4406-4014-b19b-a1456037080a=""><div class="image sd appear" data-r-0_1_0_1_0_3_56bd6f95-dd12-4fcf-890d-93219b79c9ca="" data-s-56bd6f95-dd12-4fcf-890d-93219b79c9ca=""><style></style></div><div class="sd appear" data-s-8cbb496b-07a0-4b06-9359-bfeb03b6d84e=""></div></div></div><a class="link sd appear" data-s-78be8712-a4cd-4890-8ed5-80e9f7efcc23="" href="/room-english"><i class="icon material-icons sd appear" data-s-e9f46706-0ba6-4d71-b30a-8ff9c9a817d3="">done</i><p class="text sd appear" data-r-1_1_1_0_3_7ef1a4b1-8f60-42be-82f8-12427f687739="" data-s-7ef1a4b1-8f60-42be-82f8-12427f687739="">More</p></a></div></div></section><section class="section sd appear" data-s-b5420ef4-bff7-47f0-9ef8-7e9a295e9878=""><div class="section-inner sd appear" data-s-section-inner-b5420ef4-bff7-47f0-9ef8-7e9a295e9878=""><div class="sd appear" data-s-237e9257-ad59-4d8f-be67-469525293df4=""><p class="text sd appear" data-r-0_0_0_4_606842d7-f4cd-43ac-bcaf-4fb8a8b521df="" data-s-606842d7-f4cd-43ac-bcaf-4fb8a8b521df="">会社概要</p><p class="text sd appear" data-r-1_0_0_4_4a7c4d0a-cf8d-42e1-8f9e-4119aa44c3af="" data-s-4a7c4d0a-cf8d-42e1-8f9e-4119aa44c3af="">Company</p></div><div class="sd appear" data-s-c110c662-efbd-49b0-868a-4baab1051363=""><div class="sd appear" data-s-96df9cb6-914a-4726-8789-667730aba2e0=""><div class="sd appear" data-s-a94d9df5-4b0a-4f71-b4d0-035764f4b19c=""><div class="sd appear" data-s-2fa77ee0-da6a-4015-8e33-cbe2dfe65d81=""><p class="text sd appear" data-r-0_0_0_0_1_0_4_f45c883d-188a-49af-b7f6-e69f58c359ba="" data-s-f45c883d-188a-49af-b7f6-e69f58c359ba="">Company name</p><p class="text sd appear" data-r-1_0_0_0_1_0_4_af48d012-8268-417d-a34b-04e8c084707c="" data-s-af48d012-8268-417d-a34b-04e8c084707c=""> ALL YOU NEED Co., Ltd.</p></div><div class="sd appear" data-s-6e46a44a-f234-4f52-9b3e-9ff58001c4ae=""><p class="text sd appear" data-r-0_1_0_0_1_0_4_11bf299c-5149-479b-b5fb-fb60ce33cf39="" data-s-11bf299c-5149-479b-b5fb-fb60ce33cf39="">Store name</p><p class="text sd appear" data-r-1_1_0_0_1_0_4_2a782afc-b61c-45e1-9558-91f1a0898f33="" data-s-2a782afc-b61c-45e1-9558-91f1a0898f33="">Guesthouse All you need ~surf&amp;chill~</p></div><div class="sd appear" data-s-83365c62-e20e-45ed-b568-00cb47890261=""><p class="text sd appear" data-r-0_2_0_0_1_0_4_40f4c0ea-c314-41f6-928d-e5758a11a2b3="" data-s-40f4c0ea-c314-41f6-928d-e5758a11a2b3="">Email address</p><p class="text sd appear" data-r-1_2_0_0_1_0_4_d3226a1c-8736-47b0-9602-cd9e74e268ca="" data-s-d3226a1c-8736-47b0-9602-cd9e74e268ca=""><EMAIL><br/></p></div><div class="sd appear" data-s-f47dd45c-35fe-45d9-9988-7b82bcbdac9a=""><p class="text sd appear" data-r-0_3_0_0_1_0_4_9797c98e-1675-4f3b-a0d7-cef867f20e0b="" data-s-9797c98e-1675-4f3b-a0d7-cef867f20e0b="">Telephone number</p><p class="text sd appear" data-r-1_3_0_0_1_0_4_ea95dc6f-343d-4d2d-ae4d-a350fc38ad5f="" data-s-ea95dc6f-343d-4d2d-ae4d-a350fc38ad5f="">090-8575-8812<br/>0558-36-4132 <br/>(Available for inquiries regarding check-in time only)<br/>*Please note that the phone may not be connected during cleaning etc.<br/>*Please refrain from making inquiries for commercial purposes.<br/></p></div></div></div><div class="sd appear" data-s-f61be54b-6d6c-420a-a4c7-ffea9f87f026=""><img alt="" class="sd appear" data-s-0a35f4f9-9e4d-4fb1-88c5-0b9e0c36ab13="" src="data:image/svg+xml;charset=utf-8,%3Csvg width='1242' height='919' xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E"/></div></div></div></section><section class="section sd appear" data-s-7f9a707c-b1d1-40eb-9c2f-717623ef3be9=""><div class="section-inner sd appear" data-s-section-inner-7f9a707c-b1d1-40eb-9c2f-717623ef3be9=""><div class="sd appear" data-s-e062f0d5-5024-4c13-8590-001afed40692="" id="access-e"><p class="text sd appear" data-r-0_0_0_5_993927b0-0eb4-4f57-975a-87fb50e8629f="" data-s-993927b0-0eb4-4f57-975a-87fb50e8629f="">アクセス</p><p class="text sd appear" data-r-1_0_0_5_393c2280-d000-42fb-9f97-3b8d0b0611cf="" data-s-393c2280-d000-42fb-9f97-3b8d0b0611cf="">Access</p></div><div class="sd appear" data-s-bc073913-4020-45fc-ac46-47e91cf86da4=""><div class="sd appear" data-s-c8da2f54-41d4-4561-a1f3-c25ffbc8f2a6=""><div class="sd appear" data-s-13df7335-8113-45d7-84ca-7c7f97a55eb8=""><div class="sd appear" data-s-b2a6bd52-62a1-4105-ab8f-734cee333e39=""><p class="text sd appear" data-r-0_0_0_0_1_0_5_f79b54f7-6355-4ced-a6ec-c40001f956d8="" data-s-f79b54f7-6355-4ced-a6ec-c40001f956d8="">Access</p><p class="text sd appear" data-r-1_0_0_0_1_0_5_ee64fc93-9ef4-4d0c-b031-68047e0c789b="" data-s-ee64fc93-9ef4-4d0c-b031-68047e0c789b="">Izukyu Shimoda Station : 5 minutes by car.<br/>                                 About 20 minutes on foot.<br/></p></div><div class="sd appear" data-s-a1e8dfd7-486c-4ce5-9b0d-ce8f03e75bfc=""><p class="text sd appear" data-r-0_1_0_0_1_0_5_d85acbc0-4760-4110-bb24-211ac65325bf="" data-s-d85acbc0-4760-4110-bb24-211ac65325bf="">The way to get there</p><p class="text sd appear" data-r-1_1_0_0_1_0_5_abf4c63f-5021-4f1d-bf29-ed6ec0cc581e="" data-s-abf4c63f-5021-4f1d-bf29-ed6ec0cc581e="">You can reach Izukyu Shimoda Station from Tokyo Station in about 2 hours and 30 minutes by using the limited express train "Odoriko."</p></div><div class="sd appear" data-s-6996ae51-0caf-4246-a4a9-100e90359ce6=""><p class="text sd appear" data-r-0_2_0_0_1_0_5_c998b0a5-8252-445b-b55c-528e8802245c="" data-s-c998b0a5-8252-445b-b55c-528e8802245c="">Address</p><p class="text sd appear" data-r-1_2_0_0_1_0_5_8f4e5c2c-3b78-49d4-9e3e-0f5af369ed1d="" data-s-8f4e5c2c-3b78-49d4-9e3e-0f5af369ed1d="">〒415-0023<br/>25-30, Sanchome, Shimoda City, Shizuoka Prefecture</p></div></div><div class="sd appear" data-s-2f953087-f6b6-4e5e-98fc-af8df44bcf09=""><div class="frame sd appear" data-s-ad0743f8-ff76-4085-9ba3-5b34deef4cb7=""></div></div></div></div></div></section><section class="section sd appear" data-s-eec6c358-8e75-44e2-84ff-9b94cbf18c2e=""><div class="section-inner sd appear" data-s-section-inner-eec6c358-8e75-44e2-84ff-9b94cbf18c2e=""><div class="sd appear" data-s-fd84fe9b-3821-42d2-bf88-560e11664c4c=""><p class="text sd appear" data-r-0_0_0_6_d0c280c7-da68-47b9-9c92-2160fb2b1e22="" data-s-d0c280c7-da68-47b9-9c92-2160fb2b1e22="">ニュース</p><p class="text sd appear" data-r-1_0_0_6_c7d223f4-b90b-4036-9e2d-8110a6ed0da0="" data-s-c7d223f4-b90b-4036-9e2d-8110a6ed0da0="" id="news">NEWS</p></div><div class="sd appear" data-s-60232db1-4264-402d-a322-d030c263f6cb=""><div class="sd appear" data-s-12e5f71b-3844-4312-bd65-479c54a1ef96=""></div><div class="sd appear" data-s-53048a58-73bd-4bf7-9366-c66369a419be=""><div class="sd appear" data-s-eb915849-d94b-4d03-94fe-6b341938395a=""><p class="text sd appear" data-r-0_0_1_1_0_6_7154acfd-bc22-4ba5-afbf-0c8a74132727="" data-s-7154acfd-bc22-4ba5-afbf-0c8a74132727="">New arrivals<br/></p></div><div class="sd appear" data-s-4e8b7176-7a94-4c96-9246-25e0d1b165e9=""><div class="sd appear" data-s-9c777532-a93d-454a-8ca5-684d81bc942c=""><ul class="sd appear" data-s-adc05688-73ca-471b-8242-b30b98aca0f0=""><li class="sd appear" data-s-3915dd87-3485-40c9-be32-8d3162df58c5=""><div class="sd appear" data-s-23530ee2-5ad9-4fc4-ac0c-49023defadc7=""><div class="sd appear" data-s-4306a94c-d7f2-48ff-bd40-75b31b7476ef=""><p class="text sd appear" data-r-0_0_0_0_0_0_1_1_1_0_6_c5817b81-fb8a-45b2-a9ca-d89722aedc72="" data-s-c5817b81-fb8a-45b2-a9ca-d89722aedc72="">2024/6/19 20:53</p></div><a class="text link sd appear" data-r-1_0_0_0_0_1_1_1_0_6_11b71a83-75c7-40c4-adf6-f9587cb436e2="" data-s-11b71a83-75c7-40c4-adf6-f9587cb436e2="" href="/posts/0002">サップレンタルについてのお知らせ</a><a class="text link sd appear" data-r-2_0_0_0_0_1_1_1_0_6_1304ef54-3598-404b-a6e9-e1dfc45298cd="" data-s-1304ef54-3598-404b-a6e9-e1dfc45298cd="" href="/posts/0002"> IMPORTANT ANNOUNCEMENT -We will be ending our Sta...</a></div></li><li class="sd appear" data-s-3915dd87-3485-40c9-be32-8d3162df58c5=""><div class="sd appear" data-s-23530ee2-5ad9-4fc4-ac0c-49023defadc7=""><div class="sd appear" data-s-4306a94c-d7f2-48ff-bd40-75b31b7476ef=""><p class="text sd appear" data-r-0_0_0_1_0_0_1_1_1_0_6_c5817b81-fb8a-45b2-a9ca-d89722aedc72="" data-s-c5817b81-fb8a-45b2-a9ca-d89722aedc72="">2024/6/19 20:51</p></div><a class="text link sd appear" data-r-1_0_1_0_0_1_1_1_0_6_11b71a83-75c7-40c4-adf6-f9587cb436e2="" data-s-11b71a83-75c7-40c4-adf6-f9587cb436e2="" href="/posts/0001">夏季限定のフードトラックを募集します</a><a class="text link sd appear" data-r-2_0_1_0_0_1_1_1_0_6_1304ef54-3598-404b-a6e9-e1dfc45298cd="" data-s-1304ef54-3598-404b-a6e9-e1dfc45298cd="" href="/posts/0001">.今年からGuesthouse All you needの駐車場が増えた事に伴い、夏季限定でフードト...</a></div></li></ul></div></div></div><div class="sd appear" data-s-9955cdc1-9828-48bb-8cc4-101c33cc95a5=""><div class="sd appear" data-s-53278b6a-0558-4ea2-932f-c1741065364d=""></div></div></div></div></section><div class="image fixed sd" data-r-7_7ef36464-efff-4b51-adc7-8eeaa26447c2="" data-s-7ef36464-efff-4b51-adc7-8eeaa26447c2=""><style>.sd[data-r-7_7ef36464-efff-4b51-adc7-8eeaa26447c2]:before { background-image: url("https://storage.googleapis.com/studio-design-asset-files/projects/BRO376opOD/s-2400x1799_v-frms_webp_ae4b6ecd-ebeb-4d01-9cf1-69e6bfd6ad54.webp") }</style></div><div class="sd appear" data-s-f7009d78-f595-467f-bb0d-f586fb3db9ce="" id="reservation-e"><p class="text sd appear" data-r-0_8_8c665269-c7a9-4963-8079-af47f57bdf58="" data-s-8c665269-c7a9-4963-8079-af47f57bdf58="">予約</p><p class="text sd appear" data-r-1_8_7a1341f6-63b7-4f1c-a87e-a673e157205a="" data-s-7a1341f6-63b7-4f1c-a87e-a673e157205a="" id="news">Reservation</p></div><section class="section sd appear" data-s-e727b092-6dd0-43d4-9145-4fbb2e711d06=""><div class="section-inner sd appear" data-s-section-inner-e727b092-6dd0-43d4-9145-4fbb2e711d06=""><div class="sd appear" data-s-8576d803-f474-4678-a7f2-6cbe0b2e98f2=""><div class="sd appear" data-s-268a50c4-0f2e-4445-b28f-657cbd76c877=""><p class="text sd appear" data-r-0_0_0_0_9_fd91d9c8-a502-4dcf-b0ed-0fc6b04e9580="" data-s-fd91d9c8-a502-4dcf-b0ed-0fc6b04e9580="">Before making a reservation</p></div><div class="sd appear" data-s-31d503bc-2ab5-4eaa-9c0d-d4ccacc596be=""><div class="sd appear" data-s-cd00290f-f286-4b48-b272-670c2285e0c6=""><i class="icon material-icons sd appear" data-s-e4c199ee-99ea-4994-af87-ca089b8a1895="">trending_up</i><p class="text sd appear" data-r-1_0_1_0_0_9_7c17e287-52db-45e7-b317-36d092640433="" data-s-7c17e287-52db-45e7-b317-36d092640433="">Check-in 16:00~19:00 Check-out 10:30</p></div></div><div class="sd appear" data-s-10d4fb44-662e-44ba-9e23-5ff43bdb508c=""><div class="sd appear" data-s-93f99ba8-97d0-468e-a8f3-e71dce03555d=""><i class="icon material-icons sd appear" data-s-5a550853-0fa4-4aaa-bfcc-8c7561a9234c="">trending_up</i><p class="text sd appear" data-r-1_0_2_0_0_9_b304effa-968a-4178-9ac8-1158291dab55="" data-s-b304effa-968a-4178-9ac8-1158291dab55="">Non-smoking throughout the building (smoking area is only next to the entrance on the first floor)</p></div></div><div class="sd appear" data-s-acbadb01-52d4-4fbb-9a35-747b672b7276=""><div class="sd appear" data-s-956677d1-c72b-4d5b-a78b-04c7450bf659=""><i class="icon material-icons sd appear" data-s-0c1fb2ff-8d8c-4f85-b0b4-824083d66bab="">trending_up</i><p class="text sd appear" data-r-1_0_3_0_0_9_218b172b-5961-49de-8167-e91b54510167="" data-s-218b172b-5961-49de-8167-e91b54510167="">Shared space light off time 22:30.</p></div></div><div class="sd appear" data-s-*************-4268-88d5-6f2e64406af7=""><div class="sd appear" data-s-2ccf4a5a-299e-4add-99ba-53296f23254a=""><i class="icon material-icons sd appear" data-s-cbe04342-8cf8-4c05-af45-1239e5873715="">trending_up</i><p class="text sd appear" data-r-1_0_4_0_0_9_cfde33da-0326-43f7-988f-da112dab8a46="" data-s-cfde33da-0326-43f7-988f-da112dab8a46="">Please quiet in the shared space and rooms after 9:00 pm.</p></div></div><div class="sd appear" data-s-8093f99c-07b6-4a54-9a01-1f2532137044=""><div class="sd appear" data-s-8749ca00-acb8-495f-8467-9a159d1c25a8=""><i class="icon material-icons sd appear" data-s-2f54a351-b913-4f6c-9fcd-bc07db148d61="">trending_up</i><p class="text sd appear" data-r-1_0_5_0_0_9_56a77424-2733-4a52-8fa0-8f2c69390939="" data-s-56a77424-2733-4a52-8fa0-8f2c69390939="">Smoking and eating in the rooms is not allowed. (Please eat in the common area on the 2nd floor.)</p></div></div><div class="sd appear" data-s-49317c6f-a9e3-4a5b-a512-960766fa5021=""><div class="sd appear" data-s-7b557116-ab41-40d3-b408-7d78eba4cc75=""><i class="icon material-icons sd appear" data-s-c6912d00-3f61-4e9f-97e4-59c7e92265d8="">trending_up</i><p class="text sd appear" data-r-1_0_6_0_0_9_8516a921-7250-4b19-be18-42e3c14ef4fb="" data-s-8516a921-7250-4b19-be18-42e3c14ef4fb="">Meals are not provided.</p></div></div><div class="sd appear" data-s-a9a11908-d97b-479f-ba78-ce5a3f3488b7=""><div class="sd appear" data-s-9413258a-14cb-4d40-92a9-b0b5709ef25f=""><i class="icon material-icons sd appear" data-s-895fc7e3-d8e0-411f-9256-63951b03a86d="">trending_up</i><p class="text sd appear" data-r-1_0_7_0_0_9_293169e0-b947-4311-a5d6-677dbb666654="" data-s-293169e0-b947-4311-a5d6-677dbb666654="">Laundry is not provided.(The spin dryer and clothes drying rack are available for use.)</p></div></div><div class="sd appear" data-s-c488c412-7271-47c6-bb11-7e7cd085d6e6=""><div class="sd appear" data-s-9d1dd2a8-ba00-4a0b-91c0-6139dca0e997=""><i class="icon material-icons sd appear" data-s-c680230f-7395-4246-ae2d-282c53ee3fbf="">trending_up</i><p class="text sd appear" data-r-1_0_8_0_0_9_76ae00d5-bc03-444f-9f97-dd447bed71dc="" data-s-76ae00d5-bc03-444f-9f97-dd447bed71dc="">Our guest house has slopes and stairs. Please note that there is no elevator on-site.</p></div></div><div class="sd appear" data-s-34e5a1c5-1a3e-458d-950b-fcf46fe25a14=""><div class="sd appear" data-s-8718a896-bd05-4cbd-bfc5-47f901cb1ada=""><i class="icon material-icons sd appear" data-s-1f756f70-0f8c-4a56-8dc9-d82da46326a2="">trending_up</i><p class="text sd appear" data-r-1_0_9_0_0_9_d2037801-58ff-4a7c-a80b-ae5a6583aa18="" data-s-d2037801-58ff-4a7c-a80b-ae5a6583aa18="">Please note for wheelchair users or those with limited mobility.</p></div></div><div class="sd appear" data-s-46555ff5-91fb-4c8d-b718-7eb11dd14eca=""><p class="text sd appear" data-r-0_10_0_0_9_e1149f6d-520e-4765-a356-978738d401bb="" data-s-e1149f6d-520e-4765-a356-978738d401bb="">You can also book through Instagram or phone call!<br/></p></div></div><div class="sd appear" data-s-0fb5ef8e-783d-447b-a470-49519f1d49b2=""><a class="sd appear" data-s-8bdd81b2-845b-4a3b-abb8-40c049301a60="" href="https://www.booking.com/hotel/jp/guesthouse-all-you-need.ja.html" rel="noopener" target="_blank"><div class="sd appear" data-s-9e9ae698-d200-4bf7-af14-9634cc8b0a70=""><p class="text sd appear" data-r-0_0_0_1_0_9_91e07e67-d483-460e-88d4-6795622dd089="" data-s-91e07e67-d483-460e-88d4-6795622dd089="">Reservation</p></div></a><a class="icon fa-brands fa-instagram sd appear" data-s-213a3507-ee21-49d2-9b69-61c6b2feeab5="" href="https://www.instagram.com/allyouneed2020/?hl=ja" rel="noopener" target="_blank"></a></div></div></section><section class="section sd appear" data-s-00cf3d00-f69a-478c-a207-dc28048a3f44=""><div class="section-inner sd appear" data-s-section-inner-00cf3d00-f69a-478c-a207-dc28048a3f44=""><div class="sd appear" data-s-1721453a-de2e-4eea-b937-2864bdeca523=""><p class="text sd appear" data-r-0_0_0_10_4424b192-1fe7-4e8d-a75e-d9ce6bc7827c="" data-s-4424b192-1fe7-4e8d-a75e-d9ce6bc7827c="">If you have any questions or concerns, please feel free to cont Instagram DM us</p><div class="sd appear" data-s-f82d0098-71aa-4e34-a5e4-acc2cae50aef=""><div class="sd appear" data-s-ab019cb8-0583-4795-ae7a-a08628f4396d=""><p class="text sd appear" data-r-0_0_1_0_0_10_91b4e424-f16e-4114-b2fd-d0c8f026865e="" data-s-91b4e424-f16e-4114-b2fd-d0c8f026865e="">At ALL YOU NEED, we strive to ensure customer satisfaction to the best of our ability. If you have any concerns or need assistance, please feel free to contact us.</p></div><a class="sd appear" data-s-93e51031-b746-465b-9d5d-f80f5a32c82b="" href="https://www.instagram.com/allyouneed2020/?hl=ja"><i class="icon fa-brands fa-instagram sd appear" data-s-93da1d88-02c4-4259-b8d2-2016f81e27f5=""></i><p class="text sd appear" data-r-1_1_1_0_0_10_5d835d35-0df4-4013-8330-61826104a804="" data-s-5d835d35-0df4-4013-8330-61826104a804=""> Instagram</p></a></div></div></div></section><div class="fixed sd appear" data-s-626b3c93-8e37-4376-a7e7-2bff845e6631=""><a class="link sd appear" data-s-30815b26-c41c-4d54-abaa-e35a4df5dbd9="" href="/1"><img alt="" class="sd" data-s-03c83ec7-0569-4ba4-9546-8056e67597af="" src="http://dimg.fws.qa.nt.ctripcorp.com/images/2bb3t120000435fuj186F.webp"/></a><button class="link sd appear" data-s-e1eb933e-c0af-409a-b579-4bd72b30cfbe="" type="button"><i class="icon material-icons sd appear" data-s-7b0957c6-20b4-4994-bba9-d672493efb22="">menu</i></button><div class="sd appear" data-s-e1e8b2f3-9eb8-421a-b263-d4a08c56ac1f=""><a aria-current="page" class="link sd appear link-active" data-s-83c708ff-55d6-4f4e-b20b-a6331d0106c1="" href="/home-english#abouut-e"><p class="text sd appear" data-r-0_0_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_21c66407-c485-4439-a19e-cc1a13d9b0a1="" data-s-21c66407-c485-4439-a19e-cc1a13d9b0a1="">About us </p><div class="sd appear" data-s-bab849cf-7b8f-4ecf-8671-55c3c07377dd=""></div></a><a aria-current="page" class="link sd appear link-active" data-s-978c4cba-2f93-481b-951f-79e15e92f8b5="" href="/home-english#rental-e"><p class="text sd appear" data-r-0_1_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_60e95b71-261b-4f3e-8296-e617ab7e91f1="" data-s-60e95b71-261b-4f3e-8296-e617ab7e91f1="">Rental </p><div class="sd appear" data-s-dd2b0e84-71f6-4f6c-9f94-7a35cc56a6fd=""></div></a><a aria-current="page" class="link sd appear link-active" data-s-f7d69582-1678-478b-8e1d-662e0e7e3687="" href="/home-english#room-e"><p class="text sd appear" data-r-0_2_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_c306ca52-b071-446a-952d-9879ea938334="" data-s-c306ca52-b071-446a-952d-9879ea938334="">Room&amp;Facility </p><div class="sd appear" data-s-4055dbd9-8d5a-4ba4-af8c-9f0a3da13b76=""></div></a><a aria-current="page" class="link sd appear link-active" data-s-27a49796-792c-4254-9fb7-8e0186d7a13f="" href="/home-english#access-e"><p class="text sd appear" data-r-0_3_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_abea6909-3545-411a-af4c-7a5e54dfac48="" data-s-abea6909-3545-411a-af4c-7a5e54dfac48="">Access </p><div class="sd appear" data-s-cf19597e-d899-4080-8883-3d55f8562c78=""></div></a><a class="link sd appear" data-s-87295b61-5055-431b-bf4c-f4e9a8f005e7="" href="/home"><p class="text sd appear" data-r-0_4_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_5cbc9a2f-1ecf-4ef1-b4ba-9e0232a80b63="" data-s-5cbc9a2f-1ecf-4ef1-b4ba-9e0232a80b63="">Japanese<br/></p><div class="sd appear" data-s-99bef3db-273e-44be-ab58-66584ed9bbf3=""></div></a><a aria-current="page" class="link sd appear link-active" data-s-46dfe9c8-a935-4e53-8d1e-b601eda36234="" href="/home-english#reservation-e"><i class="icon material-icons sd appear" data-s-db83e8a1-ca0c-492f-a9fb-c0ff47dbeaca="">mail_outline</i><p class="text sd appear" data-r-1_5_2_11_87e7b66c-a808-4870-941a-d23b8e59a1aa_b53dac1c-6dc0-4088-9ff6-2f49b032f0c9="" data-s-b53dac1c-6dc0-4088-9ff6-2f49b032f0c9="">Reservation </p></a></div></div><div class="sd appear" data-s-9af3a759-a25a-4214-86ca-c180325bf7bf=""><div class="sd appear" data-s-d9ae5ca7-bde8-4590-94be-d9c99412ad33=""><div class="sd appear" data-s-45b6e9c9-22cc-4a65-89b2-ed74fc3abb2a=""><img alt="" class="sd appear" data-s-e813169e-f643-4de0-8998-1e9ff3a3e005="" src="data:image/svg+xml;charset=utf-8,%3Csvg width='1124' height='1120' xmlns='http://www.w3.org/2000/svg'%3E%3C/svg%3E"/><p class="text sd appear" data-r-1_0_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_0f28ac89-c417-4660-a4cb-1e13a05d1503="" data-s-0f28ac89-c417-4660-a4cb-1e13a05d1503="">All You Need Co., Ltd.</p><p class="text sd appear" data-r-2_0_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_187071e0-52db-4c0b-8f18-75b8fcd031d2="" data-s-187071e0-52db-4c0b-8f18-75b8fcd031d2=""></p><p class="text sd appear" data-r-3_0_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_90a13d6f-a5ec-46c7-b882-f218536971fd="" data-s-90a13d6f-a5ec-46c7-b882-f218536971fd="">25-30, Sanchome, Shimoda-City, Shizuoka Prefecture<br/></p></div><div class="sd appear" data-s-f1fa17e4-7fcd-4045-a474-1a895b7998b1=""><a aria-current="page" class="link sd appear link-active" data-s-37486066-2caa-4f3a-8cd1-d0e84d9131a2="" href="/home-english"><p class="text sd appear" data-r-0_0_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_6ea98dce-ba6d-43b1-b414-fa555c61e2f2="" data-s-6ea98dce-ba6d-43b1-b414-fa555c61e2f2="">TOP<br/></p><i class="icon material-icons sd appear" data-s-312e643c-b41d-4716-a93e-25821ff789ac="">arrow_forward</i></a><a aria-current="page" class="link sd appear link-active" data-s-5f9ce921-c340-43a7-bc24-20261364cf69="" href="/home-english#abouut-e"><p class="text sd appear" data-r-0_1_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_5c380498-96d7-4554-a4dc-0512baec7785="" data-s-5c380498-96d7-4554-a4dc-0512baec7785="">About us </p><i class="icon material-icons sd appear" data-s-e6e7c0b4-6c02-436d-b19f-10c05c6fbb23="">arrow_forward</i></a><a aria-current="page" class="link sd appear link-active" data-s-5d1a0ce5-1930-4562-8174-7e0f7a43f4da="" href="/home-english#rental-e"><p class="text sd appear" data-r-0_2_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_6f79c737-59d1-4572-bfc5-08c8c9ee706b="" data-s-6f79c737-59d1-4572-bfc5-08c8c9ee706b="">Rental </p><i class="icon material-icons sd appear" data-s-2eb17631-c3b1-4b78-a899-68bbe7360984="">arrow_forward</i></a><a aria-current="page" class="link sd appear link-active" data-s-17ff5003-7f21-416e-8f43-170dd19bf7ef="" href="/home-english#room-e"><p class="text sd appear" data-r-0_3_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_3c7a9639-aaaf-41c3-992a-0dfbfef5ee63="" data-s-3c7a9639-aaaf-41c3-992a-0dfbfef5ee63="">Room&amp;Facility </p><i class="icon material-icons sd appear" data-s-a74d958c-6498-4621-85f3-0f7ac7951c65="">arrow_forward</i></a><a aria-current="page" class="link sd appear link-active" data-s-48826842-862b-4217-b700-886f2d5c348e="" href="/home-english#access-e"><p class="text sd appear" data-r-0_4_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_7f462786-0876-43f5-91c7-de6ca5870c77="" data-s-7f462786-0876-43f5-91c7-de6ca5870c77="">Access </p><i class="icon material-icons sd appear" data-s-0b117aeb-dc80-420a-87b8-f61e623d7bb1="">arrow_forward</i></a><a class="link sd appear" data-s-d5da77f6-39dc-4094-90e9-4a2f9344b61c="" href="/home"><p class="text sd appear" data-r-0_5_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_ca61848e-30a7-4395-bc57-403f91676d7e="" data-s-ca61848e-30a7-4395-bc57-403f91676d7e="">Japanese</p><i class="icon material-icons sd appear" data-s-9c9ca779-720a-41db-b9ad-bbc7190f41dd="">arrow_forward</i></a><a aria-current="page" class="link sd appear link-active" data-s-11e7f0ce-b4ea-4fde-95ae-8cab5b14e1e0="" href="/home-english#reservation-e"><p class="text sd appear" data-r-0_6_1_0_12_91965bb3-2b75-4195-9ff0-96848dad8475_8b0b8f15-36a0-4306-8063-e6c7deb420d6="" data-s-8b0b8f15-36a0-4306-8063-e6c7deb420d6="">Reservation</p><i class="icon material-icons sd appear" data-s-410924c2-0f9d-49fa-8267-cf0ee8006a2a="">arrow_forward</i></a></div></div><div class="sd appear" data-s-23c87a91-0838-42ec-8fc4-f8ce1a81e6e6=""><p class="text sd appear" data-r-0_1_12_91965bb3-2b75-4195-9ff0-96848dad8475_5feffac3-1485-4915-bfd1-e5081d9f330c="" data-s-5feffac3-1485-4915-bfd1-e5081d9f330c="">©copyright All you need ~surf&amp;chill~ All rights reserved.</p><div class="sd appear" data-s-4b0de1a8-a816-43f7-a4c8-30b1500ce689=""><a class="icon fa-brands fa-instagram sd appear" data-s-88de538a-d2d2-4a52-a06e-40a229852f72="" href="https://www.instagram.com/allyouneed2020/?hl=ja" rel="noopener" target="_blank"></a><a class="icon fa-brands fa-square-facebook sd appear" data-s-73b51265-5e29-492d-857f-6dd0236c6fe0="" href="https://www.facebook.com/AllyouneedShimoda" rel="noopener" target="_blank"></a></div></div></div></div><div class="product-font-style" data-v-51f515bd=""><style data-v-51f515bd=""></style></div><style>:root {--s-font-bd94a845: Lato;--s-font-3a8e7aa2: 'こぶりなゴシック W3 JIS2004';--s-font-d39b4375: 'こぶりなゴシック W6 JIS2004';--s-font-7d8084a9: Poppins;--s-font-0d83b48e: 'こぶりなゴシック W3 JIS2004';}</style></div></div></div><!-- --><a data-v-60d33773="" href="https://studio.design/?utm_source=/home-english&amp;utm_medium=STUDIO%20Banner&amp;utm_campaign=STUDIO%20Banner" style="display:flex !important;background-color:#000000;--01abf230:#FFFFFF;" target="_blank"><svg aria-label="Created in Studio.Design, Start your site in Studio.Design" class="custom-fill" data-v-60d33773="" fill="none" height="11" role="img" width="75" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M72.56 3.528a1.2 1.2 0 0 0-.48-.678q-.456-.327-1.187-.327-.514 0-.882.156a1.3 1.3 0 0 0-.562.426 1.04 1.04 0 0 0-.2.613q0 .286.129.497.132.21.359.358.*************.275.098.554.165l.851.215q.514.12.988.327.479.205.856.52a2.3 2.3 0 0 1 .602.756q.222.442.222 1.038 0 .805-.408 1.42-.408.608-1.179.953-.766.34-1.856.34-1.06 0-1.84-.332a2.74 2.74 0 0 1-1.213-.966q-.435-.636-.47-1.55h1.617q.035.48.293.798.257.317.669.474.416.157.93.157.536 0 .94-.161.407-.165.638-.457a1.1 1.1 0 0 0 .235-.69.88.88 0 0 0-.209-.59 1.5 1.5 0 0 0-.571-.394 5.5 5.5 0 0 0-.851-.287l-1.032-.268Q68.88 5.992 68.23 5.4q-.647-.596-.647-1.58 0-.81.434-1.42.44-.608 1.192-.944.753-.34 1.706-.34.967 0 1.693.34.73.336 1.148.936.352.504.416 1.135zM67.344 2.268l-8.153 8.236L58.02 9.32l8.154-8.236z" fill="#F7F7F7" fill-rule="evenodd"></path><path d="M50.318 6.353v4.06H48.7V3.484h1.545v1.177h.08q.237-.582.755-.925.522-.342 1.29-.342.71 0 1.237.306.531.307.822.889.294.582.29 1.411v4.411h-1.616V6.254q0-.694-.358-1.087-.352-.393-.977-.392a1.5 1.5 0 0 0-.755.19 1.3 1.3 0 0 0-.514.536q-.182.352-.182.852M46.176 10.414V3.486h1.616v6.928zm.812-7.911a.94.94 0 0 1-.66-.257.83.83 0 0 1-.277-.627q0-.37.277-.627a.93.93 0 0 1 .66-.262q.39 0 .661.262.277.257.277.627a.83.83 0 0 1-.277.627.93.93 0 0 1-.66.257M39.192 10.535q-.809 0-1.447-.42-.639-.42-1.01-1.218-.37-.798-.37-1.939 0-1.156.375-1.948.38-.799 1.023-1.205.642-.41 1.433-.41.603 0 .991.207.39.204.616.492.228.284.353.537h.067V1.176h1.62v9.237h-1.589V9.32h-.098a2.8 2.8 0 0 1-.362.537 2 2 0 0 1-.625.478q-.389.2-.977.199m.45-1.34q.514 0 .876-.28.36-.284.549-.789.187-.505.187-1.177t-.187-1.168a1.66 1.66 0 0 0-.545-.771q-.357-.276-.88-.276-.54 0-.901.285a1.7 1.7 0 0 0-.545.784 3.3 3.3 0 0 0-.183 1.146q0 .65.183 1.16.188.504.549.797.366.29.898.29M32.983 10.548q-1.03 0-1.781-.433a2.9 2.9 0 0 1-1.148-1.236q-.402-.803-.402-1.89 0-1.068.402-1.876.407-.812 1.134-1.263.728-.456 1.71-.455.634 0 1.197.207a2.7 2.7 0 0 1 1 .631q.438.429.688 1.092.25.658.25 1.57v.5H30.41v-1.1h4.072a1.8 1.8 0 0 0-.2-.835 1.47 1.47 0 0 0-.55-.582 1.53 1.53 0 0 0-.812-.212q-.496 0-.871.244-.375.24-.585.631a1.8 1.8 0 0 0-.21.853v.96q0 .605.219 1.038.219.429.612.658.392.226.92.226.352 0 .638-.1.285-.103.496-.301a1.26 1.26 0 0 0 .317-.492l1.509.171a2.4 2.4 0 0 1-.545 1.056q-.397.446-1.018.694-.62.244-1.42.244M29.425 3.488V4.75h-3.943V3.488zm-2.97-1.66h1.617v6.504q0 .33.098.505a.53.53 0 0 0 .268.235q.165.063.366.063.153 0 .277-.023a2 2 0 0 0 .197-.04l.272 1.276a4 4 0 0 1-.37.1 3 3 0 0 1-.581.062q-.608.018-1.094-.184a1.7 1.7 0 0 1-.772-.641q-.282-.434-.277-1.082zM21.506 10.552q-.652 0-1.174-.234a1.9 1.9 0 0 1-.822-.704q-.3-.465-.3-1.145 0-.587.215-.97.215-.384.585-.614t.835-.347q.47-.122.969-.176.603-.063.978-.112.375-.054.544-.163.175-.112.175-.347v-.027q0-.51-.3-.79-.299-.28-.861-.28-.594 0-.942.262-.344.262-.465.618l-1.509-.216a2.44 2.44 0 0 1 .59-1.056 2.56 2.56 0 0 1 1.004-.64 3.8 3.8 0 0 1 1.313-.216q.496 0 .987.117.49.117.897.388.406.266.652.726.25.46.25 1.15v4.637h-1.554V9.46h-.053a2 2 0 0 1-.416.541q-.263.248-.665.401-.398.15-.933.15m.42-1.2q.486 0 .844-.193.356-.199.549-.524.195-.324.196-.708v-.816a.8.8 0 0 1-.259.117 4 4 0 0 1-.402.095q-.223.04-.442.072t-.38.054a2.8 2.8 0 0 0-.647.163q-.285.112-.45.315a.78.78 0 0 0-.166.515q0 .45.326.68t.83.23M15.872 10.548q-1.032 0-1.782-.433a2.9 2.9 0 0 1-1.147-1.236q-.402-.803-.402-1.89 0-1.068.402-1.876.406-.812 1.134-1.263.727-.456 1.71-.455.635 0 1.197.207.567.203 1 .631.436.429.687 1.092.25.658.25 1.57v.5h-5.62v-1.1h4.071a1.8 1.8 0 0 0-.2-.835 1.47 1.47 0 0 0-.55-.582 1.53 1.53 0 0 0-.813-.212q-.495 0-.87.244-.375.24-.585.631a1.8 1.8 0 0 0-.21.853v.96q0 .605.219 1.038.22.429.611.658.393.226.92.226.353 0 .639-.1.285-.103.495-.301a1.26 1.26 0 0 0 .317-.492l1.51.171a2.4 2.4 0 0 1-.545 1.056q-.398.446-1.018.694-.62.244-1.42.244M8.887 10.414V3.486h1.567v1.155h.071q.188-.6.643-.925.46-.33 1.05-.33.134 0 .299.014.17.01.281.032v1.502a1.7 1.7 0 0 0-.326-.063 3 3 0 0 0-.424-.032q-.442 0-.795.194a1.4 1.4 0 0 0-.55.528 1.5 1.5 0 0 0-.2.78v4.073zM8.196 4.294h-1.67a2.1 2.1 0 0 0-.263-.735 2 2 0 0 0-.478-.55 2 2 0 0 0-.652-.339 2.5 2.5 0 0 0-.781-.117 2.35 2.35 0 0 0-1.322.379q-.576.374-.902 1.1-.326.721-.326 1.764 0 1.06.326 1.786.33.721.902 1.091.576.366 1.317.366.411 0 .768-.109.361-.113.648-.329.29-.216.486-.532.2-.315.277-.722l1.67.01a3.7 3.7 0 0 1-.406 1.235 3.6 3.6 0 0 1-.809 1.02q-.5.437-1.17.685a4.3 4.3 0 0 1-1.486.243q-1.206 0-2.152-.563Q1.227 9.413.68 8.348.137 7.284.137 5.796q0-1.494.549-2.553.55-1.065 1.496-1.628.946-.564 2.143-.564.763 0 1.42.216.656.218 1.17.636.513.415.843 1.02.336.6.438 1.37" fill="#F7F7F7"></path></svg></a></div></div><script data-ssr="true" id="__NUXT_DATA__" type="application/json">[["Reactive",1],{"data":2,"state":4,"_errors":5,"serverRendered":6,"path":7,"pinia":8},{"dynamicDatahome-english":3},null,{},{"dynamicDatahome-english":3},true,"/home-english",{"cmsContentStore":9,"indexStore":12,"projectStore":15,"productStore":27,"pageHeadStore":221,"badgeColorStore":223},{"listContentsMap":10,"contentMap":11},["Map"],["Map"],{"routeType":13,"host":14},"publish","allyouneed.studio.site",{"project":16},{"id":17,"name":18,"type":19,"customDomain":20,"iconImage":20,"coverImage":3,"displayBadge":6,"integrations":21,"snapshot_path":25,"snapshot_id":26,"recaptchaSiteKey":-1},"BRO376opOD","ALL YOU NEED","web","",[22],{"integration_name":23,"code":24},"typesquare","unused","https://storage.googleapis.com/studio-publish/projects/BRO376opOD/dO8K4o90Wn/","dO8K4o90Wn",{"product":28,"isLoaded":6,"selectedModalIds":218,"redirectPage":3,"isInitializedRSS":79,"pageViewMap":219,"symbolViewMap":220},{"breakPoints":29,"colors":36,"fonts":37,"head":72,"info":74,"pages":82,"resources":191,"symbols":195,"style":199,"styleVars":201,"enablePassword":79,"classes":215,"publishedUid":-1},[30,33],{"maxWidth":31,"name":32},540,"mobile",{"maxWidth":34,"name":35},840,"tablet",[],[38,55,57,59],{"family":39,"subsets":40,"variants":43,"vendor":54},"Lato",[41,42],"latin","latin-ext",[44,45,46,47,48,49,50,51,52,53],"100","100italic","300","300italic","regular","italic","700","700italic","900","900italic","google",{"family":56,"vendor":23},"こぶりなゴシック W3 JIS2004",{"family":58,"vendor":23},"こぶりなゴシック W6 JIS2004",{"family":60,"subsets":61,"variants":63,"vendor":54},"Poppins",[62,41,42],"devanagari",[44,45,64,65,46,47,48,49,66,67,68,69,50,51,70,71,52,53],"200","200italic","500","500italic","600","600italic","800","800italic",{"favicon":20,"meta":73,"title":20},{"description":20,"og:image":20},{"baseWidth":75,"created_at":76,"screen":77,"type":19,"updated_at":80,"version":81},1280,1518591100346,{"baseWidth":75,"height":78,"isAutoHeight":79,"width":75,"workingState":79},600,false,1518792996878,"4.1.3",[83,98,109,119,130,144,155,165,175,180,184],{"head":84,"id":91,"position":92,"type":94,"uuid":95,"symbolIds":96},{"favicon":85,"lang":86,"meta":87,"title":90},"https://storage.googleapis.com/production-os-assets/assets/f077df9b-7937-440f-8822-107bd98d1d0e","ja",{"description":88,"og:image":89},"伊豆下田に海、自然、旅などが好きな人たちが 集まる場所をつくりたい！ その夢を叶えるため、改装し遂に完成しました！全室から海が見える客室完備！","https://storage.googleapis.com/production-os-assets/assets/53e87b36-d11f-411e-9da0-c0993bf0211d","All you need | 下田","home",{"x":93,"y":93},0,"page","bf9fed39-ac7e-4c42-aa12-40d655a39612",[97],"dd7ba2a2-e1e9-4c45-93b0-064c1bba8952",{"head":99,"id":105,"position":106,"type":94,"uuid":107,"symbolIds":108},{"favicon":100,"lang":86,"meta":101,"title":104},"https://storage.googleapis.com/production-os-assets/assets/f5e14f3b-ddbc-4d56-8727-eff338c454e2",{"description":102,"og:image":103},"All you needでは 様々なレンタルを行っております","https://storage.googleapis.com/production-os-assets/assets/571cdd53-24c2-46eb-8a1b-928781a4de94","All you need | レンタル","rental",{"x":93,"y":93},"c4c1e418-0ab2-42bc-bc3c-20633ec643ec",[97],{"head":110,"id":115,"position":116,"type":94,"uuid":117,"symbolIds":118},{"favicon":111,"lang":86,"meta":112,"title":114},"https://storage.googleapis.com/production-os-assets/assets/b3aa9f68-ae7e-4d30-acd1-25fea871d3d5",{"description":113,"og:image":89},"私たちの出会い そして ゲストハウス完成までのストーリー","All you need | わたしたち","about",{"x":93,"y":93},"fbde7539-d3fd-44ed-ab54-f38c845c3c5b",[97],{"head":120,"id":126,"position":127,"type":94,"uuid":128,"symbolIds":129},{"favicon":121,"lang":86,"meta":122,"title":125},"https://storage.googleapis.com/production-os-assets/assets/3a7a1cc6-299a-4e66-995c-090028b4382e",{"description":123,"og:image":124},"当宿では快適な空間をお過ごしいただけるように全９部屋ご用意しております。 また全室海の見えるお部屋となっております。","https://storage.googleapis.com/production-os-assets/assets/d1272038-cf58-49a5-847d-61ad3fda434b","All you need | 部屋と施設装備","room",{"x":93,"y":93},"b029143f-0988-4090-b196-ddf409126cb9",[97],{"head":131,"id":138,"position":139,"type":94,"uuid":140,"symbolIds":141},{"favicon":132,"lang":133,"meta":134,"title":137},"https://storage.googleapis.com/production-os-assets/assets/78f8ae36-821b-4b19-811c-b1edbb4c1b0f","en",{"description":135,"og:image":136},"We want to create a place where people who love the sea, nature and travel can come together here in Izu Shimoda! To make that dream come true, we renovated and finally completed it!","https://storage.googleapis.com/production-os-assets/assets/813c5d80-f312-4656-999b-d3cd0c6e6c24","All you need | shimoda","home-english",{"x":93,"y":93},"11154574-7fa8-4bb4-82b4-fa5893570fce",[142,143],"6666b517-fe35-4c20-8ab8-a2a79e5e6880","8fb01ab9-c923-4407-8952-28c962ad5757",{"head":145,"id":151,"position":152,"type":94,"uuid":153,"symbolIds":154},{"favicon":146,"lang":133,"meta":147,"title":150},"https://storage.googleapis.com/production-os-assets/assets/455fd950-cd90-4430-bfed-8d2bdc20026e",{"description":148,"og:image":149},"We have a total of 9 rooms for you to enjoy acomfortable atmosphere. All rooms have an ocean view.","https://storage.googleapis.com/production-os-assets/assets/3ec7ee27-a214-4247-815a-0c1592f64f46","All you need | Room&Facility","room-english",{"x":93,"y":93},"fe5482b7-89a9-4357-ae4e-a2ac11d23fde",[142,143],{"head":156,"id":161,"position":162,"type":94,"uuid":163,"symbolIds":164},{"favicon":157,"lang":133,"meta":158,"title":160},"https://storage.googleapis.com/production-os-assets/assets/85b5943a-c4b3-456c-ae72-f459925975ac",{"description":159,"og:image":89},"Our story From our first encounter until the completion of All You Need Guest House","All you need | Aboutus","about-english",{"x":93,"y":93},"403fcf77-e30a-44f9-ab64-86c59aaa475f",[142,143],{"head":166,"id":171,"position":172,"type":94,"uuid":173,"symbolIds":174},{"favicon":167,"lang":133,"meta":168,"title":170},"https://storage.googleapis.com/production-os-assets/assets/1b8bba8a-4e04-4034-b67c-a72ec9f86839",{"description":169,"og:image":89},"At All you need, we offer a variety of rental services.","All you need | Rental","rental-english",{"x":93,"y":93},"e9bd277f-09e8-46e0-9d5f-************",[142,143],{"id":176,"isCookieModal":79,"responsive":6,"type":177,"uuid":178,"symbolIds":179},"menu","modal","403c9414-b48c-47ff-adc3-ac107bfb5461",[],{"id":181,"isCookieModal":79,"responsive":6,"type":177,"uuid":182,"symbolIds":183},"menu-1","7e1886c7-5351-409c-8240-3b8f4923e2e0",[],{"cmsRequest":185,"id":188,"type":94,"uuid":189,"symbolIds":190},{"contentSlug":186,"schemaKey":187},"{{$route.params.slug}}","bLPr0_Wf","posts/:slug","6ed59452-31f3-41a3-85c7-4640431d2381",[97],{"rssList":192,"apiList":193,"cmsProjectId":194},[],[],"KU4r0EvC6tYICjPUas0e",[196,197,198],{"uuid":97},{"uuid":142},{"uuid":143},{"fontFamily":200},[],{"fontFamily":202},[203,205,208,211,213],{"key":204,"name":20,"value":39},"bd94a845",{"key":206,"name":20,"value":207},"3a8e7aa2","'こぶりなゴシック W3 JIS2004'",{"key":209,"name":20,"value":210},"d39b4375","'こぶりなゴシック W6 JIS2004'",{"key":212,"name":20,"value":60},"7d8084a9",{"key":214,"name":20,"value":207},"0d83b48e",{"typography":216,"motion":217},[],[],[],{},["Map"],{"googleFontMap":222,"typesquareLoaded":79,"hasCustomFont":79},["Map"],{"_color":224,"contrastColor":225,"textColor":224},"#FFFFFF","#000000"]</script><script>window.__NUXT__={};window.__NUXT__.config={public:{apiBaseUrl:"https://api.studiodesignapp.com/api",cmsApiBaseUrl:"https://api.cms.studiodesignapp.com",previewBaseUrl:"https://preview.studio.site",facebookAppId:"569471266584583",firebaseApiKey:"AIzaSyBkjSUz89vvvl35U-EErvfHXLhsDakoNNg",firebaseProjectId:"studio-7e371",firebaseAuthDomain:"studio-7e371.firebaseapp.com",firebaseDatabaseURL:"https://studio-7e371.firebaseio.com",firebaseStorageBucket:"studio-7e371.appspot.com",firebaseMessagingSenderId:"373326844567",firebaseAppId:"1:389988806345:web:db757f2db74be8b3",studioDomain:".studio.site",studioPublishUrl:"https://storage.googleapis.com/studio-publish",studioPublishIndexUrl:"https://storage.googleapis.com/studio-publish-index",rssApiPath:"https://rss.studiodesignapp.com/rssConverter",embedSandboxDomain:".studioiframesandbox.com",apiProxyUrl:"https://api.proxy.studiodesignapp.com/publish",isPublishSite:true,isDev:false},app:{baseURL:"/",buildAssetsDir:"/_nuxt/",cdnURL:""}}</script><script crossorigin="" src="/_nuxt/entry.5e63065d.js" type="module"></script>
</body></html>